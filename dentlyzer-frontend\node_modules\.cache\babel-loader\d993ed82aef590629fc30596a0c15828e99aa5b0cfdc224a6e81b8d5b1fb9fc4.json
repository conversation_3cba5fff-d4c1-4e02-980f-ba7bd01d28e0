{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\supervisor\\\\ReviewStepsDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaCheck, FaTimes } from 'react-icons/fa';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745',\n  danger: '#dc2626'\n};\nconst ReviewStepsDisplay = ({\n  reviewSteps,\n  procedureType,\n  onStepStatusChange,\n  isSupervisorMode = false,\n  stepStatuses = {}\n}) => {\n  _s();\n  const [localStepStatuses, setLocalStepStatuses] = useState(stepStatuses);\n\n  // Update local state when prop changes\n  React.useEffect(() => {\n    setLocalStepStatuses(stepStatuses);\n  }, [stepStatuses]);\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 rounded-lg text-center\",\n      style: {\n        backgroundColor: '#f8f9fa'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: colorPalette.text\n        },\n        children: \"No review steps available for this procedure.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Count steps by status\n  const completedSteps = reviewSteps.filter(step => step.completed).length;\n  const totalSteps = reviewSteps.length;\n  const acceptedSteps = Object.values(localStepStatuses).filter(status => status === 'accepted').length;\n  const declinedSteps = Object.values(localStepStatuses).filter(status => status === 'declined').length;\n  const pendingSteps = completedSteps - acceptedSteps - declinedSteps;\n  const handleStepAction = (index, action) => {\n    const newStatuses = {\n      ...localStepStatuses\n    };\n    newStatuses[index] = action;\n    setLocalStepStatuses(newStatuses);\n    if (onStepStatusChange) {\n      onStepStatusChange(index, action);\n    }\n  };\n  const getStepStatus = index => {\n    return localStepStatuses[index] || 'pending';\n  };\n  const getStepBackgroundColor = (step, index) => {\n    if (!step.completed) return colorPalette.background;\n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return `${colorPalette.accent}10`;\n      case 'declined':\n        return `${colorPalette.danger}10`;\n      default:\n        return `${colorPalette.primary}10`;\n    }\n  };\n  const getStepIcon = (step, index) => {\n    if (!step.completed) {\n      return /*#__PURE__*/_jsxDEV(FaCircle, {\n        className: \"h-5 w-5 text-gray-300\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 14\n      }, this);\n    }\n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"h-5 w-5\",\n          style: {\n            color: colorPalette.accent\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 16\n        }, this);\n      case 'declined':\n        return /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n          className: \"h-5 w-5\",\n          style: {\n            color: colorPalette.danger\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaCircle, {\n          className: \"h-5 w-5\",\n          style: {\n            color: colorPalette.primary\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStepTextColor = (step, index) => {\n    if (!step.completed) return '#666666';\n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return colorPalette.accent;\n      case 'declined':\n        return colorPalette.danger;\n      default:\n        return colorPalette.primary;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-lg overflow-hidden\",\n    style: {\n      backgroundColor: colorPalette.background,\n      border: `1px solid #e5e7eb`\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b\",\n      style: {\n        backgroundColor: `${colorPalette.primary}10`,\n        borderColor: '#e5e7eb'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-md font-semibold\",\n          style: {\n            color: colorPalette.primary\n          },\n          children: [procedureType, \" Review Steps\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [isSupervisorMode && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"h-3 w-3 mr-1\",\n                style: {\n                  color: colorPalette.accent\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this), acceptedSteps, \" Accepted\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                className: \"h-3 w-3 mr-1\",\n                style: {\n                  color: colorPalette.danger\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this), declinedSteps, \" Declined\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCircle, {\n                className: \"h-3 w-3 mr-1\",\n                style: {\n                  color: colorPalette.primary\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this), pendingSteps, \" Pending\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-32 rounded-full h-2.5 mr-2\",\n              style: {\n                backgroundColor: '#e5e7eb'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-2.5 rounded-full\",\n                style: {\n                  width: `${completedSteps / totalSteps * 100}%`,\n                  backgroundColor: colorPalette.accent\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              style: {\n                color: colorPalette.text\n              },\n              children: [completedSteps, \"/\", totalSteps]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y\",\n      style: {\n        borderColor: '#e5e7eb'\n      },\n      children: reviewSteps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 5\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.05\n        },\n        className: \"p-3 flex items-center justify-between\",\n        style: {\n          backgroundColor: getStepBackgroundColor(step, index)\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 mt-0.5 mr-3\",\n            children: getStepIcon(step, index)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              style: {\n                fontWeight: step.completed ? '500' : 'normal',\n                color: getStepTextColor(step, index)\n              },\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), isSupervisorMode && step.completed && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 ml-4\",\n          children: [getStepStatus(index) === 'pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleStepAction(index, 'accepted'),\n              className: \"px-3 py-1 rounded-full text-xs font-medium flex items-center\",\n              style: {\n                backgroundColor: colorPalette.accent,\n                color: 'white'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 23\n              }, this), \"Accept\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleStepAction(index, 'declined'),\n              className: \"px-3 py-1 rounded-full text-xs font-medium flex items-center\",\n              style: {\n                backgroundColor: colorPalette.danger,\n                color: 'white'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 23\n              }, this), \"Decline\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true), getStepStatus(index) === 'accepted' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\",\n            style: {\n              backgroundColor: `${colorPalette.accent}20`,\n              color: colorPalette.accent\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 21\n            }, this), \"Accepted\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 19\n          }, this), getStepStatus(index) === 'declined' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\",\n            style: {\n              backgroundColor: `${colorPalette.danger}20`,\n              color: colorPalette.danger\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 21\n            }, this), \"Declined\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewStepsDisplay, \"JJq/ha6Jsk9y1+TIxr8Fsike1pQ=\");\n_c = ReviewStepsDisplay;\nexport default ReviewStepsDisplay;\nvar _c;\n$RefreshReg$(_c, \"ReviewStepsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaCheckCircle", "FaTimesCircle", "FaCircle", "FaCheck", "FaTimes", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "colorPalette", "primary", "secondary", "background", "text", "accent", "danger", "ReviewStepsDisplay", "reviewSteps", "procedureType", "onStepStatusChange", "isSupervisorMode", "stepStatuses", "_s", "localStepStatuses", "setLocalStepStatuses", "useEffect", "length", "className", "style", "backgroundColor", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "completedSteps", "filter", "step", "completed", "totalSteps", "acceptedSteps", "Object", "values", "status", "declinedSteps", "pendingSteps", "handleStepAction", "index", "action", "newStatuses", "getStepStatus", "getStepBackgroundColor", "getStepIcon", "getStepTextColor", "border", "borderColor", "width", "map", "div", "initial", "opacity", "y", "animate", "transition", "delay", "fontWeight", "description", "button", "whileHover", "scale", "whileTap", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Co<PERSON>/dentlyzer-frontend/src/supervisor/ReviewStepsDisplay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaCheck, FaTimes } from 'react-icons/fa';\n\n// Website color palette\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745',\n  danger: '#dc2626'\n};\n\nconst ReviewStepsDisplay = ({ reviewSteps, procedureType, onStepStatusChange, isSupervisorMode = false, stepStatuses = {} }) => {\n  const [localStepStatuses, setLocalStepStatuses] = useState(stepStatuses);\n\n  // Update local state when prop changes\n  React.useEffect(() => {\n    setLocalStepStatuses(stepStatuses);\n  }, [stepStatuses]);\n\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return (\n      <div className=\"p-4 rounded-lg text-center\" style={{ backgroundColor: '#f8f9fa' }}>\n        <p style={{ color: colorPalette.text }}>No review steps available for this procedure.</p>\n      </div>\n    );\n  }\n\n  // Count steps by status\n  const completedSteps = reviewSteps.filter(step => step.completed).length;\n  const totalSteps = reviewSteps.length;\n  const acceptedSteps = Object.values(localStepStatuses).filter(status => status === 'accepted').length;\n  const declinedSteps = Object.values(localStepStatuses).filter(status => status === 'declined').length;\n  const pendingSteps = completedSteps - acceptedSteps - declinedSteps;\n\n  const handleStepAction = (index, action) => {\n    const newStatuses = { ...localStepStatuses };\n    newStatuses[index] = action;\n    setLocalStepStatuses(newStatuses);\n    \n    if (onStepStatusChange) {\n      onStepStatusChange(index, action);\n    }\n  };\n\n  const getStepStatus = (index) => {\n    return localStepStatuses[index] || 'pending';\n  };\n\n  const getStepBackgroundColor = (step, index) => {\n    if (!step.completed) return colorPalette.background;\n    \n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return `${colorPalette.accent}10`;\n      case 'declined':\n        return `${colorPalette.danger}10`;\n      default:\n        return `${colorPalette.primary}10`;\n    }\n  };\n\n  const getStepIcon = (step, index) => {\n    if (!step.completed) {\n      return <FaCircle className=\"h-5 w-5 text-gray-300\" />;\n    }\n    \n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return <FaCheckCircle className=\"h-5 w-5\" style={{ color: colorPalette.accent }} />;\n      case 'declined':\n        return <FaTimesCircle className=\"h-5 w-5\" style={{ color: colorPalette.danger }} />;\n      default:\n        return <FaCircle className=\"h-5 w-5\" style={{ color: colorPalette.primary }} />;\n    }\n  };\n\n  const getStepTextColor = (step, index) => {\n    if (!step.completed) return '#666666';\n    \n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return colorPalette.accent;\n      case 'declined':\n        return colorPalette.danger;\n      default:\n        return colorPalette.primary;\n    }\n  };\n\n  return (\n    <div className=\"rounded-lg overflow-hidden\" style={{ backgroundColor: colorPalette.background, border: `1px solid #e5e7eb` }}>\n      <div className=\"p-4 border-b\" style={{ backgroundColor: `${colorPalette.primary}10`, borderColor: '#e5e7eb' }}>\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-md font-semibold\" style={{ color: colorPalette.primary }}>\n            {procedureType} Review Steps\n          </h3>\n          <div className=\"flex items-center space-x-4\">\n            {isSupervisorMode && (\n              <div className=\"flex items-center space-x-2 text-xs\">\n                <span className=\"flex items-center\">\n                  <FaCheckCircle className=\"h-3 w-3 mr-1\" style={{ color: colorPalette.accent }} />\n                  {acceptedSteps} Accepted\n                </span>\n                <span className=\"flex items-center\">\n                  <FaTimesCircle className=\"h-3 w-3 mr-1\" style={{ color: colorPalette.danger }} />\n                  {declinedSteps} Declined\n                </span>\n                <span className=\"flex items-center\">\n                  <FaCircle className=\"h-3 w-3 mr-1\" style={{ color: colorPalette.primary }} />\n                  {pendingSteps} Pending\n                </span>\n              </div>\n            )}\n            <div className=\"flex items-center\">\n              <div className=\"w-32 rounded-full h-2.5 mr-2\" style={{ backgroundColor: '#e5e7eb' }}>\n                <div\n                  className=\"h-2.5 rounded-full\"\n                  style={{\n                    width: `${(completedSteps / totalSteps) * 100}%`,\n                    backgroundColor: colorPalette.accent\n                  }}\n                ></div>\n              </div>\n              <span className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>\n                {completedSteps}/{totalSteps}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"divide-y\" style={{ borderColor: '#e5e7eb' }}>\n        {reviewSteps.map((step, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 5 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.05 }}\n            className=\"p-3 flex items-center justify-between\"\n            style={{\n              backgroundColor: getStepBackgroundColor(step, index)\n            }}\n          >\n            <div className=\"flex items-center flex-1\">\n              <div className=\"flex-shrink-0 mt-0.5 mr-3\">\n                {getStepIcon(step, index)}\n              </div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm\" style={{\n                  fontWeight: step.completed ? '500' : 'normal',\n                  color: getStepTextColor(step, index)\n                }}>\n                  {step.description}\n                </p>\n              </div>\n            </div>\n            \n            {isSupervisorMode && step.completed && (\n              <div className=\"flex items-center space-x-2 ml-4\">\n                {getStepStatus(index) === 'pending' && (\n                  <>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleStepAction(index, 'accepted')}\n                      className=\"px-3 py-1 rounded-full text-xs font-medium flex items-center\"\n                      style={{\n                        backgroundColor: colorPalette.accent,\n                        color: 'white'\n                      }}\n                    >\n                      <FaCheck className=\"h-3 w-3 mr-1\" />\n                      Accept\n                    </motion.button>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleStepAction(index, 'declined')}\n                      className=\"px-3 py-1 rounded-full text-xs font-medium flex items-center\"\n                      style={{\n                        backgroundColor: colorPalette.danger,\n                        color: 'white'\n                      }}\n                    >\n                      <FaTimes className=\"h-3 w-3 mr-1\" />\n                      Decline\n                    </motion.button>\n                  </>\n                )}\n                {getStepStatus(index) === 'accepted' && (\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    style={{\n                      backgroundColor: `${colorPalette.accent}20`,\n                      color: colorPalette.accent\n                    }}>\n                    <FaCheckCircle className=\"h-3 w-3 mr-1\" />\n                    Accepted\n                  </span>\n                )}\n                {getStepStatus(index) === 'declined' && (\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    style={{\n                      backgroundColor: `${colorPalette.danger}20`,\n                      color: colorPalette.danger\n                    }}>\n                    <FaTimesCircle className=\"h-3 w-3 mr-1\" />\n                    Declined\n                  </span>\n                )}\n              </div>\n            )}\n          </motion.div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewStepsDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;;AAEzF;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,aAAa;EAAEC,kBAAkB;EAAEC,gBAAgB,GAAG,KAAK;EAAEC,YAAY,GAAG,CAAC;AAAE,CAAC,KAAK;EAAAC,EAAA;EAC9H,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAACuB,YAAY,CAAC;;EAExE;EACAxB,KAAK,CAAC4B,SAAS,CAAC,MAAM;IACpBD,oBAAoB,CAACH,YAAY,CAAC;EACpC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,IAAI,CAACJ,WAAW,IAAIA,WAAW,CAACS,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACEpB,OAAA;MAAKqB,SAAS,EAAC,4BAA4B;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE;MAAU,CAAE;MAAAC,QAAA,eAChFxB,OAAA;QAAGsB,KAAK,EAAE;UAAEG,KAAK,EAAEtB,YAAY,CAACI;QAAK,CAAE;QAAAiB,QAAA,EAAC;MAA6C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;;EAEA;EACA,MAAMC,cAAc,GAAGnB,WAAW,CAACoB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACb,MAAM;EACxE,MAAMc,UAAU,GAAGvB,WAAW,CAACS,MAAM;EACrC,MAAMe,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACpB,iBAAiB,CAAC,CAACc,MAAM,CAACO,MAAM,IAAIA,MAAM,KAAK,UAAU,CAAC,CAAClB,MAAM;EACrG,MAAMmB,aAAa,GAAGH,MAAM,CAACC,MAAM,CAACpB,iBAAiB,CAAC,CAACc,MAAM,CAACO,MAAM,IAAIA,MAAM,KAAK,UAAU,CAAC,CAAClB,MAAM;EACrG,MAAMoB,YAAY,GAAGV,cAAc,GAAGK,aAAa,GAAGI,aAAa;EAEnE,MAAME,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;IAC1C,MAAMC,WAAW,GAAG;MAAE,GAAG3B;IAAkB,CAAC;IAC5C2B,WAAW,CAACF,KAAK,CAAC,GAAGC,MAAM;IAC3BzB,oBAAoB,CAAC0B,WAAW,CAAC;IAEjC,IAAI/B,kBAAkB,EAAE;MACtBA,kBAAkB,CAAC6B,KAAK,EAAEC,MAAM,CAAC;IACnC;EACF,CAAC;EAED,MAAME,aAAa,GAAIH,KAAK,IAAK;IAC/B,OAAOzB,iBAAiB,CAACyB,KAAK,CAAC,IAAI,SAAS;EAC9C,CAAC;EAED,MAAMI,sBAAsB,GAAGA,CAACd,IAAI,EAAEU,KAAK,KAAK;IAC9C,IAAI,CAACV,IAAI,CAACC,SAAS,EAAE,OAAO9B,YAAY,CAACG,UAAU;IAEnD,MAAMgC,MAAM,GAAGO,aAAa,CAACH,KAAK,CAAC;IACnC,QAAQJ,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,GAAGnC,YAAY,CAACK,MAAM,IAAI;MACnC,KAAK,UAAU;QACb,OAAO,GAAGL,YAAY,CAACM,MAAM,IAAI;MACnC;QACE,OAAO,GAAGN,YAAY,CAACC,OAAO,IAAI;IACtC;EACF,CAAC;EAED,MAAM2C,WAAW,GAAGA,CAACf,IAAI,EAAEU,KAAK,KAAK;IACnC,IAAI,CAACV,IAAI,CAACC,SAAS,EAAE;MACnB,oBAAOjC,OAAA,CAACJ,QAAQ;QAACyB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACvD;IAEA,MAAMS,MAAM,GAAGO,aAAa,CAACH,KAAK,CAAC;IACnC,QAAQJ,MAAM;MACZ,KAAK,UAAU;QACb,oBAAOtC,OAAA,CAACN,aAAa;UAAC2B,SAAS,EAAC,SAAS;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEtB,YAAY,CAACK;UAAO;QAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrF,KAAK,UAAU;QACb,oBAAO7B,OAAA,CAACL,aAAa;UAAC0B,SAAS,EAAC,SAAS;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEtB,YAAY,CAACM;UAAO;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrF;QACE,oBAAO7B,OAAA,CAACJ,QAAQ;UAACyB,SAAS,EAAC,SAAS;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEtB,YAAY,CAACC;UAAQ;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACnF;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAChB,IAAI,EAAEU,KAAK,KAAK;IACxC,IAAI,CAACV,IAAI,CAACC,SAAS,EAAE,OAAO,SAAS;IAErC,MAAMK,MAAM,GAAGO,aAAa,CAACH,KAAK,CAAC;IACnC,QAAQJ,MAAM;MACZ,KAAK,UAAU;QACb,OAAOnC,YAAY,CAACK,MAAM;MAC5B,KAAK,UAAU;QACb,OAAOL,YAAY,CAACM,MAAM;MAC5B;QACE,OAAON,YAAY,CAACC,OAAO;IAC/B;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKqB,SAAS,EAAC,4BAA4B;IAACC,KAAK,EAAE;MAAEC,eAAe,EAAEpB,YAAY,CAACG,UAAU;MAAE2C,MAAM,EAAE;IAAoB,CAAE;IAAAzB,QAAA,gBAC3HxB,OAAA;MAAKqB,SAAS,EAAC,cAAc;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE,GAAGpB,YAAY,CAACC,OAAO,IAAI;QAAE8C,WAAW,EAAE;MAAU,CAAE;MAAA1B,QAAA,eAC5GxB,OAAA;QAAKqB,SAAS,EAAC,mCAAmC;QAAAG,QAAA,gBAChDxB,OAAA;UAAIqB,SAAS,EAAC,uBAAuB;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEtB,YAAY,CAACC;UAAQ,CAAE;UAAAoB,QAAA,GAC1EZ,aAAa,EAAC,eACjB;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7B,OAAA;UAAKqB,SAAS,EAAC,6BAA6B;UAAAG,QAAA,GACzCV,gBAAgB,iBACfd,OAAA;YAAKqB,SAAS,EAAC,qCAAqC;YAAAG,QAAA,gBAClDxB,OAAA;cAAMqB,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBACjCxB,OAAA,CAACN,aAAa;gBAAC2B,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAEtB,YAAY,CAACK;gBAAO;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChFM,aAAa,EAAC,WACjB;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP7B,OAAA;cAAMqB,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBACjCxB,OAAA,CAACL,aAAa;gBAAC0B,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAEtB,YAAY,CAACM;gBAAO;cAAE;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChFU,aAAa,EAAC,WACjB;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP7B,OAAA;cAAMqB,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBACjCxB,OAAA,CAACJ,QAAQ;gBAACyB,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAEtB,YAAY,CAACC;gBAAQ;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5EW,YAAY,EAAC,UAChB;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eACD7B,OAAA;YAAKqB,SAAS,EAAC,mBAAmB;YAAAG,QAAA,gBAChCxB,OAAA;cAAKqB,SAAS,EAAC,8BAA8B;cAACC,KAAK,EAAE;gBAAEC,eAAe,EAAE;cAAU,CAAE;cAAAC,QAAA,eAClFxB,OAAA;gBACEqB,SAAS,EAAC,oBAAoB;gBAC9BC,KAAK,EAAE;kBACL6B,KAAK,EAAE,GAAIrB,cAAc,GAAGI,UAAU,GAAI,GAAG,GAAG;kBAChDX,eAAe,EAAEpB,YAAY,CAACK;gBAChC;cAAE;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7B,OAAA;cAAMqB,SAAS,EAAC,qBAAqB;cAACC,KAAK,EAAE;gBAAEG,KAAK,EAAEtB,YAAY,CAACI;cAAK,CAAE;cAAAiB,QAAA,GACvEM,cAAc,EAAC,GAAC,EAACI,UAAU;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7B,OAAA;MAAKqB,SAAS,EAAC,UAAU;MAACC,KAAK,EAAE;QAAE4B,WAAW,EAAE;MAAU,CAAE;MAAA1B,QAAA,EACzDb,WAAW,CAACyC,GAAG,CAAC,CAACpB,IAAI,EAAEU,KAAK,kBAC3B1C,OAAA,CAACP,MAAM,CAAC4D,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEjB,KAAK,GAAG;QAAK,CAAE;QACpCrB,SAAS,EAAC,uCAAuC;QACjDC,KAAK,EAAE;UACLC,eAAe,EAAEuB,sBAAsB,CAACd,IAAI,EAAEU,KAAK;QACrD,CAAE;QAAAlB,QAAA,gBAEFxB,OAAA;UAAKqB,SAAS,EAAC,0BAA0B;UAAAG,QAAA,gBACvCxB,OAAA;YAAKqB,SAAS,EAAC,2BAA2B;YAAAG,QAAA,EACvCuB,WAAW,CAACf,IAAI,EAAEU,KAAK;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACN7B,OAAA;YAAKqB,SAAS,EAAC,QAAQ;YAAAG,QAAA,eACrBxB,OAAA;cAAGqB,SAAS,EAAC,SAAS;cAACC,KAAK,EAAE;gBAC5BsC,UAAU,EAAE5B,IAAI,CAACC,SAAS,GAAG,KAAK,GAAG,QAAQ;gBAC7CR,KAAK,EAAEuB,gBAAgB,CAAChB,IAAI,EAAEU,KAAK;cACrC,CAAE;cAAAlB,QAAA,EACCQ,IAAI,CAAC6B;YAAW;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELf,gBAAgB,IAAIkB,IAAI,CAACC,SAAS,iBACjCjC,OAAA;UAAKqB,SAAS,EAAC,kCAAkC;UAAAG,QAAA,GAC9CqB,aAAa,CAACH,KAAK,CAAC,KAAK,SAAS,iBACjC1C,OAAA,CAAAE,SAAA;YAAAsB,QAAA,gBACExB,OAAA,CAACP,MAAM,CAACqE,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BE,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACC,KAAK,EAAE,UAAU,CAAE;cACnDrB,SAAS,EAAC,8DAA8D;cACxEC,KAAK,EAAE;gBACLC,eAAe,EAAEpB,YAAY,CAACK,MAAM;gBACpCiB,KAAK,EAAE;cACT,CAAE;cAAAD,QAAA,gBAEFxB,OAAA,CAACH,OAAO;gBAACwB,SAAS,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB7B,OAAA,CAACP,MAAM,CAACqE,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BE,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACC,KAAK,EAAE,UAAU,CAAE;cACnDrB,SAAS,EAAC,8DAA8D;cACxEC,KAAK,EAAE;gBACLC,eAAe,EAAEpB,YAAY,CAACM,MAAM;gBACpCgB,KAAK,EAAE;cACT,CAAE;cAAAD,QAAA,gBAEFxB,OAAA,CAACF,OAAO;gBAACuB,SAAS,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA,eAChB,CACH,EACAgB,aAAa,CAACH,KAAK,CAAC,KAAK,UAAU,iBAClC1C,OAAA;YAAMqB,SAAS,EAAC,yEAAyE;YACvFC,KAAK,EAAE;cACLC,eAAe,EAAE,GAAGpB,YAAY,CAACK,MAAM,IAAI;cAC3CiB,KAAK,EAAEtB,YAAY,CAACK;YACtB,CAAE;YAAAgB,QAAA,gBACFxB,OAAA,CAACN,aAAa;cAAC2B,SAAS,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAgB,aAAa,CAACH,KAAK,CAAC,KAAK,UAAU,iBAClC1C,OAAA;YAAMqB,SAAS,EAAC,yEAAyE;YACvFC,KAAK,EAAE;cACLC,eAAe,EAAE,GAAGpB,YAAY,CAACM,MAAM,IAAI;cAC3CgB,KAAK,EAAEtB,YAAY,CAACM;YACtB,CAAE;YAAAe,QAAA,gBACFxB,OAAA,CAACL,aAAa;cAAC0B,SAAS,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA,GA5EIa,KAAK;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6EA,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CAhNIN,kBAAkB;AAAAyD,EAAA,GAAlBzD,kBAAkB;AAkNxB,eAAeA,kBAAkB;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}