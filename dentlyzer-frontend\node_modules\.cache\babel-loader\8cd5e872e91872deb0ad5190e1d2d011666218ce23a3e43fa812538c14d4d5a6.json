{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\supervisor\\\\ReviewStepsDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaCheck, FaTimes } from 'react-icons/fa';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745',\n  danger: '#dc2626'\n};\nconst ReviewStepsDisplay = ({\n  reviewSteps,\n  procedureType,\n  onStepStatusChange,\n  isSupervisorMode = false\n}) => {\n  _s();\n  const [stepStatuses, setStepStatuses] = useState({});\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 rounded-lg text-center\",\n      style: {\n        backgroundColor: '#f8f9fa'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: colorPalette.text\n        },\n        children: \"No review steps available for this procedure.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Count steps by status\n  const completedSteps = reviewSteps.filter(step => step.completed).length;\n  const totalSteps = reviewSteps.length;\n  const acceptedSteps = Object.values(stepStatuses).filter(status => status === 'accepted').length;\n  const declinedSteps = Object.values(stepStatuses).filter(status => status === 'declined').length;\n  const pendingSteps = completedSteps - acceptedSteps - declinedSteps;\n  const handleStepAction = (index, action) => {\n    const newStatuses = {\n      ...stepStatuses\n    };\n    newStatuses[index] = action;\n    setStepStatuses(newStatuses);\n    if (onStepStatusChange) {\n      onStepStatusChange(index, action);\n    }\n  };\n  const getStepStatus = index => {\n    return stepStatuses[index] || 'pending';\n  };\n  const getStepBackgroundColor = (step, index) => {\n    if (!step.completed) return colorPalette.background;\n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return `${colorPalette.accent}10`;\n      case 'declined':\n        return `${colorPalette.danger}10`;\n      default:\n        return `${colorPalette.primary}10`;\n    }\n  };\n  const getStepIcon = (step, index) => {\n    if (!step.completed) {\n      return /*#__PURE__*/_jsxDEV(FaCircle, {\n        className: \"h-5 w-5 text-gray-300\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 14\n      }, this);\n    }\n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"h-5 w-5\",\n          style: {\n            color: colorPalette.accent\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      case 'declined':\n        return /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n          className: \"h-5 w-5\",\n          style: {\n            color: colorPalette.danger\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaCircle, {\n          className: \"h-5 w-5\",\n          style: {\n            color: colorPalette.primary\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStepTextColor = (step, index) => {\n    if (!step.completed) return '#666666';\n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return colorPalette.accent;\n      case 'declined':\n        return colorPalette.danger;\n      default:\n        return colorPalette.primary;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-lg overflow-hidden\",\n    style: {\n      backgroundColor: colorPalette.background,\n      border: `1px solid #e5e7eb`\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b\",\n      style: {\n        backgroundColor: `${colorPalette.primary}10`,\n        borderColor: '#e5e7eb'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-md font-semibold\",\n          style: {\n            color: colorPalette.primary\n          },\n          children: [procedureType, \" Review Steps\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [isSupervisorMode && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"h-3 w-3 mr-1\",\n                style: {\n                  color: colorPalette.accent\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 19\n              }, this), acceptedSteps, \" Accepted\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                className: \"h-3 w-3 mr-1\",\n                style: {\n                  color: colorPalette.danger\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this), declinedSteps, \" Declined\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCircle, {\n                className: \"h-3 w-3 mr-1\",\n                style: {\n                  color: colorPalette.primary\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 19\n              }, this), pendingSteps, \" Pending\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-32 rounded-full h-2.5 mr-2\",\n              style: {\n                backgroundColor: '#e5e7eb'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-2.5 rounded-full\",\n                style: {\n                  width: `${completedSteps / totalSteps * 100}%`,\n                  backgroundColor: colorPalette.accent\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              style: {\n                color: colorPalette.text\n              },\n              children: [completedSteps, \"/\", totalSteps]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y\",\n      style: {\n        borderColor: '#e5e7eb'\n      },\n      children: reviewSteps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 5\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.05\n        },\n        className: \"p-3 flex items-center justify-between\",\n        style: {\n          backgroundColor: getStepBackgroundColor(step, index)\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 mt-0.5 mr-3\",\n            children: getStepIcon(step, index)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              style: {\n                fontWeight: step.completed ? '500' : 'normal',\n                color: getStepTextColor(step, index)\n              },\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), isSupervisorMode && step.completed && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 ml-4\",\n          children: [getStepStatus(index) === 'pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleStepAction(index, 'accepted'),\n              className: \"px-3 py-1 rounded-full text-xs font-medium flex items-center\",\n              style: {\n                backgroundColor: colorPalette.accent,\n                color: 'white'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 23\n              }, this), \"Accept\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              onClick: () => handleStepAction(index, 'declined'),\n              className: \"px-3 py-1 rounded-full text-xs font-medium flex items-center\",\n              style: {\n                backgroundColor: colorPalette.danger,\n                color: 'white'\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 23\n              }, this), \"Decline\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true), getStepStatus(index) === 'accepted' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\",\n            style: {\n              backgroundColor: `${colorPalette.accent}20`,\n              color: colorPalette.accent\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 21\n            }, this), \"Accepted\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 19\n          }, this), getStepStatus(index) === 'declined' && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\",\n            style: {\n              backgroundColor: `${colorPalette.danger}20`,\n              color: colorPalette.danger\n            },\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"h-3 w-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 21\n            }, this), \"Declined\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewStepsDisplay, \"xqurkVFGcaRp++ka7V9OhGtRPoQ=\");\n_c = ReviewStepsDisplay;\nexport default ReviewStepsDisplay;\nvar _c;\n$RefreshReg$(_c, \"ReviewStepsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaCheckCircle", "FaTimesCircle", "FaCircle", "FaCheck", "FaTimes", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "colorPalette", "primary", "secondary", "background", "text", "accent", "danger", "ReviewStepsDisplay", "reviewSteps", "procedureType", "onStepStatusChange", "isSupervisorMode", "_s", "stepStatuses", "setStepStatuses", "length", "className", "style", "backgroundColor", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "completedSteps", "filter", "step", "completed", "totalSteps", "acceptedSteps", "Object", "values", "status", "declinedSteps", "pendingSteps", "handleStepAction", "index", "action", "newStatuses", "getStepStatus", "getStepBackgroundColor", "getStepIcon", "getStepTextColor", "border", "borderColor", "width", "map", "div", "initial", "opacity", "y", "animate", "transition", "delay", "fontWeight", "description", "button", "whileHover", "scale", "whileTap", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Co<PERSON>/dentlyzer-frontend/src/supervisor/ReviewStepsDisplay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaCheck, FaTimes } from 'react-icons/fa';\n\n// Website color palette\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745',\n  danger: '#dc2626'\n};\n\nconst ReviewStepsDisplay = ({ reviewSteps, procedureType, onStepStatusChange, isSupervisorMode = false }) => {\n  const [stepStatuses, setStepStatuses] = useState({});\n\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return (\n      <div className=\"p-4 rounded-lg text-center\" style={{ backgroundColor: '#f8f9fa' }}>\n        <p style={{ color: colorPalette.text }}>No review steps available for this procedure.</p>\n      </div>\n    );\n  }\n\n  // Count steps by status\n  const completedSteps = reviewSteps.filter(step => step.completed).length;\n  const totalSteps = reviewSteps.length;\n  const acceptedSteps = Object.values(stepStatuses).filter(status => status === 'accepted').length;\n  const declinedSteps = Object.values(stepStatuses).filter(status => status === 'declined').length;\n  const pendingSteps = completedSteps - acceptedSteps - declinedSteps;\n\n  const handleStepAction = (index, action) => {\n    const newStatuses = { ...stepStatuses };\n    newStatuses[index] = action;\n    setStepStatuses(newStatuses);\n    \n    if (onStepStatusChange) {\n      onStepStatusChange(index, action);\n    }\n  };\n\n  const getStepStatus = (index) => {\n    return stepStatuses[index] || 'pending';\n  };\n\n  const getStepBackgroundColor = (step, index) => {\n    if (!step.completed) return colorPalette.background;\n    \n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return `${colorPalette.accent}10`;\n      case 'declined':\n        return `${colorPalette.danger}10`;\n      default:\n        return `${colorPalette.primary}10`;\n    }\n  };\n\n  const getStepIcon = (step, index) => {\n    if (!step.completed) {\n      return <FaCircle className=\"h-5 w-5 text-gray-300\" />;\n    }\n    \n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return <FaCheckCircle className=\"h-5 w-5\" style={{ color: colorPalette.accent }} />;\n      case 'declined':\n        return <FaTimesCircle className=\"h-5 w-5\" style={{ color: colorPalette.danger }} />;\n      default:\n        return <FaCircle className=\"h-5 w-5\" style={{ color: colorPalette.primary }} />;\n    }\n  };\n\n  const getStepTextColor = (step, index) => {\n    if (!step.completed) return '#666666';\n    \n    const status = getStepStatus(index);\n    switch (status) {\n      case 'accepted':\n        return colorPalette.accent;\n      case 'declined':\n        return colorPalette.danger;\n      default:\n        return colorPalette.primary;\n    }\n  };\n\n  return (\n    <div className=\"rounded-lg overflow-hidden\" style={{ backgroundColor: colorPalette.background, border: `1px solid #e5e7eb` }}>\n      <div className=\"p-4 border-b\" style={{ backgroundColor: `${colorPalette.primary}10`, borderColor: '#e5e7eb' }}>\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-md font-semibold\" style={{ color: colorPalette.primary }}>\n            {procedureType} Review Steps\n          </h3>\n          <div className=\"flex items-center space-x-4\">\n            {isSupervisorMode && (\n              <div className=\"flex items-center space-x-2 text-xs\">\n                <span className=\"flex items-center\">\n                  <FaCheckCircle className=\"h-3 w-3 mr-1\" style={{ color: colorPalette.accent }} />\n                  {acceptedSteps} Accepted\n                </span>\n                <span className=\"flex items-center\">\n                  <FaTimesCircle className=\"h-3 w-3 mr-1\" style={{ color: colorPalette.danger }} />\n                  {declinedSteps} Declined\n                </span>\n                <span className=\"flex items-center\">\n                  <FaCircle className=\"h-3 w-3 mr-1\" style={{ color: colorPalette.primary }} />\n                  {pendingSteps} Pending\n                </span>\n              </div>\n            )}\n            <div className=\"flex items-center\">\n              <div className=\"w-32 rounded-full h-2.5 mr-2\" style={{ backgroundColor: '#e5e7eb' }}>\n                <div\n                  className=\"h-2.5 rounded-full\"\n                  style={{\n                    width: `${(completedSteps / totalSteps) * 100}%`,\n                    backgroundColor: colorPalette.accent\n                  }}\n                ></div>\n              </div>\n              <span className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>\n                {completedSteps}/{totalSteps}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"divide-y\" style={{ borderColor: '#e5e7eb' }}>\n        {reviewSteps.map((step, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 5 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.05 }}\n            className=\"p-3 flex items-center justify-between\"\n            style={{\n              backgroundColor: getStepBackgroundColor(step, index)\n            }}\n          >\n            <div className=\"flex items-center flex-1\">\n              <div className=\"flex-shrink-0 mt-0.5 mr-3\">\n                {getStepIcon(step, index)}\n              </div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm\" style={{\n                  fontWeight: step.completed ? '500' : 'normal',\n                  color: getStepTextColor(step, index)\n                }}>\n                  {step.description}\n                </p>\n              </div>\n            </div>\n            \n            {isSupervisorMode && step.completed && (\n              <div className=\"flex items-center space-x-2 ml-4\">\n                {getStepStatus(index) === 'pending' && (\n                  <>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleStepAction(index, 'accepted')}\n                      className=\"px-3 py-1 rounded-full text-xs font-medium flex items-center\"\n                      style={{\n                        backgroundColor: colorPalette.accent,\n                        color: 'white'\n                      }}\n                    >\n                      <FaCheck className=\"h-3 w-3 mr-1\" />\n                      Accept\n                    </motion.button>\n                    <motion.button\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                      onClick={() => handleStepAction(index, 'declined')}\n                      className=\"px-3 py-1 rounded-full text-xs font-medium flex items-center\"\n                      style={{\n                        backgroundColor: colorPalette.danger,\n                        color: 'white'\n                      }}\n                    >\n                      <FaTimes className=\"h-3 w-3 mr-1\" />\n                      Decline\n                    </motion.button>\n                  </>\n                )}\n                {getStepStatus(index) === 'accepted' && (\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    style={{\n                      backgroundColor: `${colorPalette.accent}20`,\n                      color: colorPalette.accent\n                    }}>\n                    <FaCheckCircle className=\"h-3 w-3 mr-1\" />\n                    Accepted\n                  </span>\n                )}\n                {getStepStatus(index) === 'declined' && (\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    style={{\n                      backgroundColor: `${colorPalette.danger}20`,\n                      color: colorPalette.danger\n                    }}>\n                    <FaTimesCircle className=\"h-3 w-3 mr-1\" />\n                    Declined\n                  </span>\n                )}\n              </div>\n            )}\n          </motion.div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewStepsDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;;AAEzF;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,aAAa;EAAEC,kBAAkB;EAAEC,gBAAgB,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC3G,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,IAAI,CAACmB,WAAW,IAAIA,WAAW,CAACO,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACElB,OAAA;MAAKmB,SAAS,EAAC,4BAA4B;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE;MAAU,CAAE;MAAAC,QAAA,eAChFtB,OAAA;QAAGoB,KAAK,EAAE;UAAEG,KAAK,EAAEpB,YAAY,CAACI;QAAK,CAAE;QAAAe,QAAA,EAAC;MAA6C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;;EAEA;EACA,MAAMC,cAAc,GAAGjB,WAAW,CAACkB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACb,MAAM;EACxE,MAAMc,UAAU,GAAGrB,WAAW,CAACO,MAAM;EACrC,MAAMe,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACnB,YAAY,CAAC,CAACa,MAAM,CAACO,MAAM,IAAIA,MAAM,KAAK,UAAU,CAAC,CAAClB,MAAM;EAChG,MAAMmB,aAAa,GAAGH,MAAM,CAACC,MAAM,CAACnB,YAAY,CAAC,CAACa,MAAM,CAACO,MAAM,IAAIA,MAAM,KAAK,UAAU,CAAC,CAAClB,MAAM;EAChG,MAAMoB,YAAY,GAAGV,cAAc,GAAGK,aAAa,GAAGI,aAAa;EAEnE,MAAME,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;IAC1C,MAAMC,WAAW,GAAG;MAAE,GAAG1B;IAAa,CAAC;IACvC0B,WAAW,CAACF,KAAK,CAAC,GAAGC,MAAM;IAC3BxB,eAAe,CAACyB,WAAW,CAAC;IAE5B,IAAI7B,kBAAkB,EAAE;MACtBA,kBAAkB,CAAC2B,KAAK,EAAEC,MAAM,CAAC;IACnC;EACF,CAAC;EAED,MAAME,aAAa,GAAIH,KAAK,IAAK;IAC/B,OAAOxB,YAAY,CAACwB,KAAK,CAAC,IAAI,SAAS;EACzC,CAAC;EAED,MAAMI,sBAAsB,GAAGA,CAACd,IAAI,EAAEU,KAAK,KAAK;IAC9C,IAAI,CAACV,IAAI,CAACC,SAAS,EAAE,OAAO5B,YAAY,CAACG,UAAU;IAEnD,MAAM8B,MAAM,GAAGO,aAAa,CAACH,KAAK,CAAC;IACnC,QAAQJ,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,GAAGjC,YAAY,CAACK,MAAM,IAAI;MACnC,KAAK,UAAU;QACb,OAAO,GAAGL,YAAY,CAACM,MAAM,IAAI;MACnC;QACE,OAAO,GAAGN,YAAY,CAACC,OAAO,IAAI;IACtC;EACF,CAAC;EAED,MAAMyC,WAAW,GAAGA,CAACf,IAAI,EAAEU,KAAK,KAAK;IACnC,IAAI,CAACV,IAAI,CAACC,SAAS,EAAE;MACnB,oBAAO/B,OAAA,CAACJ,QAAQ;QAACuB,SAAS,EAAC;MAAuB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACvD;IAEA,MAAMS,MAAM,GAAGO,aAAa,CAACH,KAAK,CAAC;IACnC,QAAQJ,MAAM;MACZ,KAAK,UAAU;QACb,oBAAOpC,OAAA,CAACN,aAAa;UAACyB,SAAS,EAAC,SAAS;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEpB,YAAY,CAACK;UAAO;QAAE;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrF,KAAK,UAAU;QACb,oBAAO3B,OAAA,CAACL,aAAa;UAACwB,SAAS,EAAC,SAAS;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEpB,YAAY,CAACM;UAAO;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrF;QACE,oBAAO3B,OAAA,CAACJ,QAAQ;UAACuB,SAAS,EAAC,SAAS;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEpB,YAAY,CAACC;UAAQ;QAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACnF;EACF,CAAC;EAED,MAAMmB,gBAAgB,GAAGA,CAAChB,IAAI,EAAEU,KAAK,KAAK;IACxC,IAAI,CAACV,IAAI,CAACC,SAAS,EAAE,OAAO,SAAS;IAErC,MAAMK,MAAM,GAAGO,aAAa,CAACH,KAAK,CAAC;IACnC,QAAQJ,MAAM;MACZ,KAAK,UAAU;QACb,OAAOjC,YAAY,CAACK,MAAM;MAC5B,KAAK,UAAU;QACb,OAAOL,YAAY,CAACM,MAAM;MAC5B;QACE,OAAON,YAAY,CAACC,OAAO;IAC/B;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKmB,SAAS,EAAC,4BAA4B;IAACC,KAAK,EAAE;MAAEC,eAAe,EAAElB,YAAY,CAACG,UAAU;MAAEyC,MAAM,EAAE;IAAoB,CAAE;IAAAzB,QAAA,gBAC3HtB,OAAA;MAAKmB,SAAS,EAAC,cAAc;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE,GAAGlB,YAAY,CAACC,OAAO,IAAI;QAAE4C,WAAW,EAAE;MAAU,CAAE;MAAA1B,QAAA,eAC5GtB,OAAA;QAAKmB,SAAS,EAAC,mCAAmC;QAAAG,QAAA,gBAChDtB,OAAA;UAAImB,SAAS,EAAC,uBAAuB;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEpB,YAAY,CAACC;UAAQ,CAAE;UAAAkB,QAAA,GAC1EV,aAAa,EAAC,eACjB;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3B,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAAG,QAAA,GACzCR,gBAAgB,iBACfd,OAAA;YAAKmB,SAAS,EAAC,qCAAqC;YAAAG,QAAA,gBAClDtB,OAAA;cAAMmB,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBACjCtB,OAAA,CAACN,aAAa;gBAACyB,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAEpB,YAAY,CAACK;gBAAO;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChFM,aAAa,EAAC,WACjB;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3B,OAAA;cAAMmB,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBACjCtB,OAAA,CAACL,aAAa;gBAACwB,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAEpB,YAAY,CAACM;gBAAO;cAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChFU,aAAa,EAAC,WACjB;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP3B,OAAA;cAAMmB,SAAS,EAAC,mBAAmB;cAAAG,QAAA,gBACjCtB,OAAA,CAACJ,QAAQ;gBAACuB,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAEpB,YAAY,CAACC;gBAAQ;cAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5EW,YAAY,EAAC,UAChB;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eACD3B,OAAA;YAAKmB,SAAS,EAAC,mBAAmB;YAAAG,QAAA,gBAChCtB,OAAA;cAAKmB,SAAS,EAAC,8BAA8B;cAACC,KAAK,EAAE;gBAAEC,eAAe,EAAE;cAAU,CAAE;cAAAC,QAAA,eAClFtB,OAAA;gBACEmB,SAAS,EAAC,oBAAoB;gBAC9BC,KAAK,EAAE;kBACL6B,KAAK,EAAE,GAAIrB,cAAc,GAAGI,UAAU,GAAI,GAAG,GAAG;kBAChDX,eAAe,EAAElB,YAAY,CAACK;gBAChC;cAAE;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN3B,OAAA;cAAMmB,SAAS,EAAC,qBAAqB;cAACC,KAAK,EAAE;gBAAEG,KAAK,EAAEpB,YAAY,CAACI;cAAK,CAAE;cAAAe,QAAA,GACvEM,cAAc,EAAC,GAAC,EAACI,UAAU;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3B,OAAA;MAAKmB,SAAS,EAAC,UAAU;MAACC,KAAK,EAAE;QAAE4B,WAAW,EAAE;MAAU,CAAE;MAAA1B,QAAA,EACzDX,WAAW,CAACuC,GAAG,CAAC,CAACpB,IAAI,EAAEU,KAAK,kBAC3BxC,OAAA,CAACP,MAAM,CAAC0D,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEjB,KAAK,GAAG;QAAK,CAAE;QACpCrB,SAAS,EAAC,uCAAuC;QACjDC,KAAK,EAAE;UACLC,eAAe,EAAEuB,sBAAsB,CAACd,IAAI,EAAEU,KAAK;QACrD,CAAE;QAAAlB,QAAA,gBAEFtB,OAAA;UAAKmB,SAAS,EAAC,0BAA0B;UAAAG,QAAA,gBACvCtB,OAAA;YAAKmB,SAAS,EAAC,2BAA2B;YAAAG,QAAA,EACvCuB,WAAW,CAACf,IAAI,EAAEU,KAAK;UAAC;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACN3B,OAAA;YAAKmB,SAAS,EAAC,QAAQ;YAAAG,QAAA,eACrBtB,OAAA;cAAGmB,SAAS,EAAC,SAAS;cAACC,KAAK,EAAE;gBAC5BsC,UAAU,EAAE5B,IAAI,CAACC,SAAS,GAAG,KAAK,GAAG,QAAQ;gBAC7CR,KAAK,EAAEuB,gBAAgB,CAAChB,IAAI,EAAEU,KAAK;cACrC,CAAE;cAAAlB,QAAA,EACCQ,IAAI,CAAC6B;YAAW;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELb,gBAAgB,IAAIgB,IAAI,CAACC,SAAS,iBACjC/B,OAAA;UAAKmB,SAAS,EAAC,kCAAkC;UAAAG,QAAA,GAC9CqB,aAAa,CAACH,KAAK,CAAC,KAAK,SAAS,iBACjCxC,OAAA,CAAAE,SAAA;YAAAoB,QAAA,gBACEtB,OAAA,CAACP,MAAM,CAACmE,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BE,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACC,KAAK,EAAE,UAAU,CAAE;cACnDrB,SAAS,EAAC,8DAA8D;cACxEC,KAAK,EAAE;gBACLC,eAAe,EAAElB,YAAY,CAACK,MAAM;gBACpCe,KAAK,EAAE;cACT,CAAE;cAAAD,QAAA,gBAEFtB,OAAA,CAACH,OAAO;gBAACsB,SAAS,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC,eAChB3B,OAAA,CAACP,MAAM,CAACmE,MAAM;cACZC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BE,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACC,KAAK,EAAE,UAAU,CAAE;cACnDrB,SAAS,EAAC,8DAA8D;cACxEC,KAAK,EAAE;gBACLC,eAAe,EAAElB,YAAY,CAACM,MAAM;gBACpCc,KAAK,EAAE;cACT,CAAE;cAAAD,QAAA,gBAEFtB,OAAA,CAACF,OAAO;gBAACqB,SAAS,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA,eAChB,CACH,EACAgB,aAAa,CAACH,KAAK,CAAC,KAAK,UAAU,iBAClCxC,OAAA;YAAMmB,SAAS,EAAC,yEAAyE;YACvFC,KAAK,EAAE;cACLC,eAAe,EAAE,GAAGlB,YAAY,CAACK,MAAM,IAAI;cAC3Ce,KAAK,EAAEpB,YAAY,CAACK;YACtB,CAAE;YAAAc,QAAA,gBACFtB,OAAA,CAACN,aAAa;cAACyB,SAAS,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP,EACAgB,aAAa,CAACH,KAAK,CAAC,KAAK,UAAU,iBAClCxC,OAAA;YAAMmB,SAAS,EAAC,yEAAyE;YACvFC,KAAK,EAAE;cACLC,eAAe,EAAE,GAAGlB,YAAY,CAACM,MAAM,IAAI;cAC3Cc,KAAK,EAAEpB,YAAY,CAACM;YACtB,CAAE;YAAAa,QAAA,gBACFtB,OAAA,CAACL,aAAa;cAACwB,SAAS,EAAC;YAAc;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA,GA5EIa,KAAK;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6EA,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CA3MIL,kBAAkB;AAAAuD,EAAA,GAAlBvD,kBAAkB;AA6MxB,eAAeA,kBAAkB;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}