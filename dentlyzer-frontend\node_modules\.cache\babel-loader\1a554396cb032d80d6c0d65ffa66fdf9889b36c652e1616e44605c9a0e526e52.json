{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\supervisor\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaUserMd, FaCalendarAlt, FaStar, FaCheck, FaTimes, FaChartPie, FaPercentage, FaStarHalfAlt, FaFilter, FaSearch, FaChartBar, FaClipboardCheck, FaUserGraduate, FaSignature, FaCalendarCheck, FaListAlt, FaCheckCircle } from 'react-icons/fa';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';\nimport { Pie, Bar } from 'react-chartjs-2';\n\n// Import custom components\nimport SignatureManager from './SignatureManager';\nimport ReviewStepsDisplay from './ReviewStepsDisplay';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\n// Register ChartJS components\nChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);\n\n// Chart.js default configuration\nChartJS.defaults.font.family = 'Inter, system-ui, sans-serif';\nChartJS.defaults.font.size = 12;\nChartJS.defaults.color = '#6b7280';\nChartJS.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';\nChartJS.defaults.plugins.tooltip.titleColor = '#ffffff';\nChartJS.defaults.plugins.tooltip.bodyColor = '#ffffff';\nChartJS.defaults.plugins.legend.labels.usePointStyle = true;\nChartJS.defaults.plugins.legend.labels.padding = 20;\nconst SupervisorDashboard = () => {\n  _s();\n  var _selectedReview$patie, _selectedReview$patie2;\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [pendingReviews, setPendingReviews] = useState([]);\n  const [doneReviews, setDoneReviews] = useState([]);\n  const [allReviews, setAllReviews] = useState([]);\n  const [selectedReview, setSelectedReview] = useState(null);\n  const [dayFilter, setDayFilter] = useState('all');\n  const [hourFilter, setHourFilter] = useState('');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [procedureFilter, setProcedureFilter] = useState('all');\n  const [studentFilter, setStudentFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [mainTab, setMainTab] = useState('reviews');\n  const [reviewsTab, setReviewsTab] = useState('pending');\n  const [savedSignature, setSavedSignature] = useState(null);\n  const [showSignatureModal, setShowSignatureModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [analyticsTimeRange, setAnalyticsTimeRange] = useState('month');\n  const [analyticsData, setAnalyticsData] = useState({\n    statusDistribution: {\n      accepted: 0,\n      pending: 0,\n      denied: 0\n    },\n    procedureTypeDistribution: {},\n    studentPerformance: {},\n    reviewTrends: [],\n    qualityMetrics: {\n      avgProcedureQuality: 0,\n      avgPatientInteraction: 0\n    }\n  });\n  const [uniqueStudents, setUniqueStudents] = useState([]);\n  const [uniqueProcedures, setUniqueProcedures] = useState([]);\n  const [reviewForm, setReviewForm] = useState({\n    procedureQuality: 0,\n    patientInteraction: 0,\n    comment: '',\n    status: 'accepted',\n    supervisorSignature: null\n  });\n\n  // Fetch all reviews and signature data\n  useEffect(() => {\n    const fetchData = async () => {\n      console.log('Auth data:', {\n        user,\n        token\n      }); // Debug auth\n      if (!user || !token || user.role !== 'supervisor') {\n        setError('Please log in as a supervisor to view this dashboard.');\n        navigate('/login');\n        return;\n      }\n      try {\n        setLoading(true);\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n\n        // Fetch all reviews for the supervisor\n        console.log('Fetching all supervisor reviews');\n        const allReviewsRes = await axios.get('http://localhost:5000/api/reviews/supervisor', config);\n        console.log('All reviews response:', allReviewsRes.data);\n        const reviews = Array.isArray(allReviewsRes.data) ? allReviewsRes.data : [];\n        setAllReviews(reviews);\n\n        // Separate reviews by status\n        setPendingReviews(reviews.filter(r => r.status === 'pending'));\n        setDoneReviews(reviews.filter(r => r.status !== 'pending'));\n\n        // Extract unique students and procedure types for filtering\n        const students = [...new Set(reviews.map(r => {\n          var _r$studentId;\n          return ((_r$studentId = r.studentId) === null || _r$studentId === void 0 ? void 0 : _r$studentId.name) || r.studentName;\n        }))].filter(Boolean);\n        const procedures = [...new Set(reviews.map(r => r.procedureType))].filter(Boolean);\n        setUniqueStudents(students);\n        setUniqueProcedures(procedures);\n\n        // Fetch supervisor's saved signature if available\n        try {\n          const signatureRes = await axios.get('http://localhost:5000/api/supervisors/signature', config);\n          if (signatureRes.data && signatureRes.data.signature) {\n            setSavedSignature(signatureRes.data.signature);\n          }\n        } catch (signatureErr) {\n          console.error('Error fetching signature:', signatureErr);\n          // Non-critical error, continue without signature\n        }\n\n        // Fetch analytics data from the server\n        try {\n          console.log('Fetching analytics data for time range:', analyticsTimeRange);\n          const analyticsRes = await axios.get(`http://localhost:5000/api/reviews/analytics?timeRange=${analyticsTimeRange}`, config);\n          console.log('Analytics response:', analyticsRes.data);\n          if (analyticsRes.data) {\n            var _analyticsRes$data$st, _analyticsRes$data$st2, _analyticsRes$data$st3, _analyticsRes$data$qu, _analyticsRes$data$qu2;\n            // Ensure all required fields exist with proper defaults\n            const analyticsData = {\n              statusDistribution: {\n                accepted: ((_analyticsRes$data$st = analyticsRes.data.statusDistribution) === null || _analyticsRes$data$st === void 0 ? void 0 : _analyticsRes$data$st.accepted) || 0,\n                pending: ((_analyticsRes$data$st2 = analyticsRes.data.statusDistribution) === null || _analyticsRes$data$st2 === void 0 ? void 0 : _analyticsRes$data$st2.pending) || 0,\n                denied: ((_analyticsRes$data$st3 = analyticsRes.data.statusDistribution) === null || _analyticsRes$data$st3 === void 0 ? void 0 : _analyticsRes$data$st3.denied) || 0\n              },\n              procedureTypeDistribution: analyticsRes.data.procedureTypeDistribution || {},\n              studentPerformance: analyticsRes.data.studentPerformance || {},\n              reviewTrends: analyticsRes.data.reviewTrends || [],\n              qualityMetrics: {\n                avgProcedureQuality: ((_analyticsRes$data$qu = analyticsRes.data.qualityMetrics) === null || _analyticsRes$data$qu === void 0 ? void 0 : _analyticsRes$data$qu.avgProcedureQuality) || 0,\n                avgPatientInteraction: ((_analyticsRes$data$qu2 = analyticsRes.data.qualityMetrics) === null || _analyticsRes$data$qu2 === void 0 ? void 0 : _analyticsRes$data$qu2.avgPatientInteraction) || 0\n              }\n            };\n            setAnalyticsData(analyticsData);\n          }\n        } catch (analyticsErr) {\n          console.error('Error fetching analytics:', analyticsErr);\n          console.log('Falling back to client-side calculation');\n          // Fallback to client-side calculation if server analytics fails\n          if (reviews.length > 0) {\n            calculateAnalytics(reviews);\n          } else {\n            // If no reviews data, use sample data for testing\n            console.log('No reviews data available, using sample data');\n            setAnalyticsData(generateSampleData());\n          }\n        }\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data, _err$response5, _err$response6;\n        console.error('Fetch error:', {\n          message: err.message,\n          status: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status,\n          data: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data,\n          headers: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.headers\n        });\n        const errorMessage = ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || (((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 403 ? 'Access denied: Insufficient permissions' : 'Failed to load reviews. Please try again.');\n        setError(errorMessage);\n        if (((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : _err$response6.status) === 401) {\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          navigate('/login');\n        }\n        setPendingReviews([]);\n        setDoneReviews([]);\n        setAllReviews([]);\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate, analyticsTimeRange]);\n\n  // Track analytics data changes\n  useEffect(() => {\n    console.log('Analytics data updated:', {\n      statusDistribution: analyticsData.statusDistribution,\n      procedureTypes: Object.keys(analyticsData.procedureTypeDistribution),\n      students: Object.keys(analyticsData.studentPerformance),\n      trends: analyticsData.reviewTrends.length\n    });\n  }, [analyticsData]);\n\n  // Fetch new analytics data when time range changes\n  const handleAnalyticsTimeRangeChange = newRange => {\n    setAnalyticsTimeRange(newRange);\n  };\n\n  // Generate sample data for testing charts\n  const generateSampleData = () => {\n    return {\n      statusDistribution: {\n        accepted: 15,\n        pending: 8,\n        denied: 3\n      },\n      procedureTypeDistribution: {\n        'Operative Dentistry': {\n          total: 10,\n          accepted: 7,\n          denied: 2,\n          pending: 1\n        },\n        'Endodontics': {\n          total: 8,\n          accepted: 5,\n          denied: 1,\n          pending: 2\n        },\n        'Periodontics': {\n          total: 6,\n          accepted: 3,\n          denied: 0,\n          pending: 3\n        },\n        'Fixed Prosthodontics': {\n          total: 2,\n          accepted: 0,\n          denied: 0,\n          pending: 2\n        }\n      },\n      studentPerformance: {\n        'John Doe': {\n          total: 8,\n          accepted: 6,\n          denied: 1,\n          pending: 1,\n          avgProcedureQuality: 4.2,\n          avgPatientInteraction: 4.5\n        },\n        'Jane Smith': {\n          total: 6,\n          accepted: 4,\n          denied: 1,\n          pending: 1,\n          avgProcedureQuality: 3.8,\n          avgPatientInteraction: 4.0\n        },\n        'Mike Johnson': {\n          total: 4,\n          accepted: 3,\n          denied: 0,\n          pending: 1,\n          avgProcedureQuality: 4.5,\n          avgPatientInteraction: 4.2\n        }\n      },\n      reviewTrends: [{\n        month: '1/2024',\n        total: 5,\n        accepted: 3,\n        denied: 1,\n        pending: 1\n      }, {\n        month: '2/2024',\n        total: 8,\n        accepted: 6,\n        denied: 1,\n        pending: 1\n      }, {\n        month: '3/2024',\n        total: 6,\n        accepted: 4,\n        denied: 1,\n        pending: 1\n      }, {\n        month: '4/2024',\n        total: 7,\n        accepted: 2,\n        denied: 0,\n        pending: 5\n      }],\n      qualityMetrics: {\n        avgProcedureQuality: 4.1,\n        avgPatientInteraction: 4.2\n      }\n    };\n  };\n\n  // Test chart rendering\n  const testCharts = () => {\n    console.log('Testing charts with sample data...');\n    const sampleData = generateSampleData();\n    setAnalyticsData(sampleData);\n    console.log('Sample data set:', sampleData);\n  };\n\n  // Calculate analytics from reviews data\n  const calculateAnalytics = reviews => {\n    if (!Array.isArray(reviews) || reviews.length === 0) {\n      console.log('No reviews data available for analytics calculation');\n      return;\n    }\n    console.log('Calculating analytics for', reviews.length, 'reviews');\n\n    // Status distribution\n    const pending = reviews.filter(r => r.status === 'pending').length;\n    const accepted = reviews.filter(r => r.status === 'accepted').length;\n    const denied = reviews.filter(r => r.status === 'denied').length;\n    console.log('Status distribution:', {\n      pending,\n      accepted,\n      denied\n    });\n\n    // Procedure type distribution\n    const procedureTypes = {};\n    reviews.forEach(review => {\n      const type = review.procedureType || 'Unknown';\n      if (!procedureTypes[type]) {\n        procedureTypes[type] = {\n          total: 0,\n          accepted: 0,\n          denied: 0,\n          pending: 0\n        };\n      }\n      procedureTypes[type].total++;\n      if (review.status === 'accepted') procedureTypes[type].accepted++;else if (review.status === 'denied') procedureTypes[type].denied++;else procedureTypes[type].pending++;\n    });\n    console.log('Procedure types:', Object.keys(procedureTypes));\n\n    // Student performance\n    const studentPerformance = {};\n    reviews.forEach(review => {\n      var _review$studentId;\n      const studentName = review.studentName || ((_review$studentId = review.studentId) === null || _review$studentId === void 0 ? void 0 : _review$studentId.name) || 'Unknown';\n      if (!studentPerformance[studentName]) {\n        studentPerformance[studentName] = {\n          total: 0,\n          accepted: 0,\n          denied: 0,\n          pending: 0,\n          avgProcedureQuality: 0,\n          avgPatientInteraction: 0,\n          qualityRatings: [],\n          interactionRatings: []\n        };\n      }\n      studentPerformance[studentName].total++;\n      if (review.status === 'accepted') studentPerformance[studentName].accepted++;else if (review.status === 'denied') studentPerformance[studentName].denied++;else studentPerformance[studentName].pending++;\n      if (review.procedureQuality) {\n        studentPerformance[studentName].qualityRatings.push(review.procedureQuality);\n      }\n      if (review.patientInteraction) {\n        studentPerformance[studentName].interactionRatings.push(review.patientInteraction);\n      }\n    });\n\n    // Calculate averages for each student\n    Object.keys(studentPerformance).forEach(student => {\n      const {\n        qualityRatings,\n        interactionRatings\n      } = studentPerformance[student];\n      if (qualityRatings.length > 0) {\n        studentPerformance[student].avgProcedureQuality = (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);\n      }\n      if (interactionRatings.length > 0) {\n        studentPerformance[student].avgPatientInteraction = (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);\n      }\n\n      // Remove the arrays from the response\n      delete studentPerformance[student].qualityRatings;\n      delete studentPerformance[student].interactionRatings;\n    });\n    console.log('Student performance calculated for', Object.keys(studentPerformance).length, 'students');\n\n    // Review trends by month\n    const reviewsByMonth = {};\n    const now = new Date();\n    const sixMonthsAgo = new Date();\n    sixMonthsAgo.setMonth(now.getMonth() - 6);\n    reviews.forEach(review => {\n      const date = new Date(review.submittedDate);\n      if (date >= sixMonthsAgo) {\n        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;\n        if (!reviewsByMonth[monthYear]) {\n          reviewsByMonth[monthYear] = {\n            total: 0,\n            accepted: 0,\n            denied: 0,\n            pending: 0\n          };\n        }\n        reviewsByMonth[monthYear].total++;\n        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;else reviewsByMonth[monthYear].pending++;\n      }\n    });\n\n    // Convert to array and sort by date\n    const reviewTrends = Object.keys(reviewsByMonth).map(month => ({\n      month,\n      ...reviewsByMonth[month]\n    })).sort((a, b) => {\n      const [aMonth, aYear] = a.month.split('/').map(Number);\n      const [bMonth, bYear] = b.month.split('/').map(Number);\n      return aYear === bYear ? aMonth - bMonth : aYear - bYear;\n    });\n    console.log('Review trends calculated for', reviewTrends.length, 'months');\n\n    // Quality metrics\n    const doneReviewsArr = reviews.filter(r => r.status !== 'pending');\n    const avgProcedureQuality = doneReviewsArr.length > 0 ? (doneReviewsArr.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviewsArr.length).toFixed(1) : 0;\n    const avgPatientInteraction = doneReviewsArr.length > 0 ? (doneReviewsArr.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviewsArr.length).toFixed(1) : 0;\n    console.log('Quality metrics:', {\n      avgProcedureQuality,\n      avgPatientInteraction\n    });\n\n    // Update analytics state\n    const newAnalyticsData = {\n      statusDistribution: {\n        accepted,\n        pending,\n        denied\n      },\n      procedureTypeDistribution: procedureTypes,\n      studentPerformance,\n      reviewTrends,\n      qualityMetrics: {\n        avgProcedureQuality,\n        avgPatientInteraction\n      }\n    };\n    console.log('Setting analytics data:', newAnalyticsData);\n    setAnalyticsData(newAnalyticsData);\n  };\n  const handleReviewSubmit = async reviewId => {\n    if (reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0) {\n      setError('Please provide ratings for Procedure Quality and Patient Interaction');\n      return;\n    }\n    if (!reviewForm.supervisorSignature) {\n      setError('Please provide your signature to complete the review');\n      return;\n    }\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const updatedReview = {\n        status: reviewForm.status,\n        procedureQuality: reviewForm.procedureQuality,\n        patientInteraction: reviewForm.patientInteraction,\n        comment: reviewForm.comment || '',\n        supervisorSignature: reviewForm.supervisorSignature\n      };\n      console.log('Submitting review update:', {\n        reviewId,\n        ...updatedReview,\n        supervisorSignature: 'signature data present'\n      });\n      const response = await axios.put(`http://localhost:5000/api/reviews/${reviewId}`, updatedReview, config);\n      console.log('Review updated:', response.data);\n\n      // Save the signature for future use\n      setSavedSignature(reviewForm.supervisorSignature);\n\n      // Update local state\n      const reviewToMove = pendingReviews.find(r => r._id === reviewId);\n\n      // Make sure supervisor name is set in the local state\n      const updatedReviewWithSupervisor = {\n        ...updatedReview,\n        supervisorName: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown Supervisor',\n        reviewedDate: new Date()\n      };\n      setPendingReviews(prev => prev.filter(r => r._id !== reviewId));\n      setDoneReviews(prev => [...prev, {\n        ...reviewToMove,\n        ...updatedReviewWithSupervisor\n      }]);\n\n      // Recalculate analytics with the updated review\n      const allReviewsUpdated = [...doneReviews, {\n        ...reviewToMove,\n        ...updatedReviewWithSupervisor\n      }, ...pendingReviews.filter(r => r._id !== reviewId)];\n      calculateAnalytics(allReviewsUpdated);\n\n      // Reset form and close modal\n      setSelectedReview(null);\n      setReviewForm({\n        procedureQuality: 0,\n        patientInteraction: 0,\n        comment: '',\n        status: 'accepted',\n        supervisorSignature: null\n      });\n      setError('');\n\n      // Show success message in a styled popup\n      setSuccessMessage(`Review ${reviewForm.status === 'accepted' ? 'approved' : 'declined'} successfully!`);\n      setShowSuccessModal(true);\n\n      // Auto-hide the success message after 3 seconds\n      setTimeout(() => {\n        setShowSuccessModal(false);\n      }, 3000);\n    } catch (err) {\n      var _err$response7, _err$response8, _err$response9, _err$response9$data;\n      console.error('Review submit error:', {\n        message: err.message,\n        status: (_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : _err$response7.status,\n        data: (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : _err$response8.data\n      });\n      setError(((_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.message) || 'Failed to submit review');\n    }\n  };\n  const filterReviews = reviews => {\n    let filtered = [...reviews];\n\n    // Day filter\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    if (dayFilter === 'today') {\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate.toDateString() === today.toDateString();\n      });\n    } else if (dayFilter === 'tomorrow') {\n      const tomorrow = new Date(today);\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate.toDateString() === tomorrow.toDateString();\n      });\n    } else if (dayFilter === 'week') {\n      const weekEnd = new Date(today);\n      weekEnd.setDate(weekEnd.getDate() + 7);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate >= today && reviewDate <= weekEnd;\n      });\n    } else if (dayFilter === 'month') {\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate >= monthStart && reviewDate < nextMonth;\n      });\n    }\n\n    // Hour filter\n    if (hourFilter) {\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        const hours = reviewDate.getHours().toString().padStart(2, '0');\n        return `${hours}:00` === hourFilter;\n      });\n    }\n\n    // Search by patient name or student name\n    if (searchQuery) {\n      filtered = filtered.filter(r => {\n        var _r$patientId, _r$patientId$fullName, _r$studentName;\n        return ((_r$patientId = r.patientId) === null || _r$patientId === void 0 ? void 0 : (_r$patientId$fullName = _r$patientId.fullName) === null || _r$patientId$fullName === void 0 ? void 0 : _r$patientId$fullName.toLowerCase().includes(searchQuery.toLowerCase())) || ((_r$studentName = r.studentName) === null || _r$studentName === void 0 ? void 0 : _r$studentName.toLowerCase().includes(searchQuery.toLowerCase()));\n      });\n    }\n\n    // Procedure type filter\n    if (procedureFilter && procedureFilter !== 'all') {\n      filtered = filtered.filter(r => r.procedureType === procedureFilter);\n    }\n\n    // Student filter\n    if (studentFilter && studentFilter !== 'all') {\n      filtered = filtered.filter(r => r.studentName === studentFilter);\n    }\n\n    // Status filter (only applies to the \"done\" tab)\n    if (reviewsTab === 'done' && statusFilter && statusFilter !== 'all') {\n      filtered = filtered.filter(r => r.status === statusFilter);\n    }\n\n    // Sort by date (submittedDate for pending, reviewedDate for done)\n    return filtered.sort((a, b) => {\n      const dateA = new Date(reviewsTab === 'pending' ? a.submittedDate : a.reviewedDate || a.submittedDate);\n      const dateB = new Date(reviewsTab === 'pending' ? b.submittedDate : b.reviewedDate || b.submittedDate);\n      return dateB - dateA; // Newest first\n    });\n  };\n\n  // Analytics calculations\n  const totalReviews = pendingReviews.length + doneReviews.length;\n  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;\n  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;\n  const acceptanceRate = totalReviews > 0 ? (acceptedReviews / totalReviews * 100).toFixed(1) : 0;\n  const denialRate = totalReviews > 0 ? (deniedReviews / totalReviews * 100).toFixed(1) : 0;\n  const avgProcedureQuality = doneReviews.length > 0 ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1) : 0;\n  const avgPatientInteraction = doneReviews.length > 0 ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1) : 0;\n  const renderStars = (rating, onClick = null) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex\",\n    children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FaStar, {\n      className: `h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} ${onClick ? 'cursor-pointer' : ''}`,\n      onClick: onClick ? () => onClick(i + 1) : null\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 563,\n    columnNumber: 5\n  }, this);\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6\",\n        style: {\n          background: `linear-gradient(to bottom right, ${colorPalette.primary}10, ${colorPalette.background})`\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold mb-1\",\n                  style: {\n                    color: colorPalette.primary\n                  },\n                  children: \"Supervisor Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: colorPalette.text\n                  },\n                  children: [\"Welcome back, \", (user === null || user === void 0 ? void 0 : user.name) || 'Supervisor']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-3\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowSignatureModal(true),\n                  className: \"px-4 py-2 text-white rounded-lg flex items-center\",\n                  style: {\n                    background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 21\n                  }, this), \" Signature\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex border-b border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setMainTab('reviews'),\n                  className: \"px-6 py-3 font-medium text-base\",\n                  style: {\n                    borderBottom: mainTab === 'reviews' ? `2px solid ${colorPalette.primary}` : 'none',\n                    color: mainTab === 'reviews' ? colorPalette.primary : '#6b7280',\n                    backgroundColor: mainTab === 'reviews' ? `${colorPalette.primary}10` : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                    className: \"h-5 w-5 inline mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 21\n                  }, this), \"Reviews\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setMainTab('analytics'),\n                  className: \"px-6 py-3 font-medium text-base\",\n                  style: {\n                    borderBottom: mainTab === 'analytics' ? `2px solid ${colorPalette.primary}` : 'none',\n                    color: mainTab === 'analytics' ? colorPalette.primary : '#6b7280',\n                    backgroundColor: mainTab === 'analytics' ? `${colorPalette.primary}10` : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                    className: \"h-5 w-5 inline mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 21\n                  }, this), \"Analytics\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), mainTab === 'reviews' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: container,\n                initial: \"hidden\",\n                whileInView: \"show\",\n                viewport: {\n                  once: true\n                },\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaChartPie, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 688,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Total Reviews\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: totalReviews\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaPercentage, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Acceptance Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [acceptanceRate, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: [\"Decline: \", denialRate, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaStarHalfAlt, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Avg. Procedure Quality\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [avgProcedureQuality, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 729,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaStarHalfAlt, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Avg. Patient Interaction\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [avgPatientInteraction, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 747,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex border-b border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setReviewsTab('pending'),\n                    className: \"px-4 py-2 font-medium text-sm\",\n                    style: {\n                      borderBottom: reviewsTab === 'pending' ? `2px solid ${colorPalette.primary}` : 'none',\n                      color: reviewsTab === 'pending' ? colorPalette.primary : '#6b7280'\n                    },\n                    children: [\"Pending Reviews (\", pendingReviews.length, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 756,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setReviewsTab('done'),\n                    className: \"px-4 py-2 font-medium text-sm\",\n                    style: {\n                      borderBottom: reviewsTab === 'done' ? `2px solid ${colorPalette.primary}` : 'none',\n                      color: reviewsTab === 'done' ? colorPalette.primary : '#6b7280'\n                    },\n                    children: [\"Done Reviews (\", doneReviews.length, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), mainTab === 'analytics' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-bold\",\n                  style: {\n                    color: colorPalette.primary\n                  },\n                  children: \"Analytics Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: analyticsTimeRange,\n                    onChange: e => handleAnalyticsTimeRangeChange(e.target.value),\n                    className: \"px-4 py-2 border border-gray-300 rounded-lg\",\n                    style: {\n                      outline: 'none',\n                      boxShadow: 'none',\n                      borderColor: '#e5e7eb',\n                      ':focus': {\n                        borderColor: colorPalette.primary\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"week\",\n                      children: \"Last Week\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"month\",\n                      children: \"Last Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 799,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"year\",\n                      children: \"Last Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 800,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 801,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                  children: \"Debug Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-yellow-700 space-y-1 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Time Range: \", analyticsTimeRange]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Total Reviews: \", allReviews.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Status Distribution: \", JSON.stringify(analyticsData.statusDistribution)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Procedure Types: \", Object.keys(analyticsData.procedureTypeDistribution).length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Students: \", Object.keys(analyticsData.studentPerformance).length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Trends: \", analyticsData.reviewTrends.length, \" months\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setAnalyticsData(generateSampleData()),\n                    className: \"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600\",\n                    children: \"Load Sample Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => calculateAnalytics(allReviews),\n                    className: \"px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600\",\n                    children: \"Recalculate Analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: testCharts,\n                    className: \"px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600\",\n                    children: \"Test Charts\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.1\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Review Status Distribution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 854,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64 flex items-center justify-center\",\n                    children: analyticsData.statusDistribution && Object.values(analyticsData.statusDistribution).some(val => val > 0) ? /*#__PURE__*/_jsxDEV(Pie, {\n                      data: {\n                        labels: ['Accepted', 'Pending', 'Denied'],\n                        datasets: [{\n                          data: [analyticsData.statusDistribution.accepted || 0, analyticsData.statusDistribution.pending || 0, analyticsData.statusDistribution.denied || 0],\n                          backgroundColor: [`${colorPalette.accent}B3`,\n                          // accent with opacity\n                          `${colorPalette.primary}B3`,\n                          // primary with opacity\n                          'rgba(239, 68, 68, 0.7)' // red\n                          ],\n                          borderColor: [colorPalette.accent, colorPalette.primary, 'rgba(239, 68, 68, 1)'],\n                          borderWidth: 1\n                        }]\n                      },\n                      options: {\n                        responsive: true,\n                        maintainAspectRatio: false,\n                        plugins: {\n                          legend: {\n                            position: 'bottom',\n                            labels: {\n                              usePointStyle: true,\n                              padding: 20,\n                              font: {\n                                size: 12\n                              }\n                            }\n                          },\n                          tooltip: {\n                            callbacks: {\n                              label: function (context) {\n                                const label = context.label || '';\n                                const value = context.raw || 0;\n                                const total = context.dataset.data.reduce((a, b) => a + b, 0);\n                                const percentage = total > 0 ? Math.round(value / total * 100) : 0;\n                                return `${label}: ${value} (${percentage}%)`;\n                              }\n                            }\n                          }\n                        }\n                      },\n                      onRender: () => console.log('Pie chart rendered successfully'),\n                      onError: error => console.error('Pie chart error:', error)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 857,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 1,\n                          d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 914,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 913,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No review data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 916,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 912,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.2\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Procedure Type Distribution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64 flex items-center justify-center\",\n                    children: analyticsData.procedureTypeDistribution && Object.keys(analyticsData.procedureTypeDistribution).length > 0 ? /*#__PURE__*/_jsxDEV(Bar, {\n                      data: {\n                        labels: Object.keys(analyticsData.procedureTypeDistribution),\n                        datasets: [{\n                          label: 'Accepted',\n                          data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.accepted || 0),\n                          backgroundColor: `${colorPalette.accent}B3`,\n                          borderColor: colorPalette.accent,\n                          borderWidth: 1\n                        }, {\n                          label: 'Pending',\n                          data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.pending || 0),\n                          backgroundColor: `${colorPalette.primary}B3`,\n                          borderColor: colorPalette.primary,\n                          borderWidth: 1\n                        }, {\n                          label: 'Denied',\n                          data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.denied || 0),\n                          backgroundColor: 'rgba(239, 68, 68, 0.7)',\n                          borderColor: 'rgba(239, 68, 68, 1)',\n                          borderWidth: 1\n                        }]\n                      },\n                      options: {\n                        responsive: true,\n                        maintainAspectRatio: false,\n                        scales: {\n                          x: {\n                            stacked: true,\n                            ticks: {\n                              maxRotation: 45,\n                              minRotation: 0\n                            }\n                          },\n                          y: {\n                            stacked: true,\n                            beginAtZero: true,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        },\n                        plugins: {\n                          legend: {\n                            position: 'bottom',\n                            labels: {\n                              usePointStyle: true,\n                              padding: 20,\n                              font: {\n                                size: 12\n                              }\n                            }\n                          },\n                          tooltip: {\n                            mode: 'index',\n                            intersect: false\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 932,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 1,\n                          d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 999,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 998,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No procedure data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1001,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 997,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.3\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Student Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/_jsxDEV(\"table\", {\n                      className: \"min-w-full divide-y divide-gray-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Student\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1019,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Reviews\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1022,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Acceptance Rate\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1025,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Avg. Quality\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1028,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Avg. Interaction\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1031,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1018,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1017,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: analyticsData.studentPerformance && Object.keys(analyticsData.studentPerformance).length > 0 ? Object.entries(analyticsData.studentPerformance).map(([student, data], index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: student\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1040,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.total || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1043,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.total > 0 ? `${Math.round(data.accepted / data.total * 100)}%` : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1046,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.avgProcedureQuality > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"mr-2\",\n                                children: data.avgProcedureQuality\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1052,\n                                columnNumber: 41\n                              }, this), renderStars(parseFloat(data.avgProcedureQuality))]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1051,\n                              columnNumber: 39\n                            }, this) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1049,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.avgPatientInteraction > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"mr-2\",\n                                children: data.avgPatientInteraction\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1060,\n                                columnNumber: 41\n                              }, this), renderStars(parseFloat(data.avgPatientInteraction))]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1059,\n                              columnNumber: 39\n                            }, this) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1057,\n                            columnNumber: 35\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1039,\n                          columnNumber: 33\n                        }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: /*#__PURE__*/_jsxDEV(\"td\", {\n                            colSpan: \"5\",\n                            className: \"px-6 py-4 text-center text-sm text-gray-500\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-col items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                className: \"h-12 w-12 text-gray-300 mb-2\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  strokeWidth: 1,\n                                  d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1072,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1071,\n                                columnNumber: 37\n                              }, this), \"No student performance data available\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1070,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1069,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1068,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1036,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1016,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.4\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Review Trends\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64 flex items-center justify-center\",\n                    children: analyticsData.reviewTrends && analyticsData.reviewTrends.length > 0 ? /*#__PURE__*/_jsxDEV(Bar, {\n                      data: {\n                        labels: analyticsData.reviewTrends.map(t => t.month),\n                        datasets: [{\n                          label: 'Accepted',\n                          data: analyticsData.reviewTrends.map(t => t.accepted || 0),\n                          backgroundColor: `${colorPalette.accent}B3`,\n                          borderColor: colorPalette.accent,\n                          borderWidth: 1\n                        }, {\n                          label: 'Pending',\n                          data: analyticsData.reviewTrends.map(t => t.pending || 0),\n                          backgroundColor: `${colorPalette.primary}B3`,\n                          borderColor: colorPalette.primary,\n                          borderWidth: 1\n                        }, {\n                          label: 'Denied',\n                          data: analyticsData.reviewTrends.map(t => t.denied || 0),\n                          backgroundColor: 'rgba(239, 68, 68, 0.7)',\n                          borderColor: 'rgba(239, 68, 68, 1)',\n                          borderWidth: 1\n                        }]\n                      },\n                      options: {\n                        responsive: true,\n                        maintainAspectRatio: false,\n                        scales: {\n                          x: {\n                            stacked: true,\n                            ticks: {\n                              maxRotation: 45,\n                              minRotation: 0\n                            }\n                          },\n                          y: {\n                            stacked: true,\n                            beginAtZero: true,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        },\n                        plugins: {\n                          legend: {\n                            position: 'bottom',\n                            labels: {\n                              usePointStyle: true,\n                              padding: 20,\n                              font: {\n                                size: 12\n                              }\n                            }\n                          },\n                          tooltip: {\n                            mode: 'index',\n                            intersect: false\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1094,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 1,\n                          d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1161,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1160,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No trend data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1163,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1159,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.5\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-4\",\n                  style: {\n                    color: colorPalette.primary\n                  },\n                  children: \"Quality Metrics Summary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 rounded-lg\",\n                    style: {\n                      backgroundColor: `${colorPalette.primary}10`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-md font-medium mb-2\",\n                      style: {\n                        color: colorPalette.primary\n                      },\n                      children: \"Average Procedure Quality\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1180,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mr-4\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [analyticsData.qualityMetrics.avgProcedureQuality, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1182,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex\",\n                        children: renderStars(parseFloat(analyticsData.qualityMetrics.avgProcedureQuality))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1185,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1181,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1179,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 rounded-lg\",\n                    style: {\n                      backgroundColor: `${colorPalette.primary}10`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-md font-medium mb-2\",\n                      style: {\n                        color: colorPalette.primary\n                      },\n                      children: \"Average Patient Interaction\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1191,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mr-4\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [analyticsData.qualityMetrics.avgPatientInteraction, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1193,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex\",\n                        children: renderStars(parseFloat(analyticsData.qualityMetrics.avgPatientInteraction))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1196,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1192,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1178,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this), mainTab === 'reviews' && /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: reviewsTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1217,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by patient or student name\",\n                        value: searchQuery,\n                        onChange: e => setSearchQuery(e.target.value),\n                        className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1222,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(FaSearch, {\n                        className: \"absolute left-3 top-3 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1235,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1221,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => document.getElementById('filterDropdown').classList.toggle('hidden'),\n                        className: \"px-3 py-2 border border-gray-300 rounded-lg flex items-center\",\n                        style: {\n                          borderColor: '#e5e7eb',\n                          color: colorPalette.text,\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseOver: e => e.currentTarget.style.backgroundColor = '#f9fafb',\n                        onMouseOut: e => e.currentTarget.style.backgroundColor = 'transparent',\n                        children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n                          className: \"mr-2 text-gray-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1251,\n                          columnNumber: 29\n                        }, this), \" Filters\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1240,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1239,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1220,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1216,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"filterDropdown\",\n                  className: \"mb-6 p-4 border border-gray-200 rounded-lg hidden\",\n                  style: {\n                    backgroundColor: `${colorPalette.primary}05`\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Date Range\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1261,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: dayFilter,\n                        onChange: e => setDayFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Dates\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1273,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"today\",\n                          children: \"Today\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1274,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"tomorrow\",\n                          children: \"Tomorrow\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1275,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"week\",\n                          children: \"This Week\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1276,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"month\",\n                          children: \"This Month\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1277,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1262,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1260,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1282,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: hourFilter,\n                        onChange: e => setHourFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"All Hours\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1294,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"09:00\",\n                          children: \"09:00 AM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1295,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"10:00\",\n                          children: \"10:00 AM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1296,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"11:00\",\n                          children: \"11:00 AM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1297,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"12:00\",\n                          children: \"12:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1298,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"13:00\",\n                          children: \"01:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1299,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"14:00\",\n                          children: \"02:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1300,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"15:00\",\n                          children: \"03:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1301,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1283,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1281,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Procedure Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1306,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: procedureFilter,\n                        onChange: e => setProcedureFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Procedures\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1318,\n                          columnNumber: 29\n                        }, this), uniqueProcedures.map(procedure => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: procedure,\n                          children: procedure\n                        }, procedure, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1320,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1307,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1305,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1326,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: studentFilter,\n                        onChange: e => setStudentFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Students\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1338,\n                          columnNumber: 29\n                        }, this), uniqueStudents.map(student => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: student,\n                          children: student\n                        }, student, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1340,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1327,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1325,\n                      columnNumber: 25\n                    }, this), reviewsTab === 'done' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1348,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: statusFilter,\n                        onChange: e => setStatusFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Statuses\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1360,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"accepted\",\n                          children: \"Accepted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1361,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1362,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1349,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1347,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1259,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      style: {\n                        backgroundColor: `${colorPalette.primary}05`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Date\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1373,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Patient\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1374,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Student\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1375,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Procedure\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1376,\n                          columnNumber: 27\n                        }, this), reviewsTab === 'done' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                            style: {\n                              color: colorPalette.primary\n                            },\n                            children: \"Status\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1379,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                            style: {\n                              color: colorPalette.primary\n                            },\n                            children: \"Supervisor\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1380,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true), reviewsTab === 'pending' && /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1384,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1372,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1371,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: reviewsTab === 'pending' ? 5 : 6,\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"h-12 w-12 text-gray-400 mb-4\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              stroke: \"currentColor\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1394,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1393,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: [\"No \", reviewsTab === 'pending' ? 'pending' : 'completed', \" reviews\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1396,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: error ? 'Failed to load reviews due to an error. Check your permissions or try logging in again.' : reviewsTab === 'pending' ? 'No pending reviews are available. Check back later or ensure student reviews have been submitted.' : 'No completed reviews found. Try adjusting the filters or review pending submissions.'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1399,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1392,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1391,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1390,\n                        columnNumber: 27\n                      }, this) : filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).map(review => {\n                        var _review$patientId;\n                        return /*#__PURE__*/_jsxDEV(motion.tr, {\n                          initial: {\n                            opacity: 0\n                          },\n                          animate: {\n                            opacity: 1\n                          },\n                          className: \"hover:bg-gray-50 cursor-pointer\",\n                          onClick: () => setSelectedReview(review),\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: new Date(reviewsTab === 'pending' ? review.submittedDate : review.reviewedDate || review.submittedDate).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1418,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_review$patientId = review.patientId) === null || _review$patientId === void 0 ? void 0 : _review$patientId.fullName) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1421,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.studentName || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1422,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.procedureType || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1423,\n                            columnNumber: 31\n                          }, this), reviewsTab === 'done' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\",\n                                style: {\n                                  backgroundColor: review.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\n                                  color: review.status === 'accepted' ? colorPalette.accent : '#b91c1c'\n                                },\n                                children: review.status\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1427,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1426,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: review.supervisorName || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1437,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true), reviewsTab === 'pending' && /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => setSelectedReview(review),\n                              style: {\n                                color: colorPalette.primary\n                              },\n                              className: \"hover:underline\",\n                              children: \"Review\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1442,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1441,\n                            columnNumber: 33\n                          }, this)]\n                        }, review._id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1411,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1388,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1370,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1369,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1215,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1208,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), selectedReview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold\",\n              style: {\n                color: colorPalette.primary\n              },\n              children: \"Review Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1474,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedReview(null),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1477,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1476,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1475,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4 flex items-center\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                  className: \"h-5 w-5 mr-2\",\n                  style: {\n                    color: colorPalette.primary\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1486,\n                  columnNumber: 21\n                }, this), \"Patient Information\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1485,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: ((_selectedReview$patie = selectedReview.patientId) === null || _selectedReview$patie === void 0 ? void 0 : _selectedReview$patie.fullName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1492,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"National ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: ((_selectedReview$patie2 = selectedReview.patientId) === null || _selectedReview$patie2 === void 0 ? void 0 : _selectedReview$patie2.nationalId) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1494,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1489,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4 flex items-center\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"h-5 w-5 mr-2\",\n                  style: {\n                    color: colorPalette.primary\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1504,\n                  columnNumber: 21\n                }, this), \"Review Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1509,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.studentName || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1510,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1508,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Student ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1513,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: typeof selectedReview.studentId === 'object' ? selectedReview.studentId.studentId : selectedReview.studentId || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1514,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1512,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Procedure\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1517,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.procedureType || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1518,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1516,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Submission Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1521,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.submittedDate ? new Date(selectedReview.submittedDate).toLocaleString() : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1522,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Student Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1527,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.comment || 'No comment'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1528,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1526,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1507,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1502,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4 flex items-center\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaListAlt, {\n                  className: \"h-5 w-5 mr-2\",\n                  style: {\n                    color: colorPalette.primary\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1536,\n                  columnNumber: 21\n                }, this), \"Procedure Steps\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ReviewStepsDisplay, {\n                reviewSteps: selectedReview.reviewSteps || [],\n                procedureType: selectedReview.procedureType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1534,\n              columnNumber: 17\n            }, this), selectedReview.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: \"Submit Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1548,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Procedure Quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1551,\n                    columnNumber: 25\n                  }, this), renderStars(reviewForm.procedureQuality, rating => setReviewForm({\n                    ...reviewForm,\n                    procedureQuality: rating\n                  }))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1550,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Patient Interaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1557,\n                    columnNumber: 25\n                  }, this), renderStars(reviewForm.patientInteraction, rating => setReviewForm({\n                    ...reviewForm,\n                    patientInteraction: rating\n                  }))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1556,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1563,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: reviewForm.comment,\n                    onChange: e => setReviewForm({\n                      ...reviewForm,\n                      comment: e.target.value\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg\",\n                    style: {\n                      outline: 'none',\n                      boxShadow: 'none',\n                      borderColor: '#e5e7eb',\n                      ':focus': {\n                        borderColor: colorPalette.primary\n                      }\n                    },\n                    rows: \"4\",\n                    placeholder: \"Add any comments or feedback\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1564,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1562,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Review Decision\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1579,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-4 mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setReviewForm({\n                        ...reviewForm,\n                        status: 'accepted'\n                      }),\n                      className: \"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\",\n                      style: {\n                        backgroundColor: reviewForm.status === 'accepted' ? colorPalette.accent : '#f3f4f6',\n                        color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.text,\n                        fontWeight: reviewForm.status === 'accepted' ? '500' : 'normal',\n                        border: reviewForm.status === 'accepted' ? `2px solid ${colorPalette.accent}` : 'none'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                        className: \"mr-2\",\n                        style: {\n                          color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.accent\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1598,\n                        columnNumber: 29\n                      }, this), \"Accept\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1581,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setReviewForm({\n                        ...reviewForm,\n                        status: 'denied'\n                      }),\n                      className: \"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\",\n                      style: {\n                        backgroundColor: reviewForm.status === 'denied' ? '#ef4444' : '#f3f4f6',\n                        color: reviewForm.status === 'denied' ? '#ffffff' : colorPalette.text,\n                        fontWeight: reviewForm.status === 'denied' ? '500' : 'normal',\n                        border: reviewForm.status === 'denied' ? '2px solid #dc2626' : 'none'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                        className: \"mr-2\",\n                        style: {\n                          color: reviewForm.status === 'denied' ? '#ffffff' : '#ef4444'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1620,\n                        columnNumber: 29\n                      }, this), \"Decline\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1603,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1580,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1578,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6 border-t pt-4 border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Signature\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1630,\n                    columnNumber: 25\n                  }, this), savedSignature ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"You have a saved signature\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1635,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => setReviewForm(prev => ({\n                          ...prev,\n                          supervisorSignature: savedSignature\n                        })),\n                        className: \"px-4 py-2 text-white rounded-lg flex items-center\",\n                        style: {\n                          background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                          className: \"mr-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1643,\n                          columnNumber: 33\n                        }, this), \" Sign\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1636,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1634,\n                      columnNumber: 29\n                    }, this), reviewForm.supervisorSignature && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border rounded-lg p-4\",\n                      style: {\n                        borderColor: '#e5e7eb',\n                        backgroundColor: `${colorPalette.primary}05`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-medium mb-2\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: \"Signature Preview\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1653,\n                        columnNumber: 33\n                      }, this), reviewForm.supervisorSignature.startsWith('data:image') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: reviewForm.supervisorSignature,\n                        alt: \"Signature\",\n                        className: \"max-h-16 mx-auto\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1655,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-signature text-lg text-center\",\n                        children: reviewForm.supervisorSignature\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1657,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1648,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1633,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 rounded-lg mb-4\",\n                    style: {\n                      backgroundColor: `${colorPalette.secondary}10`,\n                      border: `1px solid ${colorPalette.secondary}30`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm mb-2\",\n                      style: {\n                        color: colorPalette.secondary\n                      },\n                      children: \"You don't have a saved signature. Please create one first.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1668,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      onClick: () => setShowSignatureModal(true),\n                      className: \"px-4 py-2 text-white rounded-lg flex items-center text-sm\",\n                      style: {\n                        backgroundColor: colorPalette.secondary\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                        className: \"mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1678,\n                        columnNumber: 31\n                      }, this), \" Create Signature\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1671,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1663,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1629,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-end gap-4 mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    onClick: () => handleReviewSubmit(selectedReview._id),\n                    disabled: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature,\n                    className: \"px-6 py-3 rounded-lg flex items-center shadow-md\",\n                    style: {\n                      backgroundColor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? '#d1d5db' : 'transparent',\n                      background: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? '#d1d5db' : reviewForm.status === 'accepted' ? `linear-gradient(to right, ${colorPalette.accent}, ${colorPalette.accent})` : 'linear-gradient(to right, #ef4444, #b91c1c)',\n                      color: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? '#6b7280' : '#ffffff',\n                      cursor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? 'not-allowed' : 'pointer'\n                    },\n                    children: [reviewForm.status === 'accepted' ? /*#__PURE__*/_jsxDEV(FaCheck, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1709,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(FaTimes, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1711,\n                      columnNumber: 29\n                    }, this), \"Submit \", reviewForm.status === 'accepted' ? 'Approval' : 'Decline']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1685,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1549,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1547,\n              columnNumber: 19\n            }, this), selectedReview.status !== 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: \"Supervisor Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1723,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Supervisor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1726,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.supervisorName || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1727,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1725,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Reviewed Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1730,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.reviewedDate ? new Date(selectedReview.reviewedDate).toLocaleString() : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1731,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1729,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Procedure Quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1736,\n                    columnNumber: 25\n                  }, this), renderStars(selectedReview.procedureQuality || 0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1735,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Patient Interaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1740,\n                    columnNumber: 25\n                  }, this), renderStars(selectedReview.patientInteraction || 0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1739,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1744,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.comment || 'No comment'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1745,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1743,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1748,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\",\n                    style: {\n                      backgroundColor: selectedReview.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\n                      color: selectedReview.status === 'accepted' ? colorPalette.accent : '#b91c1c'\n                    },\n                    children: selectedReview.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1749,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1747,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 pt-4 border-t border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Signature\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1762,\n                    columnNumber: 25\n                  }, this), selectedReview.supervisorSignature ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border rounded-lg p-4\",\n                    style: {\n                      borderColor: '#e5e7eb',\n                      backgroundColor: colorPalette.background\n                    },\n                    children: selectedReview.supervisorSignature.startsWith('data:image') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedReview.supervisorSignature,\n                      alt: \"Signature\",\n                      className: \"max-h-20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1769,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-signature text-lg\",\n                      children: selectedReview.supervisorSignature\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1771,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1764,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"No signature provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1775,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1761,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1724,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1722,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1482,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1472,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1467,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1466,\n      columnNumber: 9\n    }, this), showSignatureModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          type: \"spring\",\n          stiffness: 300,\n          damping: 30\n        },\n        className: \"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 rounded-t-xl\",\n          style: {\n            background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                className: \"mr-3 h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1801,\n                columnNumber: 19\n              }, this), \"Manage Your Signature\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1800,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSignatureModal(false),\n              className: \"text-white hover:text-blue-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1809,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1808,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1804,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1799,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1796,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 rounded-lg mb-6 border-l-4\",\n            style: {\n              backgroundColor: `${colorPalette.primary}10`,\n              borderColor: colorPalette.primary\n            },\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: colorPalette.primary\n              },\n              children: \"Create or update your signature below. This signature will be used for all your review approvals.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1821,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1816,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(SignatureManager, {\n            initialSignature: savedSignature,\n            onSignatureSelect: signature => {\n              setSavedSignature(signature);\n              // Close modal after a short delay to show success\n              setTimeout(() => setShowSignatureModal(false), 1500);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1826,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1815,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1790,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1789,\n      columnNumber: 9\n    }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -50\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      exit: {\n        opacity: 0,\n        y: 50\n      },\n      className: \"fixed top-0 inset-x-0 flex justify-center items-start z-50 pt-6 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl border-l-4 p-4 flex items-center max-w-md w-full\",\n        style: {\n          borderColor: colorPalette.accent\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-full mr-4\",\n          style: {\n            backgroundColor: `${colorPalette.accent}20`\n          },\n          children: /*#__PURE__*/_jsxDEV(FaCheck, {\n            className: \"text-xl\",\n            style: {\n              color: colorPalette.accent\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1850,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1849,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-medium\",\n            style: {\n              color: colorPalette.text\n            },\n            children: \"Success!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1853,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: successMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1854,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1852,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowSuccessModal(false),\n          className: \"text-gray-400 hover:text-gray-500\",\n          children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1860,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1856,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1847,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1841,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 594,\n    columnNumber: 5\n  }, this);\n};\n_s(SupervisorDashboard, \"83ZpOXWrGM/gFM5T52Bt6ekMcMs=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = SupervisorDashboard;\nexport default SupervisorDashboard;\nvar _c;\n$RefreshReg$(_c, \"SupervisorDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Loader", "motion", "FaUserMd", "FaCalendarAlt", "FaStar", "FaCheck", "FaTimes", "FaChart<PERSON>ie", "FaPercentage", "FaStarHalfAlt", "FaFilter", "FaSearch", "FaChartBar", "FaClipboardCheck", "FaUserGraduate", "FaSignature", "FaCalendarCheck", "FaListAlt", "FaCheckCircle", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "CategoryScale", "LinearScale", "BarElement", "Title", "Pie", "Bar", "SignatureManager", "ReviewStepsDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "colorPalette", "primary", "secondary", "background", "text", "accent", "register", "defaults", "font", "family", "size", "color", "plugins", "tooltip", "backgroundColor", "titleColor", "bodyColor", "legend", "labels", "usePointStyle", "padding", "SupervisorDashboard", "_s", "_selectedReview$patie", "_selectedReview$patie2", "navigate", "user", "token", "loading", "setLoading", "error", "setError", "pendingReviews", "setPendingReviews", "doneReviews", "setDoneReviews", "allReviews", "setAllReviews", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedReview", "dayFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hourFilter", "setHour<PERSON><PERSON>er", "searchQuery", "setSearch<PERSON>uery", "procedureFilter", "setProcedureFilter", "studentFilter", "setStudentFilter", "statusFilter", "setStatus<PERSON>ilter", "mainTab", "setMainTab", "reviewsTab", "setReviewsTab", "savedSignature", "setSavedSignature", "showSignatureModal", "setShowSignatureModal", "showSuccessModal", "setShowSuccessModal", "successMessage", "setSuccessMessage", "analyticsTimeRange", "setAnalyticsTimeRange", "analyticsData", "setAnalyticsData", "statusDistribution", "accepted", "pending", "denied", "procedureTypeDistribution", "studentPerformance", "reviewTrends", "qualityMetrics", "avgProcedureQuality", "avgPatientInteraction", "uniqueStudents", "setUniqueStudents", "uniqueProcedures", "setUniqueProcedures", "reviewForm", "setReviewForm", "procedureQuality", "patientInteraction", "comment", "status", "supervisorSignature", "fetchData", "console", "log", "role", "config", "headers", "Authorization", "allReviewsRes", "get", "data", "reviews", "Array", "isArray", "filter", "r", "students", "Set", "map", "_r$studentId", "studentId", "name", "studentName", "Boolean", "procedures", "procedureType", "signatureRes", "signature", "signatureErr", "analyticsRes", "_analyticsRes$data$st", "_analyticsRes$data$st2", "_analyticsRes$data$st3", "_analyticsRes$data$qu", "_analyticsRes$data$qu2", "analyticsErr", "length", "calculateAnalytics", "generateSampleData", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "_err$response5", "_err$response6", "message", "response", "errorMessage", "localStorage", "removeItem", "procedureTypes", "Object", "keys", "trends", "handleAnalyticsTimeRangeChange", "newRange", "total", "month", "test<PERSON><PERSON>s", "sampleData", "for<PERSON>ach", "review", "type", "_review$studentId", "qualityRatings", "interactionRatings", "push", "student", "reduce", "sum", "rating", "toFixed", "reviewsByMonth", "now", "Date", "sixMonthsAgo", "setMonth", "getMonth", "date", "submittedDate", "monthYear", "getFullYear", "sort", "a", "b", "aMonth", "aYear", "split", "Number", "b<PERSON>onth", "bYear", "doneReviewsArr", "newAnalyticsData", "handleReviewSubmit", "reviewId", "updatedReview", "put", "reviewToMove", "find", "_id", "updatedReviewWithSupervisor", "<PERSON><PERSON><PERSON>", "reviewedDate", "prev", "allReviewsUpdated", "setTimeout", "_err$response7", "_err$response8", "_err$response9", "_err$response9$data", "filterReviews", "filtered", "today", "setHours", "reviewDate", "toDateString", "tomorrow", "setDate", "getDate", "weekEnd", "monthStart", "nextMonth", "hours", "getHours", "toString", "padStart", "_r$patientId", "_r$patientId$fullName", "_r$studentName", "patientId", "fullName", "toLowerCase", "includes", "dateA", "dateB", "totalReviews", "acceptedReviews", "deniedReviews", "acceptanceRate", "denialRate", "renderStars", "onClick", "className", "children", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "toggleSidebar", "style", "div", "initial", "animate", "fill", "viewBox", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "borderBottom", "variants", "whileInView", "viewport", "once", "value", "onChange", "e", "target", "outline", "boxShadow", "borderColor", "process", "env", "NODE_ENV", "JSON", "stringify", "delay", "values", "some", "val", "datasets", "borderWidth", "options", "responsive", "maintainAspectRatio", "position", "callbacks", "label", "context", "raw", "dataset", "percentage", "Math", "round", "onRender", "onError", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "p", "scales", "x", "stacked", "ticks", "maxRotation", "minRotation", "beginAtZero", "stepSize", "mode", "intersect", "entries", "index", "parseFloat", "colSpan", "t", "placeholder", "document", "getElementById", "classList", "toggle", "onMouseOver", "currentTarget", "onMouseOut", "id", "procedure", "_review$patientId", "tr", "toLocaleDateString", "nationalId", "toLocaleString", "reviewSteps", "rows", "fontWeight", "border", "startsWith", "src", "alt", "disabled", "cursor", "stiffness", "damping", "initialSignature", "onSignatureSelect", "exit", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Co<PERSON>/dentlyzer-frontend/src/supervisor/Dashboard.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from '../student/Navbar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  FaUserMd, FaCalendarAlt, FaStar, FaCheck, FaTimes,\r\n  FaChartPie, FaPercentage, FaStarHalfAlt, FaFilter, FaSearch,\r\n  FaChartBar, FaClipboardCheck, FaUserGraduate, FaSignature,\r\n  FaCalendarCheck, FaListAlt, FaCheckCircle\r\n} from 'react-icons/fa';\r\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';\r\nimport { Pie, Bar } from 'react-chartjs-2';\r\n\r\n// Import custom components\r\nimport SignatureManager from './SignatureManager';\r\nimport ReviewStepsDisplay from './ReviewStepsDisplay';\r\n\r\n// Website color palette\r\nconst colorPalette = {\r\n  primary: '#0077B6',\r\n  secondary: '#20B2AA',\r\n  background: '#FFFFFF',\r\n  text: '#333333',\r\n  accent: '#28A745'\r\n};\r\n\r\n// Register ChartJS components\r\nChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);\r\n\r\n// Chart.js default configuration\r\nChartJS.defaults.font.family = 'Inter, system-ui, sans-serif';\r\nChartJS.defaults.font.size = 12;\r\nChartJS.defaults.color = '#6b7280';\r\nChartJS.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';\r\nChartJS.defaults.plugins.tooltip.titleColor = '#ffffff';\r\nChartJS.defaults.plugins.tooltip.bodyColor = '#ffffff';\r\nChartJS.defaults.plugins.legend.labels.usePointStyle = true;\r\nChartJS.defaults.plugins.legend.labels.padding = 20;\r\n\r\nconst SupervisorDashboard = () => {\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [pendingReviews, setPendingReviews] = useState([]);\r\n  const [doneReviews, setDoneReviews] = useState([]);\r\n  const [allReviews, setAllReviews] = useState([]);\r\n  const [selectedReview, setSelectedReview] = useState(null);\r\n  const [dayFilter, setDayFilter] = useState('all');\r\n  const [hourFilter, setHourFilter] = useState('');\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [procedureFilter, setProcedureFilter] = useState('all');\r\n  const [studentFilter, setStudentFilter] = useState('all');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [mainTab, setMainTab] = useState('reviews');\r\n  const [reviewsTab, setReviewsTab] = useState('pending');\r\n  const [savedSignature, setSavedSignature] = useState(null);\r\n  const [showSignatureModal, setShowSignatureModal] = useState(false);\r\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [analyticsTimeRange, setAnalyticsTimeRange] = useState('month');\r\n  const [analyticsData, setAnalyticsData] = useState({\r\n    statusDistribution: { accepted: 0, pending: 0, denied: 0 },\r\n    procedureTypeDistribution: {},\r\n    studentPerformance: {},\r\n    reviewTrends: [],\r\n    qualityMetrics: {\r\n      avgProcedureQuality: 0,\r\n      avgPatientInteraction: 0\r\n    }\r\n  });\r\n  const [uniqueStudents, setUniqueStudents] = useState([]);\r\n  const [uniqueProcedures, setUniqueProcedures] = useState([]);\r\n\r\n  const [reviewForm, setReviewForm] = useState({\r\n    procedureQuality: 0,\r\n    patientInteraction: 0,\r\n    comment: '',\r\n    status: 'accepted',\r\n    supervisorSignature: null\r\n  });\r\n\r\n\r\n\r\n  // Fetch all reviews and signature data\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      console.log('Auth data:', { user, token }); // Debug auth\r\n      if (!user || !token || user.role !== 'supervisor') {\r\n        setError('Please log in as a supervisor to view this dashboard.');\r\n        navigate('/login');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n\r\n        // Fetch all reviews for the supervisor\r\n        console.log('Fetching all supervisor reviews');\r\n        const allReviewsRes = await axios.get('http://localhost:5000/api/reviews/supervisor', config);\r\n        console.log('All reviews response:', allReviewsRes.data);\r\n\r\n        const reviews = Array.isArray(allReviewsRes.data) ? allReviewsRes.data : [];\r\n        setAllReviews(reviews);\r\n\r\n        // Separate reviews by status\r\n        setPendingReviews(reviews.filter(r => r.status === 'pending'));\r\n        setDoneReviews(reviews.filter(r => r.status !== 'pending'));\r\n\r\n        // Extract unique students and procedure types for filtering\r\n        const students = [...new Set(reviews.map(r => r.studentId?.name || r.studentName))].filter(Boolean);\r\n        const procedures = [...new Set(reviews.map(r => r.procedureType))].filter(Boolean);\r\n\r\n        setUniqueStudents(students);\r\n        setUniqueProcedures(procedures);\r\n\r\n        // Fetch supervisor's saved signature if available\r\n        try {\r\n          const signatureRes = await axios.get('http://localhost:5000/api/supervisors/signature', config);\r\n          if (signatureRes.data && signatureRes.data.signature) {\r\n            setSavedSignature(signatureRes.data.signature);\r\n          }\r\n        } catch (signatureErr) {\r\n          console.error('Error fetching signature:', signatureErr);\r\n          // Non-critical error, continue without signature\r\n        }\r\n\r\n        // Fetch analytics data from the server\r\n        try {\r\n          console.log('Fetching analytics data for time range:', analyticsTimeRange);\r\n          const analyticsRes = await axios.get(`http://localhost:5000/api/reviews/analytics?timeRange=${analyticsTimeRange}`, config);\r\n          console.log('Analytics response:', analyticsRes.data);\r\n          \r\n          if (analyticsRes.data) {\r\n            // Ensure all required fields exist with proper defaults\r\n            const analyticsData = {\r\n              statusDistribution: {\r\n                accepted: analyticsRes.data.statusDistribution?.accepted || 0,\r\n                pending: analyticsRes.data.statusDistribution?.pending || 0,\r\n                denied: analyticsRes.data.statusDistribution?.denied || 0\r\n              },\r\n              procedureTypeDistribution: analyticsRes.data.procedureTypeDistribution || {},\r\n              studentPerformance: analyticsRes.data.studentPerformance || {},\r\n              reviewTrends: analyticsRes.data.reviewTrends || [],\r\n              qualityMetrics: {\r\n                avgProcedureQuality: analyticsRes.data.qualityMetrics?.avgProcedureQuality || 0,\r\n                avgPatientInteraction: analyticsRes.data.qualityMetrics?.avgPatientInteraction || 0\r\n              }\r\n            };\r\n            setAnalyticsData(analyticsData);\r\n          }\r\n        } catch (analyticsErr) {\r\n          console.error('Error fetching analytics:', analyticsErr);\r\n          console.log('Falling back to client-side calculation');\r\n          // Fallback to client-side calculation if server analytics fails\r\n          if (reviews.length > 0) {\r\n            calculateAnalytics(reviews);\r\n          } else {\r\n            // If no reviews data, use sample data for testing\r\n            console.log('No reviews data available, using sample data');\r\n            setAnalyticsData(generateSampleData());\r\n          }\r\n        }\r\n\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Fetch error:', {\r\n          message: err.message,\r\n          status: err.response?.status,\r\n          data: err.response?.data,\r\n          headers: err.response?.headers,\r\n        });\r\n        const errorMessage = err.response?.data?.message ||\r\n                            (err.response?.status === 403 ? 'Access denied: Insufficient permissions' :\r\n                            'Failed to load reviews. Please try again.');\r\n        setError(errorMessage);\r\n        if (err.response?.status === 401) {\r\n          localStorage.removeItem('token');\r\n          localStorage.removeItem('user');\r\n          navigate('/login');\r\n        }\r\n        setPendingReviews([]);\r\n        setDoneReviews([]);\r\n        setAllReviews([]);\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [user, token, navigate, analyticsTimeRange]);\r\n\r\n  // Track analytics data changes\r\n  useEffect(() => {\r\n    console.log('Analytics data updated:', {\r\n      statusDistribution: analyticsData.statusDistribution,\r\n      procedureTypes: Object.keys(analyticsData.procedureTypeDistribution),\r\n      students: Object.keys(analyticsData.studentPerformance),\r\n      trends: analyticsData.reviewTrends.length\r\n    });\r\n  }, [analyticsData]);\r\n\r\n  // Fetch new analytics data when time range changes\r\n  const handleAnalyticsTimeRangeChange = (newRange) => {\r\n    setAnalyticsTimeRange(newRange);\r\n  };\r\n\r\n  // Generate sample data for testing charts\r\n  const generateSampleData = () => {\r\n    return {\r\n      statusDistribution: { accepted: 15, pending: 8, denied: 3 },\r\n      procedureTypeDistribution: {\r\n        'Operative Dentistry': { total: 10, accepted: 7, denied: 2, pending: 1 },\r\n        'Endodontics': { total: 8, accepted: 5, denied: 1, pending: 2 },\r\n        'Periodontics': { total: 6, accepted: 3, denied: 0, pending: 3 },\r\n        'Fixed Prosthodontics': { total: 2, accepted: 0, denied: 0, pending: 2 }\r\n      },\r\n      studentPerformance: {\r\n        'John Doe': { total: 8, accepted: 6, denied: 1, pending: 1, avgProcedureQuality: 4.2, avgPatientInteraction: 4.5 },\r\n        'Jane Smith': { total: 6, accepted: 4, denied: 1, pending: 1, avgProcedureQuality: 3.8, avgPatientInteraction: 4.0 },\r\n        'Mike Johnson': { total: 4, accepted: 3, denied: 0, pending: 1, avgProcedureQuality: 4.5, avgPatientInteraction: 4.2 }\r\n      },\r\n      reviewTrends: [\r\n        { month: '1/2024', total: 5, accepted: 3, denied: 1, pending: 1 },\r\n        { month: '2/2024', total: 8, accepted: 6, denied: 1, pending: 1 },\r\n        { month: '3/2024', total: 6, accepted: 4, denied: 1, pending: 1 },\r\n        { month: '4/2024', total: 7, accepted: 2, denied: 0, pending: 5 }\r\n      ],\r\n      qualityMetrics: {\r\n        avgProcedureQuality: 4.1,\r\n        avgPatientInteraction: 4.2\r\n      }\r\n    };\r\n  };\r\n\r\n  // Test chart rendering\r\n  const testCharts = () => {\r\n    console.log('Testing charts with sample data...');\r\n    const sampleData = generateSampleData();\r\n    setAnalyticsData(sampleData);\r\n    console.log('Sample data set:', sampleData);\r\n  };\r\n\r\n  // Calculate analytics from reviews data\r\n  const calculateAnalytics = (reviews) => {\r\n    if (!Array.isArray(reviews) || reviews.length === 0) {\r\n      console.log('No reviews data available for analytics calculation');\r\n      return;\r\n    }\r\n\r\n    console.log('Calculating analytics for', reviews.length, 'reviews');\r\n\r\n    // Status distribution\r\n    const pending = reviews.filter(r => r.status === 'pending').length;\r\n    const accepted = reviews.filter(r => r.status === 'accepted').length;\r\n    const denied = reviews.filter(r => r.status === 'denied').length;\r\n\r\n    console.log('Status distribution:', { pending, accepted, denied });\r\n\r\n    // Procedure type distribution\r\n    const procedureTypes = {};\r\n    reviews.forEach(review => {\r\n      const type = review.procedureType || 'Unknown';\r\n      if (!procedureTypes[type]) {\r\n        procedureTypes[type] = {\r\n          total: 0,\r\n          accepted: 0,\r\n          denied: 0,\r\n          pending: 0\r\n        };\r\n      }\r\n      procedureTypes[type].total++;\r\n      if (review.status === 'accepted') procedureTypes[type].accepted++;\r\n      else if (review.status === 'denied') procedureTypes[type].denied++;\r\n      else procedureTypes[type].pending++;\r\n    });\r\n\r\n    console.log('Procedure types:', Object.keys(procedureTypes));\r\n\r\n    // Student performance\r\n    const studentPerformance = {};\r\n    reviews.forEach(review => {\r\n      const studentName = review.studentName || review.studentId?.name || 'Unknown';\r\n      if (!studentPerformance[studentName]) {\r\n        studentPerformance[studentName] = {\r\n          total: 0,\r\n          accepted: 0,\r\n          denied: 0,\r\n          pending: 0,\r\n          avgProcedureQuality: 0,\r\n          avgPatientInteraction: 0,\r\n          qualityRatings: [],\r\n          interactionRatings: []\r\n        };\r\n      }\r\n\r\n      studentPerformance[studentName].total++;\r\n      if (review.status === 'accepted') studentPerformance[studentName].accepted++;\r\n      else if (review.status === 'denied') studentPerformance[studentName].denied++;\r\n      else studentPerformance[studentName].pending++;\r\n\r\n      if (review.procedureQuality) {\r\n        studentPerformance[studentName].qualityRatings.push(review.procedureQuality);\r\n      }\r\n\r\n      if (review.patientInteraction) {\r\n        studentPerformance[studentName].interactionRatings.push(review.patientInteraction);\r\n      }\r\n    });\r\n\r\n    // Calculate averages for each student\r\n    Object.keys(studentPerformance).forEach(student => {\r\n      const { qualityRatings, interactionRatings } = studentPerformance[student];\r\n\r\n      if (qualityRatings.length > 0) {\r\n        studentPerformance[student].avgProcedureQuality =\r\n          (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);\r\n      }\r\n\r\n      if (interactionRatings.length > 0) {\r\n        studentPerformance[student].avgPatientInteraction =\r\n          (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);\r\n      }\r\n\r\n      // Remove the arrays from the response\r\n      delete studentPerformance[student].qualityRatings;\r\n      delete studentPerformance[student].interactionRatings;\r\n    });\r\n\r\n    console.log('Student performance calculated for', Object.keys(studentPerformance).length, 'students');\r\n\r\n    // Review trends by month\r\n    const reviewsByMonth = {};\r\n    const now = new Date();\r\n    const sixMonthsAgo = new Date();\r\n    sixMonthsAgo.setMonth(now.getMonth() - 6);\r\n\r\n    reviews.forEach(review => {\r\n      const date = new Date(review.submittedDate);\r\n      if (date >= sixMonthsAgo) {\r\n        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;\r\n        if (!reviewsByMonth[monthYear]) {\r\n          reviewsByMonth[monthYear] = { total: 0, accepted: 0, denied: 0, pending: 0 };\r\n        }\r\n        reviewsByMonth[monthYear].total++;\r\n        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;\r\n        else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;\r\n        else reviewsByMonth[monthYear].pending++;\r\n      }\r\n    });\r\n\r\n    // Convert to array and sort by date\r\n    const reviewTrends = Object.keys(reviewsByMonth).map(month => ({\r\n      month,\r\n      ...reviewsByMonth[month]\r\n    })).sort((a, b) => {\r\n      const [aMonth, aYear] = a.month.split('/').map(Number);\r\n      const [bMonth, bYear] = b.month.split('/').map(Number);\r\n      return aYear === bYear ? aMonth - bMonth : aYear - bYear;\r\n    });\r\n\r\n    console.log('Review trends calculated for', reviewTrends.length, 'months');\r\n\r\n    // Quality metrics\r\n    const doneReviewsArr = reviews.filter(r => r.status !== 'pending');\r\n    const avgProcedureQuality = doneReviewsArr.length > 0\r\n      ? (doneReviewsArr.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviewsArr.length).toFixed(1)\r\n      : 0;\r\n    const avgPatientInteraction = doneReviewsArr.length > 0\r\n      ? (doneReviewsArr.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviewsArr.length).toFixed(1)\r\n      : 0;\r\n\r\n    console.log('Quality metrics:', { avgProcedureQuality, avgPatientInteraction });\r\n\r\n    // Update analytics state\r\n    const newAnalyticsData = {\r\n      statusDistribution: { accepted, pending, denied },\r\n      procedureTypeDistribution: procedureTypes,\r\n      studentPerformance,\r\n      reviewTrends,\r\n      qualityMetrics: {\r\n        avgProcedureQuality,\r\n        avgPatientInteraction\r\n      }\r\n    };\r\n\r\n    console.log('Setting analytics data:', newAnalyticsData);\r\n    setAnalyticsData(newAnalyticsData);\r\n  };\r\n\r\n\r\n\r\n  const handleReviewSubmit = async (reviewId) => {\r\n    if (reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0) {\r\n      setError('Please provide ratings for Procedure Quality and Patient Interaction');\r\n      return;\r\n    }\r\n\r\n    if (!reviewForm.supervisorSignature) {\r\n      setError('Please provide your signature to complete the review');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      const updatedReview = {\r\n        status: reviewForm.status,\r\n        procedureQuality: reviewForm.procedureQuality,\r\n        patientInteraction: reviewForm.patientInteraction,\r\n        comment: reviewForm.comment || '',\r\n        supervisorSignature: reviewForm.supervisorSignature\r\n      };\r\n\r\n      console.log('Submitting review update:', { reviewId, ...updatedReview, supervisorSignature: 'signature data present' });\r\n      const response = await axios.put(`http://localhost:5000/api/reviews/${reviewId}`, updatedReview, config);\r\n      console.log('Review updated:', response.data);\r\n\r\n      // Save the signature for future use\r\n      setSavedSignature(reviewForm.supervisorSignature);\r\n\r\n      // Update local state\r\n      const reviewToMove = pendingReviews.find(r => r._id === reviewId);\r\n\r\n      // Make sure supervisor name is set in the local state\r\n      const updatedReviewWithSupervisor = {\r\n        ...updatedReview,\r\n        supervisorName: user?.name || 'Unknown Supervisor',\r\n        reviewedDate: new Date()\r\n      };\r\n\r\n      setPendingReviews(prev => prev.filter(r => r._id !== reviewId));\r\n      setDoneReviews(prev => [...prev, { ...reviewToMove, ...updatedReviewWithSupervisor }]);\r\n\r\n      // Recalculate analytics with the updated review\r\n      const allReviewsUpdated = [\r\n        ...doneReviews,\r\n        { ...reviewToMove, ...updatedReviewWithSupervisor },\r\n        ...pendingReviews.filter(r => r._id !== reviewId)\r\n      ];\r\n      calculateAnalytics(allReviewsUpdated);\r\n\r\n      // Reset form and close modal\r\n      setSelectedReview(null);\r\n      setReviewForm({\r\n        procedureQuality: 0,\r\n        patientInteraction: 0,\r\n        comment: '',\r\n        status: 'accepted',\r\n        supervisorSignature: null\r\n      });\r\n      setError('');\r\n\r\n      // Show success message in a styled popup\r\n      setSuccessMessage(`Review ${reviewForm.status === 'accepted' ? 'approved' : 'declined'} successfully!`);\r\n      setShowSuccessModal(true);\r\n\r\n      // Auto-hide the success message after 3 seconds\r\n      setTimeout(() => {\r\n        setShowSuccessModal(false);\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Review submit error:', {\r\n        message: err.message,\r\n        status: err.response?.status,\r\n        data: err.response?.data,\r\n      });\r\n      setError(err.response?.data?.message || 'Failed to submit review');\r\n    }\r\n  };\r\n\r\n  const filterReviews = (reviews) => {\r\n    let filtered = [...reviews];\r\n\r\n    // Day filter\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    if (dayFilter === 'today') {\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate.toDateString() === today.toDateString();\r\n      });\r\n    } else if (dayFilter === 'tomorrow') {\r\n      const tomorrow = new Date(today);\r\n      tomorrow.setDate(tomorrow.getDate() + 1);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate.toDateString() === tomorrow.toDateString();\r\n      });\r\n    } else if (dayFilter === 'week') {\r\n      const weekEnd = new Date(today);\r\n      weekEnd.setDate(weekEnd.getDate() + 7);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate >= today && reviewDate <= weekEnd;\r\n      });\r\n    } else if (dayFilter === 'month') {\r\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate >= monthStart && reviewDate < nextMonth;\r\n      });\r\n    }\r\n\r\n    // Hour filter\r\n    if (hourFilter) {\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        const hours = reviewDate.getHours().toString().padStart(2, '0');\r\n        return `${hours}:00` === hourFilter;\r\n      });\r\n    }\r\n\r\n    // Search by patient name or student name\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(r =>\r\n        (r.patientId?.fullName?.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n        (r.studentName?.toLowerCase().includes(searchQuery.toLowerCase()))\r\n      );\r\n    }\r\n\r\n    // Procedure type filter\r\n    if (procedureFilter && procedureFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.procedureType === procedureFilter);\r\n    }\r\n\r\n    // Student filter\r\n    if (studentFilter && studentFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.studentName === studentFilter);\r\n    }\r\n\r\n    // Status filter (only applies to the \"done\" tab)\r\n    if (reviewsTab === 'done' && statusFilter && statusFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.status === statusFilter);\r\n    }\r\n\r\n    // Sort by date (submittedDate for pending, reviewedDate for done)\r\n    return filtered.sort((a, b) => {\r\n      const dateA = new Date(reviewsTab === 'pending' ? a.submittedDate : a.reviewedDate || a.submittedDate);\r\n      const dateB = new Date(reviewsTab === 'pending' ? b.submittedDate : b.reviewedDate || b.submittedDate);\r\n      return dateB - dateA; // Newest first\r\n    });\r\n  };\r\n\r\n  // Analytics calculations\r\n  const totalReviews = pendingReviews.length + doneReviews.length;\r\n  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;\r\n  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;\r\n  const acceptanceRate = totalReviews > 0 ? ((acceptedReviews / totalReviews) * 100).toFixed(1) : 0;\r\n  const denialRate = totalReviews > 0 ? ((deniedReviews / totalReviews) * 100).toFixed(1) : 0;\r\n  const avgProcedureQuality =\r\n    doneReviews.length > 0\r\n      ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1)\r\n      : 0;\r\n  const avgPatientInteraction =\r\n    doneReviews.length > 0\r\n      ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1)\r\n      : 0;\r\n\r\n  const renderStars = (rating, onClick = null) => (\r\n    <div className=\"flex\">\r\n      {[...Array(5)].map((_, i) => (\r\n        <FaStar\r\n          key={i}\r\n          className={`h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} ${\r\n            onClick ? 'cursor-pointer' : ''\r\n          }`}\r\n          onClick={onClick ? () => onClick(i + 1) : null}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: { staggerChildren: 0.1 },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => {}} />\r\n        <main className=\"flex-1 overflow-y-auto p-6\" style={{ background: `linear-gradient(to bottom right, ${colorPalette.primary}10, ${colorPalette.background})` }}>\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-red-700 font-medium\">{error}</p>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold mb-1\" style={{ color: colorPalette.primary }}>Supervisor Dashboard</h1>\r\n                  <p style={{ color: colorPalette.text }}>Welcome back, {user?.name || 'Supervisor'}</p>\r\n                </div>\r\n                <div className=\"flex gap-3\">\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={() => setShowSignatureModal(true)}\r\n                    className=\"px-4 py-2 text-white rounded-lg flex items-center\"\r\n                    style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}\r\n                  >\r\n                    <FaSignature className=\"mr-2\" /> Signature\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Main Tabs */}\r\n              <div className=\"mb-8\">\r\n                <div className=\"flex border-b border-gray-200\">\r\n                  <button\r\n                    onClick={() => setMainTab('reviews')}\r\n                    className=\"px-6 py-3 font-medium text-base\"\r\n                    style={{\r\n                      borderBottom: mainTab === 'reviews' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                      color: mainTab === 'reviews' ? colorPalette.primary : '#6b7280',\r\n                      backgroundColor: mainTab === 'reviews' ? `${colorPalette.primary}10` : 'transparent'\r\n                    }}\r\n                  >\r\n                    <FaClipboardCheck className=\"h-5 w-5 inline mr-2\" />\r\n                    Reviews\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setMainTab('analytics')}\r\n                    className=\"px-6 py-3 font-medium text-base\"\r\n                    style={{\r\n                      borderBottom: mainTab === 'analytics' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                      color: mainTab === 'analytics' ? colorPalette.primary : '#6b7280',\r\n                      backgroundColor: mainTab === 'analytics' ? `${colorPalette.primary}10` : 'transparent'\r\n                    }}\r\n                  >\r\n                    <FaChartBar className=\"h-5 w-5 inline mr-2\" />\r\n                    Analytics\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Reviews Section */}\r\n              {mainTab === 'reviews' && (\r\n                <>\r\n                  {/* Analytics Summary */}\r\n                  <motion.div\r\n                    variants={container}\r\n                    initial=\"hidden\"\r\n                    whileInView=\"show\"\r\n                    viewport={{ once: true }}\r\n                    className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n                  >\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaChartPie className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Total Reviews</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{totalReviews}</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaPercentage className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Acceptance Rate</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{acceptanceRate}%</p>\r\n                          <p className=\"text-xs\" style={{ color: colorPalette.text }}>Decline: {denialRate}%</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaStarHalfAlt className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Avg. Procedure Quality</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{avgProcedureQuality}/5</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaStarHalfAlt className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Avg. Patient Interaction</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{avgPatientInteraction}/5</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                  </motion.div>\r\n\r\n                  {/* Reviews Tabs */}\r\n                  <div className=\"mb-6\">\r\n                    <div className=\"flex border-b border-gray-200\">\r\n                      <button\r\n                        onClick={() => setReviewsTab('pending')}\r\n                        className=\"px-4 py-2 font-medium text-sm\"\r\n                        style={{\r\n                          borderBottom: reviewsTab === 'pending' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                          color: reviewsTab === 'pending' ? colorPalette.primary : '#6b7280'\r\n                        }}\r\n                      >\r\n                        Pending Reviews ({pendingReviews.length})\r\n                      </button>\r\n                      <button\r\n                        onClick={() => setReviewsTab('done')}\r\n                        className=\"px-4 py-2 font-medium text-sm\"\r\n                        style={{\r\n                          borderBottom: reviewsTab === 'done' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                          color: reviewsTab === 'done' ? colorPalette.primary : '#6b7280'\r\n                        }}\r\n                      >\r\n                        Done Reviews ({doneReviews.length})\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              {/* Analytics Section */}\r\n              {mainTab === 'analytics' && (\r\n                <div className=\"mb-6\">\r\n                  <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4\">\r\n                    <h2 className=\"text-xl font-bold\" style={{ color: colorPalette.primary }}>Analytics Dashboard</h2>\r\n                    <div className=\"flex gap-3\">\r\n                      <select\r\n                        value={analyticsTimeRange}\r\n                        onChange={(e) => handleAnalyticsTimeRangeChange(e.target.value)}\r\n                        className=\"px-4 py-2 border border-gray-300 rounded-lg\"\r\n                        style={{\r\n                          outline: 'none',\r\n                          boxShadow: 'none',\r\n                          borderColor: '#e5e7eb',\r\n                          ':focus': { borderColor: colorPalette.primary }\r\n                        }}\r\n                      >\r\n                        <option value=\"week\">Last Week</option>\r\n                        <option value=\"month\">Last Month</option>\r\n                        <option value=\"year\">Last Year</option>\r\n                        <option value=\"all\">All Time</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Debug Information - Only show in development */}\r\n                  {process.env.NODE_ENV === 'development' && (\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\"\r\n                    >\r\n                      <h3 className=\"text-sm font-semibold text-yellow-800 mb-2\">Debug Information</h3>\r\n                      <div className=\"text-xs text-yellow-700 space-y-1 mb-3\">\r\n                        <p>Time Range: {analyticsTimeRange}</p>\r\n                        <p>Total Reviews: {allReviews.length}</p>\r\n                        <p>Status Distribution: {JSON.stringify(analyticsData.statusDistribution)}</p>\r\n                        <p>Procedure Types: {Object.keys(analyticsData.procedureTypeDistribution).length}</p>\r\n                        <p>Students: {Object.keys(analyticsData.studentPerformance).length}</p>\r\n                        <p>Trends: {analyticsData.reviewTrends.length} months</p>\r\n                      </div>\r\n                      <div className=\"flex gap-2\">\r\n                        <button\r\n                          onClick={() => setAnalyticsData(generateSampleData())}\r\n                          className=\"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600\"\r\n                        >\r\n                          Load Sample Data\r\n                        </button>\r\n                        <button\r\n                          onClick={() => calculateAnalytics(allReviews)}\r\n                          className=\"px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600\"\r\n                        >\r\n                          Recalculate Analytics\r\n                        </button>\r\n                        <button\r\n                          onClick={testCharts}\r\n                          className=\"px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600\"\r\n                        >\r\n                          Test Charts\r\n                        </button>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n\r\n                  {/* Analytics Content */}\r\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n                    {/* Status Distribution Chart */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.1 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Review Status Distribution</h3>\r\n                      <div className=\"h-64 flex items-center justify-center\">\r\n                        {analyticsData.statusDistribution && Object.values(analyticsData.statusDistribution).some(val => val > 0) ? (\r\n                          <Pie\r\n                            data={{\r\n                              labels: ['Accepted', 'Pending', 'Denied'],\r\n                              datasets: [\r\n                                {\r\n                                  data: [\r\n                                    analyticsData.statusDistribution.accepted || 0,\r\n                                    analyticsData.statusDistribution.pending || 0,\r\n                                    analyticsData.statusDistribution.denied || 0\r\n                                  ],\r\n                                  backgroundColor: [\r\n                                    `${colorPalette.accent}B3`,  // accent with opacity\r\n                                    `${colorPalette.primary}B3`, // primary with opacity\r\n                                    'rgba(239, 68, 68, 0.7)'     // red\r\n                                  ],\r\n                                  borderColor: [\r\n                                    colorPalette.accent,\r\n                                    colorPalette.primary,\r\n                                    'rgba(239, 68, 68, 1)'\r\n                                  ],\r\n                                  borderWidth: 1,\r\n                                }\r\n                              ]\r\n                            }}\r\n                            options={{\r\n                              responsive: true,\r\n                              maintainAspectRatio: false,\r\n                              plugins: {\r\n                                legend: {\r\n                                  position: 'bottom',\r\n                                  labels: {\r\n                                    usePointStyle: true,\r\n                                    padding: 20,\r\n                                    font: {\r\n                                      size: 12\r\n                                    }\r\n                                  }\r\n                                },\r\n                                tooltip: {\r\n                                  callbacks: {\r\n                                    label: function(context) {\r\n                                      const label = context.label || '';\r\n                                      const value = context.raw || 0;\r\n                                      const total = context.dataset.data.reduce((a, b) => a + b, 0);\r\n                                      const percentage = total > 0 ? Math.round((value / total) * 100) : 0;\r\n                                      return `${label}: ${value} (${percentage}%)`;\r\n                                    }\r\n                                  }\r\n                                }\r\n                              }\r\n                            }}\r\n                            onRender={() => console.log('Pie chart rendered successfully')}\r\n                            onError={(error) => console.error('Pie chart error:', error)}\r\n                          />\r\n                        ) : (\r\n                          <div className=\"text-center text-gray-500\">\r\n                            <svg className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                            </svg>\r\n                            <p>No review data available</p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Procedure Type Distribution */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.2 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Procedure Type Distribution</h3>\r\n                      <div className=\"h-64 flex items-center justify-center\">\r\n                        {analyticsData.procedureTypeDistribution && Object.keys(analyticsData.procedureTypeDistribution).length > 0 ? (\r\n                          <Bar\r\n                            data={{\r\n                              labels: Object.keys(analyticsData.procedureTypeDistribution),\r\n                              datasets: [\r\n                                {\r\n                                  label: 'Accepted',\r\n                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.accepted || 0),\r\n                                  backgroundColor: `${colorPalette.accent}B3`,\r\n                                  borderColor: colorPalette.accent,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Pending',\r\n                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.pending || 0),\r\n                                  backgroundColor: `${colorPalette.primary}B3`,\r\n                                  borderColor: colorPalette.primary,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Denied',\r\n                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.denied || 0),\r\n                                  backgroundColor: 'rgba(239, 68, 68, 0.7)',\r\n                                  borderColor: 'rgba(239, 68, 68, 1)',\r\n                                  borderWidth: 1,\r\n                                }\r\n                              ]\r\n                            }}\r\n                            options={{\r\n                              responsive: true,\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                x: {\r\n                                  stacked: true,\r\n                                  ticks: {\r\n                                    maxRotation: 45,\r\n                                    minRotation: 0\r\n                                  }\r\n                                },\r\n                                y: {\r\n                                  stacked: true,\r\n                                  beginAtZero: true,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                }\r\n                              },\r\n                              plugins: {\r\n                                legend: {\r\n                                  position: 'bottom',\r\n                                  labels: {\r\n                                    usePointStyle: true,\r\n                                    padding: 20,\r\n                                    font: {\r\n                                      size: 12\r\n                                    }\r\n                                  }\r\n                                },\r\n                                tooltip: {\r\n                                  mode: 'index',\r\n                                  intersect: false\r\n                                }\r\n                              }\r\n                            }}\r\n                          />\r\n                        ) : (\r\n                          <div className=\"text-center text-gray-500\">\r\n                            <svg className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                            </svg>\r\n                            <p>No procedure data available</p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Student Performance */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.3 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Student Performance</h3>\r\n                      <div className=\"overflow-x-auto\">\r\n                        <table className=\"min-w-full divide-y divide-gray-200\">\r\n                          <thead className=\"bg-gray-50\">\r\n                            <tr>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Student\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Reviews\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Acceptance Rate\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Avg. Quality\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Avg. Interaction\r\n                              </th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {analyticsData.studentPerformance && Object.keys(analyticsData.studentPerformance).length > 0 ? (\r\n                              Object.entries(analyticsData.studentPerformance).map(([student, data], index) => (\r\n                                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                    {student}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.total || 0}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.total > 0 ? `${Math.round((data.accepted / data.total) * 100)}%` : 'N/A'}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.avgProcedureQuality > 0 ? (\r\n                                      <div className=\"flex items-center\">\r\n                                        <span className=\"mr-2\">{data.avgProcedureQuality}</span>\r\n                                        {renderStars(parseFloat(data.avgProcedureQuality))}\r\n                                      </div>\r\n                                    ) : 'N/A'}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.avgPatientInteraction > 0 ? (\r\n                                      <div className=\"flex items-center\">\r\n                                        <span className=\"mr-2\">{data.avgPatientInteraction}</span>\r\n                                        {renderStars(parseFloat(data.avgPatientInteraction))}\r\n                                      </div>\r\n                                    ) : 'N/A'}\r\n                                  </td>\r\n                                </tr>\r\n                              ))\r\n                            ) : (\r\n                              <tr>\r\n                                <td colSpan=\"5\" className=\"px-6 py-4 text-center text-sm text-gray-500\">\r\n                                  <div className=\"flex flex-col items-center\">\r\n                                    <svg className=\"h-12 w-12 text-gray-300 mb-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\r\n                                    </svg>\r\n                                    No student performance data available\r\n                                  </div>\r\n                                </td>\r\n                              </tr>\r\n                            )}\r\n                          </tbody>\r\n                        </table>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Review Trends */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.4 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Review Trends</h3>\r\n                      <div className=\"h-64 flex items-center justify-center\">\r\n                        {analyticsData.reviewTrends && analyticsData.reviewTrends.length > 0 ? (\r\n                          <Bar\r\n                            data={{\r\n                              labels: analyticsData.reviewTrends.map(t => t.month),\r\n                              datasets: [\r\n                                {\r\n                                  label: 'Accepted',\r\n                                  data: analyticsData.reviewTrends.map(t => t.accepted || 0),\r\n                                  backgroundColor: `${colorPalette.accent}B3`,\r\n                                  borderColor: colorPalette.accent,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Pending',\r\n                                  data: analyticsData.reviewTrends.map(t => t.pending || 0),\r\n                                  backgroundColor: `${colorPalette.primary}B3`,\r\n                                  borderColor: colorPalette.primary,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Denied',\r\n                                  data: analyticsData.reviewTrends.map(t => t.denied || 0),\r\n                                  backgroundColor: 'rgba(239, 68, 68, 0.7)',\r\n                                  borderColor: 'rgba(239, 68, 68, 1)',\r\n                                  borderWidth: 1,\r\n                                }\r\n                              ]\r\n                            }}\r\n                            options={{\r\n                              responsive: true,\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                x: {\r\n                                  stacked: true,\r\n                                  ticks: {\r\n                                    maxRotation: 45,\r\n                                    minRotation: 0\r\n                                  }\r\n                                },\r\n                                y: {\r\n                                  stacked: true,\r\n                                  beginAtZero: true,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                }\r\n                              },\r\n                              plugins: {\r\n                                legend: {\r\n                                  position: 'bottom',\r\n                                  labels: {\r\n                                    usePointStyle: true,\r\n                                    padding: 20,\r\n                                    font: {\r\n                                      size: 12\r\n                                    }\r\n                                  }\r\n                                },\r\n                                tooltip: {\r\n                                  mode: 'index',\r\n                                  intersect: false\r\n                                }\r\n                              }\r\n                            }}\r\n                          />\r\n                        ) : (\r\n                          <div className=\"text-center text-gray-500\">\r\n                            <svg className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\" />\r\n                            </svg>\r\n                            <p>No trend data available</p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n                  </div>\r\n\r\n                  {/* Quality Metrics Summary */}\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.5 }}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6\"\r\n                  >\r\n                    <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Quality Metrics Summary</h3>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                      <div className=\"p-4 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                        <h4 className=\"text-md font-medium mb-2\" style={{ color: colorPalette.primary }}>Average Procedure Quality</h4>\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"text-3xl font-bold mr-4\" style={{ color: colorPalette.primary }}>\r\n                            {analyticsData.qualityMetrics.avgProcedureQuality}/5\r\n                          </div>\r\n                          <div className=\"flex\">\r\n                            {renderStars(parseFloat(analyticsData.qualityMetrics.avgProcedureQuality))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"p-4 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                        <h4 className=\"text-md font-medium mb-2\" style={{ color: colorPalette.primary }}>Average Patient Interaction</h4>\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"text-3xl font-bold mr-4\" style={{ color: colorPalette.primary }}>\r\n                            {analyticsData.qualityMetrics.avgPatientInteraction}/5\r\n                          </div>\r\n                          <div className=\"flex\">\r\n                            {renderStars(parseFloat(analyticsData.qualityMetrics.avgPatientInteraction))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Reviews Table - Only show when in reviews tab */}\r\n              {mainTab === 'reviews' && (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  whileInView=\"show\"\r\n                  viewport={{ once: true }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\r\n                >\r\n                  <div className=\"p-6\">\r\n                    <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\r\n                      <h2 className=\"text-xl font-bold\" style={{ color: colorPalette.primary }}>\r\n                        {reviewsTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'}\r\n                      </h2>\r\n                      <div className=\"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\">\r\n                        <div className=\"relative\">\r\n                          <input\r\n                            type=\"text\"\r\n                            placeholder=\"Search by patient or student name\"\r\n                            value={searchQuery}\r\n                            onChange={(e) => setSearchQuery(e.target.value)}\r\n                            className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          />\r\n                          <FaSearch className=\"absolute left-3 top-3 text-gray-400\" />\r\n                        </div>\r\n\r\n                        {/* Additional filters */}\r\n                        <div className=\"flex gap-2\">\r\n                          <button\r\n                            onClick={() => document.getElementById('filterDropdown').classList.toggle('hidden')}\r\n                            className=\"px-3 py-2 border border-gray-300 rounded-lg flex items-center\"\r\n                            style={{\r\n                              borderColor: '#e5e7eb',\r\n                              color: colorPalette.text,\r\n                              transition: 'all 0.2s ease'\r\n                            }}\r\n                            onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f9fafb')}\r\n                            onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}\r\n                          >\r\n                            <FaFilter className=\"mr-2 text-gray-500\" /> Filters\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Filter dropdown */}\r\n                    <div id=\"filterDropdown\" className=\"mb-6 p-4 border border-gray-200 rounded-lg hidden\" style={{ backgroundColor: `${colorPalette.primary}05` }}>\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Date Range</label>\r\n                          <select\r\n                            value={dayFilter}\r\n                            onChange={(e) => setDayFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"all\">All Dates</option>\r\n                            <option value=\"today\">Today</option>\r\n                            <option value=\"tomorrow\">Tomorrow</option>\r\n                            <option value=\"week\">This Week</option>\r\n                            <option value=\"month\">This Month</option>\r\n                          </select>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Time</label>\r\n                          <select\r\n                            value={hourFilter}\r\n                            onChange={(e) => setHourFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"\">All Hours</option>\r\n                            <option value=\"09:00\">09:00 AM</option>\r\n                            <option value=\"10:00\">10:00 AM</option>\r\n                            <option value=\"11:00\">11:00 AM</option>\r\n                            <option value=\"12:00\">12:00 PM</option>\r\n                            <option value=\"13:00\">01:00 PM</option>\r\n                            <option value=\"14:00\">02:00 PM</option>\r\n                            <option value=\"15:00\">03:00 PM</option>\r\n                          </select>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Procedure Type</label>\r\n                          <select\r\n                            value={procedureFilter}\r\n                            onChange={(e) => setProcedureFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"all\">All Procedures</option>\r\n                            {uniqueProcedures.map(procedure => (\r\n                              <option key={procedure} value={procedure}>{procedure}</option>\r\n                            ))}\r\n                          </select>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Student</label>\r\n                          <select\r\n                            value={studentFilter}\r\n                            onChange={(e) => setStudentFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"all\">All Students</option>\r\n                            {uniqueStudents.map(student => (\r\n                              <option key={student} value={student}>{student}</option>\r\n                            ))}\r\n                          </select>\r\n                        </div>\r\n\r\n                        {/* Status filter - only show for done reviews */}\r\n                        {reviewsTab === 'done' && (\r\n                          <div>\r\n                            <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Status</label>\r\n                            <select\r\n                              value={statusFilter}\r\n                              onChange={(e) => setStatusFilter(e.target.value)}\r\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                              style={{\r\n                                outline: 'none',\r\n                                boxShadow: 'none',\r\n                                borderColor: '#e5e7eb',\r\n                                ':focus': { borderColor: colorPalette.primary }\r\n                              }}\r\n                            >\r\n                              <option value=\"all\">All Statuses</option>\r\n                              <option value=\"accepted\">Accepted</option>\r\n                              <option value=\"denied\">Denied</option>\r\n                            </select>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead style={{ backgroundColor: `${colorPalette.primary}05` }}>\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Date</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Patient</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Student</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Procedure</th>\r\n                          {reviewsTab === 'done' && (\r\n                            <>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Status</th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Supervisor</th>\r\n                            </>\r\n                          )}\r\n                          {reviewsTab === 'pending' && (\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Actions</th>\r\n                          )}\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? (\r\n                          <tr>\r\n                            <td colSpan={reviewsTab === 'pending' ? 5 : 6} className=\"px-6 py-8 text-center\">\r\n                              <div className=\"flex flex-col items-center justify-center\">\r\n                                <svg className=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                                </svg>\r\n                                <h3 className=\"text-lg font-medium text-gray-900\">\r\n                                  No {reviewsTab === 'pending' ? 'pending' : 'completed'} reviews\r\n                                </h3>\r\n                                <p className=\"mt-1 text-gray-500\">\r\n                                  {error\r\n                                    ? 'Failed to load reviews due to an error. Check your permissions or try logging in again.'\r\n                                    : reviewsTab === 'pending'\r\n                                      ? 'No pending reviews are available. Check back later or ensure student reviews have been submitted.'\r\n                                      : 'No completed reviews found. Try adjusting the filters or review pending submissions.'}\r\n                                </p>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ) : (\r\n                          filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).map((review) => (\r\n                            <motion.tr\r\n                              key={review._id}\r\n                              initial={{ opacity: 0 }}\r\n                              animate={{ opacity: 1 }}\r\n                              className=\"hover:bg-gray-50 cursor-pointer\"\r\n                              onClick={() => setSelectedReview(review)}\r\n                            >\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                {new Date(reviewsTab === 'pending' ? review.submittedDate : review.reviewedDate || review.submittedDate).toLocaleDateString()}\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.patientId?.fullName || 'N/A'}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.studentName || 'N/A'}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.procedureType || 'N/A'}</td>\r\n                              {reviewsTab === 'done' && (\r\n                                <>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <span\r\n                                      className=\"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n                                      style={{\r\n                                        backgroundColor: review.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\r\n                                        color: review.status === 'accepted' ? colorPalette.accent : '#b91c1c'\r\n                                      }}\r\n                                    >\r\n                                      {review.status}\r\n                                    </span>\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.supervisorName || 'N/A'}</td>\r\n                                </>\r\n                              )}\r\n                              {reviewsTab === 'pending' && (\r\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm\">\r\n                                  <button\r\n                                    onClick={() => setSelectedReview(review)}\r\n                                    style={{ color: colorPalette.primary }}\r\n                                    className=\"hover:underline\"\r\n                                  >\r\n                                    Review\r\n                                  </button>\r\n                                </td>\r\n                              )}\r\n                            </motion.tr>\r\n                          ))\r\n                        )}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      {selectedReview && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>Review Details</h2>\r\n                <button onClick={() => setSelectedReview(null)} className=\"text-gray-400 hover:text-gray-500\">\r\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"space-y-6\">\r\n                {/* Patient Information */}\r\n                <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center\" style={{ color: colorPalette.primary }}>\r\n                    <FaUserMd className=\"h-5 w-5 mr-2\" style={{ color: colorPalette.primary }} />\r\n                    Patient Information\r\n                  </h3>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Name</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.patientId?.fullName || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.patientId?.nationalId || 'N/A'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Review Details */}\r\n                <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center\" style={{ color: colorPalette.primary }}>\r\n                    <FaCalendarAlt className=\"h-5 w-5 mr-2\" style={{ color: colorPalette.primary }} />\r\n                    Review Details\r\n                  </h3>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Student</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.studentName || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Student ID</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{typeof selectedReview.studentId === 'object' ? selectedReview.studentId.studentId : selectedReview.studentId || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Procedure</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.procedureType || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Submission Date</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">\r\n                        {selectedReview.submittedDate ? new Date(selectedReview.submittedDate).toLocaleString() : 'N/A'}\r\n                      </p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Student Comment</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.comment || 'No comment'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Review Steps */}\r\n                <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center\" style={{ color: colorPalette.primary }}>\r\n                    <FaListAlt className=\"h-5 w-5 mr-2\" style={{ color: colorPalette.primary }} />\r\n                    Procedure Steps\r\n                  </h3>\r\n                  <ReviewStepsDisplay\r\n                    reviewSteps={selectedReview.reviewSteps || []}\r\n                    procedureType={selectedReview.procedureType}\r\n                  />\r\n                </div>\r\n\r\n                {/* Supervisor Review Form (for pending reviews) */}\r\n                {selectedReview.status === 'pending' && (\r\n                  <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                    <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Submit Review</h3>\r\n                    <div className=\"space-y-4\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Procedure Quality</h4>\r\n                        {renderStars(reviewForm.procedureQuality, (rating) =>\r\n                          setReviewForm({ ...reviewForm, procedureQuality: rating })\r\n                        )}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Patient Interaction</h4>\r\n                        {renderStars(reviewForm.patientInteraction, (rating) =>\r\n                          setReviewForm({ ...reviewForm, patientInteraction: rating })\r\n                        )}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Comment</h4>\r\n                        <textarea\r\n                          value={reviewForm.comment}\r\n                          onChange={(e) => setReviewForm({ ...reviewForm, comment: e.target.value })}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg\"\r\n                          style={{\r\n                            outline: 'none',\r\n                            boxShadow: 'none',\r\n                            borderColor: '#e5e7eb',\r\n                            ':focus': { borderColor: colorPalette.primary }\r\n                          }}\r\n                          rows=\"4\"\r\n                          placeholder=\"Add any comments or feedback\"\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Review Decision</h4>\r\n                        <div className=\"flex gap-4 mt-2\">\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => setReviewForm({ ...reviewForm, status: 'accepted' })}\r\n                            className=\"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\"\r\n                            style={{\r\n                              backgroundColor: reviewForm.status === 'accepted'\r\n                                ? colorPalette.accent\r\n                                : '#f3f4f6',\r\n                              color: reviewForm.status === 'accepted'\r\n                                ? '#ffffff'\r\n                                : colorPalette.text,\r\n                              fontWeight: reviewForm.status === 'accepted' ? '500' : 'normal',\r\n                              border: reviewForm.status === 'accepted'\r\n                                ? `2px solid ${colorPalette.accent}`\r\n                                : 'none'\r\n                            }}\r\n                          >\r\n                            <FaCheck className=\"mr-2\" style={{\r\n                              color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.accent\r\n                            }} />\r\n                            Accept\r\n                          </button>\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => setReviewForm({ ...reviewForm, status: 'denied' })}\r\n                            className=\"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\"\r\n                            style={{\r\n                              backgroundColor: reviewForm.status === 'denied'\r\n                                ? '#ef4444'\r\n                                : '#f3f4f6',\r\n                              color: reviewForm.status === 'denied'\r\n                                ? '#ffffff'\r\n                                : colorPalette.text,\r\n                              fontWeight: reviewForm.status === 'denied' ? '500' : 'normal',\r\n                              border: reviewForm.status === 'denied'\r\n                                ? '2px solid #dc2626'\r\n                                : 'none'\r\n                            }}\r\n                          >\r\n                            <FaTimes className=\"mr-2\" style={{\r\n                              color: reviewForm.status === 'denied' ? '#ffffff' : '#ef4444'\r\n                            }} />\r\n                            Decline\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Signature Section */}\r\n                      <div className=\"mt-6 border-t pt-4 border-gray-200\">\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Signature</h4>\r\n\r\n                        {savedSignature ? (\r\n                          <div className=\"mb-4\">\r\n                            <div className=\"flex justify-between items-center mb-3\">\r\n                              <p className=\"text-sm text-gray-600\">You have a saved signature</p>\r\n                              <motion.button\r\n                                whileHover={{ scale: 1.05 }}\r\n                                whileTap={{ scale: 0.95 }}\r\n                                onClick={() => setReviewForm(prev => ({ ...prev, supervisorSignature: savedSignature }))}\r\n                                className=\"px-4 py-2 text-white rounded-lg flex items-center\"\r\n                                style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}\r\n                              >\r\n                                <FaSignature className=\"mr-2\" /> Sign\r\n                              </motion.button>\r\n                            </div>\r\n\r\n                            {reviewForm.supervisorSignature && (\r\n                              <div className=\"border rounded-lg p-4\"\r\n                                style={{\r\n                                  borderColor: '#e5e7eb',\r\n                                  backgroundColor: `${colorPalette.primary}05`\r\n                                }}>\r\n                                <h4 className=\"text-sm font-medium mb-2\" style={{ color: colorPalette.primary }}>Signature Preview</h4>\r\n                                {reviewForm.supervisorSignature.startsWith('data:image') ? (\r\n                                  <img src={reviewForm.supervisorSignature} alt=\"Signature\" className=\"max-h-16 mx-auto\" />\r\n                                ) : (\r\n                                  <p className=\"font-signature text-lg text-center\">{reviewForm.supervisorSignature}</p>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"p-4 rounded-lg mb-4\"\r\n                            style={{\r\n                              backgroundColor: `${colorPalette.secondary}10`,\r\n                              border: `1px solid ${colorPalette.secondary}30`\r\n                            }}>\r\n                            <p className=\"text-sm mb-2\" style={{ color: colorPalette.secondary }}>\r\n                              You don't have a saved signature. Please create one first.\r\n                            </p>\r\n                            <motion.button\r\n                              whileHover={{ scale: 1.05 }}\r\n                              whileTap={{ scale: 0.95 }}\r\n                              onClick={() => setShowSignatureModal(true)}\r\n                              className=\"px-4 py-2 text-white rounded-lg flex items-center text-sm\"\r\n                              style={{ backgroundColor: colorPalette.secondary }}\r\n                            >\r\n                              <FaSignature className=\"mr-2\" /> Create Signature\r\n                            </motion.button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"flex justify-end gap-4 mt-6\">\r\n                        <motion.button\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                          onClick={() => handleReviewSubmit(selectedReview._id)}\r\n                          disabled={reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature}\r\n                          className=\"px-6 py-3 rounded-lg flex items-center shadow-md\"\r\n                          style={{\r\n                            backgroundColor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? '#d1d5db'\r\n                              : 'transparent',\r\n                            background: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? '#d1d5db'\r\n                              : reviewForm.status === 'accepted'\r\n                                ? `linear-gradient(to right, ${colorPalette.accent}, ${colorPalette.accent})`\r\n                                : 'linear-gradient(to right, #ef4444, #b91c1c)',\r\n                            color: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? '#6b7280'\r\n                              : '#ffffff',\r\n                            cursor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? 'not-allowed'\r\n                              : 'pointer'\r\n                          }}\r\n                        >\r\n                          {reviewForm.status === 'accepted' ? (\r\n                            <FaCheck className=\"h-5 w-5 mr-2\" />\r\n                          ) : (\r\n                            <FaTimes className=\"h-5 w-5 mr-2\" />\r\n                          )}\r\n                          Submit {reviewForm.status === 'accepted' ? 'Approval' : 'Decline'}\r\n                        </motion.button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Display Supervisor Review (for done reviews) */}\r\n                {selectedReview.status !== 'pending' && (\r\n                  <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                    <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Supervisor Review</h3>\r\n                    <div className=\"space-y-4\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Supervisor</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.supervisorName || 'N/A'}</p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Reviewed Date</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">\r\n                          {selectedReview.reviewedDate ? new Date(selectedReview.reviewedDate).toLocaleString() : 'N/A'}\r\n                        </p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Procedure Quality</h4>\r\n                        {renderStars(selectedReview.procedureQuality || 0)}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Patient Interaction</h4>\r\n                        {renderStars(selectedReview.patientInteraction || 0)}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Comment</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.comment || 'No comment'}</p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Status</h4>\r\n                        <span\r\n                          className=\"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n                          style={{\r\n                            backgroundColor: selectedReview.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\r\n                            color: selectedReview.status === 'accepted' ? colorPalette.accent : '#b91c1c'\r\n                          }}\r\n                        >\r\n                          {selectedReview.status}\r\n                        </span>\r\n                      </div>\r\n\r\n                      {/* Signature Display */}\r\n                      <div className=\"mt-4 pt-4 border-t border-gray-200\">\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Signature</h4>\r\n                        {selectedReview.supervisorSignature ? (\r\n                          <div className=\"border rounded-lg p-4\" style={{\r\n                            borderColor: '#e5e7eb',\r\n                            backgroundColor: colorPalette.background\r\n                          }}>\r\n                            {selectedReview.supervisorSignature.startsWith('data:image') ? (\r\n                              <img src={selectedReview.supervisorSignature} alt=\"Signature\" className=\"max-h-20\" />\r\n                            ) : (\r\n                              <p className=\"font-signature text-lg\">{selectedReview.supervisorSignature}</p>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <p className=\"text-sm text-gray-500\">No signature provided</p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Signature Modal */}\r\n      {showSignatureModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n            className=\"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6 rounded-t-xl\" style={{\r\n              background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\r\n            }}>\r\n              <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-xl font-bold text-white flex items-center\">\r\n                  <FaSignature className=\"mr-3 h-5 w-5\" />\r\n                  Manage Your Signature\r\n                </h2>\r\n                <button\r\n                  onClick={() => setShowSignatureModal(false)}\r\n                  className=\"text-white hover:text-blue-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-6\">\r\n              <div className=\"p-4 rounded-lg mb-6 border-l-4\"\r\n                style={{\r\n                  backgroundColor: `${colorPalette.primary}10`,\r\n                  borderColor: colorPalette.primary\r\n                }}>\r\n                <p style={{ color: colorPalette.primary }}>\r\n                  Create or update your signature below. This signature will be used for all your review approvals.\r\n                </p>\r\n              </div>\r\n\r\n              <SignatureManager\r\n                initialSignature={savedSignature}\r\n                onSignatureSelect={(signature) => {\r\n                  setSavedSignature(signature);\r\n                  // Close modal after a short delay to show success\r\n                  setTimeout(() => setShowSignatureModal(false), 1500);\r\n                }}\r\n              />\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Success Modal */}\r\n      {showSuccessModal && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -50 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: 50 }}\r\n          className=\"fixed top-0 inset-x-0 flex justify-center items-start z-50 pt-6 px-4\"\r\n        >\r\n          <div className=\"bg-white rounded-lg shadow-xl border-l-4 p-4 flex items-center max-w-md w-full\"\r\n            style={{ borderColor: colorPalette.accent }}>\r\n            <div className=\"p-2 rounded-full mr-4\" style={{ backgroundColor: `${colorPalette.accent}20` }}>\r\n              <FaCheck className=\"text-xl\" style={{ color: colorPalette.accent }} />\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <h3 className=\"font-medium\" style={{ color: colorPalette.text }}>Success!</h3>\r\n              <p style={{ color: '#6b7280' }}>{successMessage}</p>\r\n            </div>\r\n            <button\r\n              onClick={() => setShowSuccessModal(false)}\r\n              className=\"text-gray-400 hover:text-gray-500\"\r\n            >\r\n              <FaTimes />\r\n            </button>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupervisorDashboard;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EACjDC,UAAU,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAC3DC,UAAU,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,WAAW,EACzDC,eAAe,EAAEC,SAAS,EAAEC,aAAa,QACpC,gBAAgB;AACvB,SAASC,KAAK,IAAIC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,QAAQ,UAAU;AACvH,SAASC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;;AAE1C;AACA,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,kBAAkB,MAAM,sBAAsB;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;;AAED;AACArB,OAAO,CAACsB,QAAQ,CAACrB,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,CAAC;;AAE5F;AACAP,OAAO,CAACuB,QAAQ,CAACC,IAAI,CAACC,MAAM,GAAG,8BAA8B;AAC7DzB,OAAO,CAACuB,QAAQ,CAACC,IAAI,CAACE,IAAI,GAAG,EAAE;AAC/B1B,OAAO,CAACuB,QAAQ,CAACI,KAAK,GAAG,SAAS;AAClC3B,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACC,OAAO,CAACC,eAAe,GAAG,oBAAoB;AACvE9B,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACC,OAAO,CAACE,UAAU,GAAG,SAAS;AACvD/B,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACC,OAAO,CAACG,SAAS,GAAG,SAAS;AACtDhC,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACK,MAAM,CAACC,MAAM,CAACC,aAAa,GAAG,IAAI;AAC3DnC,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACK,MAAM,CAACC,MAAM,CAACE,OAAO,GAAG,EAAE;AAEnD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAChC,MAAMC,QAAQ,GAAGjE,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkE,IAAI;IAAEC;EAAM,CAAC,GAAGjE,OAAO,CAAC,CAAC;EACjC,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC,SAAS,CAAC;EACjD,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACkG,cAAc,EAAEC,iBAAiB,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwG,cAAc,EAAEC,iBAAiB,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3G,QAAQ,CAAC,OAAO,CAAC;EACrE,MAAM,CAAC4G,aAAa,EAAEC,gBAAgB,CAAC,GAAG7G,QAAQ,CAAC;IACjD8G,kBAAkB,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC1DC,yBAAyB,EAAE,CAAC,CAAC;IAC7BC,kBAAkB,EAAE,CAAC,CAAC;IACtBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE;MACdC,mBAAmB,EAAE,CAAC;MACtBC,qBAAqB,EAAE;IACzB;EACF,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzH,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC4H,UAAU,EAAEC,aAAa,CAAC,GAAG7H,QAAQ,CAAC;IAC3C8H,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,UAAU;IAClBC,mBAAmB,EAAE;EACvB,CAAC,CAAC;;EAIF;EACAjI,SAAS,CAAC,MAAM;IACd,MAAMkI,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QAAEjE,IAAI;QAAEC;MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACD,IAAI,IAAI,CAACC,KAAK,IAAID,IAAI,CAACkE,IAAI,KAAK,YAAY,EAAE;QACjD7D,QAAQ,CAAC,uDAAuD,CAAC;QACjEN,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,IAAI;QACFI,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMgE,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUpE,KAAK;UAAG;QAAE,CAAC;;QAEhE;QACA+D,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,MAAMK,aAAa,GAAG,MAAMvI,KAAK,CAACwI,GAAG,CAAC,8CAA8C,EAAEJ,MAAM,CAAC;QAC7FH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,aAAa,CAACE,IAAI,CAAC;QAExD,MAAMC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACL,aAAa,CAACE,IAAI,CAAC,GAAGF,aAAa,CAACE,IAAI,GAAG,EAAE;QAC3E7D,aAAa,CAAC8D,OAAO,CAAC;;QAEtB;QACAlE,iBAAiB,CAACkE,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC,CAAC;QAC9DpD,cAAc,CAACgE,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC,CAAC;;QAE3D;QACA,MAAMiB,QAAQ,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACN,OAAO,CAACO,GAAG,CAACH,CAAC;UAAA,IAAAI,YAAA;UAAA,OAAI,EAAAA,YAAA,GAAAJ,CAAC,CAACK,SAAS,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,IAAI,KAAIN,CAAC,CAACO,WAAW;QAAA,EAAC,CAAC,CAAC,CAACR,MAAM,CAACS,OAAO,CAAC;QACnG,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAIP,GAAG,CAACN,OAAO,CAACO,GAAG,CAACH,CAAC,IAAIA,CAAC,CAACU,aAAa,CAAC,CAAC,CAAC,CAACX,MAAM,CAACS,OAAO,CAAC;QAElFhC,iBAAiB,CAACyB,QAAQ,CAAC;QAC3BvB,mBAAmB,CAAC+B,UAAU,CAAC;;QAE/B;QACA,IAAI;UACF,MAAME,YAAY,GAAG,MAAMzJ,KAAK,CAACwI,GAAG,CAAC,iDAAiD,EAAEJ,MAAM,CAAC;UAC/F,IAAIqB,YAAY,CAAChB,IAAI,IAAIgB,YAAY,CAAChB,IAAI,CAACiB,SAAS,EAAE;YACpD1D,iBAAiB,CAACyD,YAAY,CAAChB,IAAI,CAACiB,SAAS,CAAC;UAChD;QACF,CAAC,CAAC,OAAOC,YAAY,EAAE;UACrB1B,OAAO,CAAC5D,KAAK,CAAC,2BAA2B,EAAEsF,YAAY,CAAC;UACxD;QACF;;QAEA;QACA,IAAI;UACF1B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE3B,kBAAkB,CAAC;UAC1E,MAAMqD,YAAY,GAAG,MAAM5J,KAAK,CAACwI,GAAG,CAAC,yDAAyDjC,kBAAkB,EAAE,EAAE6B,MAAM,CAAC;UAC3HH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0B,YAAY,CAACnB,IAAI,CAAC;UAErD,IAAImB,YAAY,CAACnB,IAAI,EAAE;YAAA,IAAAoB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACrB;YACA,MAAMxD,aAAa,GAAG;cACpBE,kBAAkB,EAAE;gBAClBC,QAAQ,EAAE,EAAAiD,qBAAA,GAAAD,YAAY,CAACnB,IAAI,CAAC9B,kBAAkB,cAAAkD,qBAAA,uBAApCA,qBAAA,CAAsCjD,QAAQ,KAAI,CAAC;gBAC7DC,OAAO,EAAE,EAAAiD,sBAAA,GAAAF,YAAY,CAACnB,IAAI,CAAC9B,kBAAkB,cAAAmD,sBAAA,uBAApCA,sBAAA,CAAsCjD,OAAO,KAAI,CAAC;gBAC3DC,MAAM,EAAE,EAAAiD,sBAAA,GAAAH,YAAY,CAACnB,IAAI,CAAC9B,kBAAkB,cAAAoD,sBAAA,uBAApCA,sBAAA,CAAsCjD,MAAM,KAAI;cAC1D,CAAC;cACDC,yBAAyB,EAAE6C,YAAY,CAACnB,IAAI,CAAC1B,yBAAyB,IAAI,CAAC,CAAC;cAC5EC,kBAAkB,EAAE4C,YAAY,CAACnB,IAAI,CAACzB,kBAAkB,IAAI,CAAC,CAAC;cAC9DC,YAAY,EAAE2C,YAAY,CAACnB,IAAI,CAACxB,YAAY,IAAI,EAAE;cAClDC,cAAc,EAAE;gBACdC,mBAAmB,EAAE,EAAA6C,qBAAA,GAAAJ,YAAY,CAACnB,IAAI,CAACvB,cAAc,cAAA8C,qBAAA,uBAAhCA,qBAAA,CAAkC7C,mBAAmB,KAAI,CAAC;gBAC/EC,qBAAqB,EAAE,EAAA6C,sBAAA,GAAAL,YAAY,CAACnB,IAAI,CAACvB,cAAc,cAAA+C,sBAAA,uBAAhCA,sBAAA,CAAkC7C,qBAAqB,KAAI;cACpF;YACF,CAAC;YACDV,gBAAgB,CAACD,aAAa,CAAC;UACjC;QACF,CAAC,CAAC,OAAOyD,YAAY,EAAE;UACrBjC,OAAO,CAAC5D,KAAK,CAAC,2BAA2B,EAAE6F,YAAY,CAAC;UACxDjC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtD;UACA,IAAIQ,OAAO,CAACyB,MAAM,GAAG,CAAC,EAAE;YACtBC,kBAAkB,CAAC1B,OAAO,CAAC;UAC7B,CAAC,MAAM;YACL;YACAT,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;YAC3DxB,gBAAgB,CAAC2D,kBAAkB,CAAC,CAAC,CAAC;UACxC;QACF;QAEAjG,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOkG,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,cAAA;QACZ5C,OAAO,CAAC5D,KAAK,CAAC,cAAc,EAAE;UAC5ByG,OAAO,EAAER,GAAG,CAACQ,OAAO;UACpBhD,MAAM,GAAAyC,aAAA,GAAED,GAAG,CAACS,QAAQ,cAAAR,aAAA,uBAAZA,aAAA,CAAczC,MAAM;UAC5BW,IAAI,GAAA+B,cAAA,GAAEF,GAAG,CAACS,QAAQ,cAAAP,cAAA,uBAAZA,cAAA,CAAc/B,IAAI;UACxBJ,OAAO,GAAAoC,cAAA,GAAEH,GAAG,CAACS,QAAQ,cAAAN,cAAA,uBAAZA,cAAA,CAAcpC;QACzB,CAAC,CAAC;QACF,MAAM2C,YAAY,GAAG,EAAAN,cAAA,GAAAJ,GAAG,CAACS,QAAQ,cAAAL,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,MAC3B,EAAAF,cAAA,GAAAN,GAAG,CAACS,QAAQ,cAAAH,cAAA,uBAAZA,cAAA,CAAc9C,MAAM,MAAK,GAAG,GAAG,yCAAyC,GACzE,2CAA2C,CAAC;QAChExD,QAAQ,CAAC0G,YAAY,CAAC;QACtB,IAAI,EAAAH,cAAA,GAAAP,GAAG,CAACS,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAc/C,MAAM,MAAK,GAAG,EAAE;UAChCmD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;UAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;UAC/BlH,QAAQ,CAAC,QAAQ,CAAC;QACpB;QACAQ,iBAAiB,CAAC,EAAE,CAAC;QACrBE,cAAc,CAAC,EAAE,CAAC;QAClBE,aAAa,CAAC,EAAE,CAAC;QACjBR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACD4D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC/D,IAAI,EAAEC,KAAK,EAAEF,QAAQ,EAAEuC,kBAAkB,CAAC,CAAC;;EAE/C;EACAzG,SAAS,CAAC,MAAM;IACdmI,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;MACrCvB,kBAAkB,EAAEF,aAAa,CAACE,kBAAkB;MACpDwE,cAAc,EAAEC,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACM,yBAAyB,CAAC;MACpEgC,QAAQ,EAAEqC,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACO,kBAAkB,CAAC;MACvDsE,MAAM,EAAE7E,aAAa,CAACQ,YAAY,CAACkD;IACrC,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1D,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8E,8BAA8B,GAAIC,QAAQ,IAAK;IACnDhF,qBAAqB,CAACgF,QAAQ,CAAC;EACjC,CAAC;;EAED;EACA,MAAMnB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACL1D,kBAAkB,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC3DC,yBAAyB,EAAE;QACzB,qBAAqB,EAAE;UAAE0E,KAAK,EAAE,EAAE;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;QACxE,aAAa,EAAE;UAAE4E,KAAK,EAAE,CAAC;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;QAC/D,cAAc,EAAE;UAAE4E,KAAK,EAAE,CAAC;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;QAChE,sBAAsB,EAAE;UAAE4E,KAAK,EAAE,CAAC;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE;MACzE,CAAC;MACDG,kBAAkB,EAAE;QAClB,UAAU,EAAE;UAAEyE,KAAK,EAAE,CAAC;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE,CAAC;UAAEM,mBAAmB,EAAE,GAAG;UAAEC,qBAAqB,EAAE;QAAI,CAAC;QAClH,YAAY,EAAE;UAAEqE,KAAK,EAAE,CAAC;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE,CAAC;UAAEM,mBAAmB,EAAE,GAAG;UAAEC,qBAAqB,EAAE;QAAI,CAAC;QACpH,cAAc,EAAE;UAAEqE,KAAK,EAAE,CAAC;UAAE7E,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE,CAAC;UAAEM,mBAAmB,EAAE,GAAG;UAAEC,qBAAqB,EAAE;QAAI;MACvH,CAAC;MACDH,YAAY,EAAE,CACZ;QAAEyE,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAE7E,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,EACjE;QAAE6E,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAE7E,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,EACjE;QAAE6E,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAE7E,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,EACjE;QAAE6E,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAE7E,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,CAClE;MACDK,cAAc,EAAE;QACdC,mBAAmB,EAAE,GAAG;QACxBC,qBAAqB,EAAE;MACzB;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMuE,UAAU,GAAGA,CAAA,KAAM;IACvB1D,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACjD,MAAM0D,UAAU,GAAGvB,kBAAkB,CAAC,CAAC;IACvC3D,gBAAgB,CAACkF,UAAU,CAAC;IAC5B3D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0D,UAAU,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMxB,kBAAkB,GAAI1B,OAAO,IAAK;IACtC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAIA,OAAO,CAACyB,MAAM,KAAK,CAAC,EAAE;MACnDlC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,OAAO,CAACyB,MAAM,EAAE,SAAS,CAAC;;IAEnE;IACA,MAAMtD,OAAO,GAAG6B,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC,CAACqC,MAAM;IAClE,MAAMvD,QAAQ,GAAG8B,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,UAAU,CAAC,CAACqC,MAAM;IACpE,MAAMrD,MAAM,GAAG4B,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,QAAQ,CAAC,CAACqC,MAAM;IAEhElC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAErB,OAAO;MAAED,QAAQ;MAAEE;IAAO,CAAC,CAAC;;IAElE;IACA,MAAMqE,cAAc,GAAG,CAAC,CAAC;IACzBzC,OAAO,CAACmD,OAAO,CAACC,MAAM,IAAI;MACxB,MAAMC,IAAI,GAAGD,MAAM,CAACtC,aAAa,IAAI,SAAS;MAC9C,IAAI,CAAC2B,cAAc,CAACY,IAAI,CAAC,EAAE;QACzBZ,cAAc,CAACY,IAAI,CAAC,GAAG;UACrBN,KAAK,EAAE,CAAC;UACR7E,QAAQ,EAAE,CAAC;UACXE,MAAM,EAAE,CAAC;UACTD,OAAO,EAAE;QACX,CAAC;MACH;MACAsE,cAAc,CAACY,IAAI,CAAC,CAACN,KAAK,EAAE;MAC5B,IAAIK,MAAM,CAAChE,MAAM,KAAK,UAAU,EAAEqD,cAAc,CAACY,IAAI,CAAC,CAACnF,QAAQ,EAAE,CAAC,KAC7D,IAAIkF,MAAM,CAAChE,MAAM,KAAK,QAAQ,EAAEqD,cAAc,CAACY,IAAI,CAAC,CAACjF,MAAM,EAAE,CAAC,KAC9DqE,cAAc,CAACY,IAAI,CAAC,CAAClF,OAAO,EAAE;IACrC,CAAC,CAAC;IAEFoB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAACC,IAAI,CAACF,cAAc,CAAC,CAAC;;IAE5D;IACA,MAAMnE,kBAAkB,GAAG,CAAC,CAAC;IAC7B0B,OAAO,CAACmD,OAAO,CAACC,MAAM,IAAI;MAAA,IAAAE,iBAAA;MACxB,MAAM3C,WAAW,GAAGyC,MAAM,CAACzC,WAAW,MAAA2C,iBAAA,GAAIF,MAAM,CAAC3C,SAAS,cAAA6C,iBAAA,uBAAhBA,iBAAA,CAAkB5C,IAAI,KAAI,SAAS;MAC7E,IAAI,CAACpC,kBAAkB,CAACqC,WAAW,CAAC,EAAE;QACpCrC,kBAAkB,CAACqC,WAAW,CAAC,GAAG;UAChCoC,KAAK,EAAE,CAAC;UACR7E,QAAQ,EAAE,CAAC;UACXE,MAAM,EAAE,CAAC;UACTD,OAAO,EAAE,CAAC;UACVM,mBAAmB,EAAE,CAAC;UACtBC,qBAAqB,EAAE,CAAC;UACxB6E,cAAc,EAAE,EAAE;UAClBC,kBAAkB,EAAE;QACtB,CAAC;MACH;MAEAlF,kBAAkB,CAACqC,WAAW,CAAC,CAACoC,KAAK,EAAE;MACvC,IAAIK,MAAM,CAAChE,MAAM,KAAK,UAAU,EAAEd,kBAAkB,CAACqC,WAAW,CAAC,CAACzC,QAAQ,EAAE,CAAC,KACxE,IAAIkF,MAAM,CAAChE,MAAM,KAAK,QAAQ,EAAEd,kBAAkB,CAACqC,WAAW,CAAC,CAACvC,MAAM,EAAE,CAAC,KACzEE,kBAAkB,CAACqC,WAAW,CAAC,CAACxC,OAAO,EAAE;MAE9C,IAAIiF,MAAM,CAACnE,gBAAgB,EAAE;QAC3BX,kBAAkB,CAACqC,WAAW,CAAC,CAAC4C,cAAc,CAACE,IAAI,CAACL,MAAM,CAACnE,gBAAgB,CAAC;MAC9E;MAEA,IAAImE,MAAM,CAAClE,kBAAkB,EAAE;QAC7BZ,kBAAkB,CAACqC,WAAW,CAAC,CAAC6C,kBAAkB,CAACC,IAAI,CAACL,MAAM,CAAClE,kBAAkB,CAAC;MACpF;IACF,CAAC,CAAC;;IAEF;IACAwD,MAAM,CAACC,IAAI,CAACrE,kBAAkB,CAAC,CAAC6E,OAAO,CAACO,OAAO,IAAI;MACjD,MAAM;QAAEH,cAAc;QAAEC;MAAmB,CAAC,GAAGlF,kBAAkB,CAACoF,OAAO,CAAC;MAE1E,IAAIH,cAAc,CAAC9B,MAAM,GAAG,CAAC,EAAE;QAC7BnD,kBAAkB,CAACoF,OAAO,CAAC,CAACjF,mBAAmB,GAC7C,CAAC8E,cAAc,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,EAAE,CAAC,CAAC,GAAGN,cAAc,CAAC9B,MAAM,EAAEqC,OAAO,CAAC,CAAC,CAAC;MAChG;MAEA,IAAIN,kBAAkB,CAAC/B,MAAM,GAAG,CAAC,EAAE;QACjCnD,kBAAkB,CAACoF,OAAO,CAAC,CAAChF,qBAAqB,GAC/C,CAAC8E,kBAAkB,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,EAAE,CAAC,CAAC,GAAGL,kBAAkB,CAAC/B,MAAM,EAAEqC,OAAO,CAAC,CAAC,CAAC;MACxG;;MAEA;MACA,OAAOxF,kBAAkB,CAACoF,OAAO,CAAC,CAACH,cAAc;MACjD,OAAOjF,kBAAkB,CAACoF,OAAO,CAAC,CAACF,kBAAkB;IACvD,CAAC,CAAC;IAEFjE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEkD,MAAM,CAACC,IAAI,CAACrE,kBAAkB,CAAC,CAACmD,MAAM,EAAE,UAAU,CAAC;;IAErG;IACA,MAAMsC,cAAc,GAAG,CAAC,CAAC;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,IAAID,IAAI,CAAC,CAAC;IAC/BC,YAAY,CAACC,QAAQ,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAEzCpE,OAAO,CAACmD,OAAO,CAACC,MAAM,IAAI;MACxB,MAAMiB,IAAI,GAAG,IAAIJ,IAAI,CAACb,MAAM,CAACkB,aAAa,CAAC;MAC3C,IAAID,IAAI,IAAIH,YAAY,EAAE;QACxB,MAAMK,SAAS,GAAG,GAAGF,IAAI,CAACD,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAIC,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE;QAChE,IAAI,CAACT,cAAc,CAACQ,SAAS,CAAC,EAAE;UAC9BR,cAAc,CAACQ,SAAS,CAAC,GAAG;YAAExB,KAAK,EAAE,CAAC;YAAE7E,QAAQ,EAAE,CAAC;YAAEE,MAAM,EAAE,CAAC;YAAED,OAAO,EAAE;UAAE,CAAC;QAC9E;QACA4F,cAAc,CAACQ,SAAS,CAAC,CAACxB,KAAK,EAAE;QACjC,IAAIK,MAAM,CAAChE,MAAM,KAAK,UAAU,EAAE2E,cAAc,CAACQ,SAAS,CAAC,CAACrG,QAAQ,EAAE,CAAC,KAClE,IAAIkF,MAAM,CAAChE,MAAM,KAAK,QAAQ,EAAE2E,cAAc,CAACQ,SAAS,CAAC,CAACnG,MAAM,EAAE,CAAC,KACnE2F,cAAc,CAACQ,SAAS,CAAC,CAACpG,OAAO,EAAE;MAC1C;IACF,CAAC,CAAC;;IAEF;IACA,MAAMI,YAAY,GAAGmE,MAAM,CAACC,IAAI,CAACoB,cAAc,CAAC,CAACxD,GAAG,CAACyC,KAAK,KAAK;MAC7DA,KAAK;MACL,GAAGe,cAAc,CAACf,KAAK;IACzB,CAAC,CAAC,CAAC,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGH,CAAC,CAAC1B,KAAK,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAACvE,GAAG,CAACwE,MAAM,CAAC;MACtD,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGN,CAAC,CAAC3B,KAAK,CAAC8B,KAAK,CAAC,GAAG,CAAC,CAACvE,GAAG,CAACwE,MAAM,CAAC;MACtD,OAAOF,KAAK,KAAKI,KAAK,GAAGL,MAAM,GAAGI,MAAM,GAAGH,KAAK,GAAGI,KAAK;IAC1D,CAAC,CAAC;IAEF1F,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEjB,YAAY,CAACkD,MAAM,EAAE,QAAQ,CAAC;;IAE1E;IACA,MAAMyD,cAAc,GAAGlF,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC;IAClE,MAAMX,mBAAmB,GAAGyG,cAAc,CAACzD,MAAM,GAAG,CAAC,GACjD,CAACyD,cAAc,CAACvB,MAAM,CAAC,CAACC,GAAG,EAAExD,CAAC,KAAKwD,GAAG,IAAIxD,CAAC,CAACnB,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGiG,cAAc,CAACzD,MAAM,EAAEqC,OAAO,CAAC,CAAC,CAAC,GAC1G,CAAC;IACL,MAAMpF,qBAAqB,GAAGwG,cAAc,CAACzD,MAAM,GAAG,CAAC,GACnD,CAACyD,cAAc,CAACvB,MAAM,CAAC,CAACC,GAAG,EAAExD,CAAC,KAAKwD,GAAG,IAAIxD,CAAC,CAAClB,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGgG,cAAc,CAACzD,MAAM,EAAEqC,OAAO,CAAC,CAAC,CAAC,GAC5G,CAAC;IAELvE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAAEf,mBAAmB;MAAEC;IAAsB,CAAC,CAAC;;IAE/E;IACA,MAAMyG,gBAAgB,GAAG;MACvBlH,kBAAkB,EAAE;QAAEC,QAAQ;QAAEC,OAAO;QAAEC;MAAO,CAAC;MACjDC,yBAAyB,EAAEoE,cAAc;MACzCnE,kBAAkB;MAClBC,YAAY;MACZC,cAAc,EAAE;QACdC,mBAAmB;QACnBC;MACF;IACF,CAAC;IAEDa,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE2F,gBAAgB,CAAC;IACxDnH,gBAAgB,CAACmH,gBAAgB,CAAC;EACpC,CAAC;EAID,MAAMC,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAItG,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,EAAE;MAC5EtD,QAAQ,CAAC,sEAAsE,CAAC;MAChF;IACF;IAEA,IAAI,CAACmD,UAAU,CAACM,mBAAmB,EAAE;MACnCzD,QAAQ,CAAC,sDAAsD,CAAC;MAChE;IACF;IAEA,IAAI;MACF,MAAM8D,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUpE,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM8J,aAAa,GAAG;QACpBlG,MAAM,EAAEL,UAAU,CAACK,MAAM;QACzBH,gBAAgB,EAAEF,UAAU,CAACE,gBAAgB;QAC7CC,kBAAkB,EAAEH,UAAU,CAACG,kBAAkB;QACjDC,OAAO,EAAEJ,UAAU,CAACI,OAAO,IAAI,EAAE;QACjCE,mBAAmB,EAAEN,UAAU,CAACM;MAClC,CAAC;MAEDE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QAAE6F,QAAQ;QAAE,GAAGC,aAAa;QAAEjG,mBAAmB,EAAE;MAAyB,CAAC,CAAC;MACvH,MAAMgD,QAAQ,GAAG,MAAM/K,KAAK,CAACiO,GAAG,CAAC,qCAAqCF,QAAQ,EAAE,EAAEC,aAAa,EAAE5F,MAAM,CAAC;MACxGH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6C,QAAQ,CAACtC,IAAI,CAAC;;MAE7C;MACAzC,iBAAiB,CAACyB,UAAU,CAACM,mBAAmB,CAAC;;MAEjD;MACA,MAAMmG,YAAY,GAAG3J,cAAc,CAAC4J,IAAI,CAACrF,CAAC,IAAIA,CAAC,CAACsF,GAAG,KAAKL,QAAQ,CAAC;;MAEjE;MACA,MAAMM,2BAA2B,GAAG;QAClC,GAAGL,aAAa;QAChBM,cAAc,EAAE,CAAArK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,IAAI,KAAI,oBAAoB;QAClDmF,YAAY,EAAE,IAAI5B,IAAI,CAAC;MACzB,CAAC;MAEDnI,iBAAiB,CAACgK,IAAI,IAAIA,IAAI,CAAC3F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACsF,GAAG,KAAKL,QAAQ,CAAC,CAAC;MAC/DrJ,cAAc,CAAC8J,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAGN,YAAY;QAAE,GAAGG;MAA4B,CAAC,CAAC,CAAC;;MAEtF;MACA,MAAMI,iBAAiB,GAAG,CACxB,GAAGhK,WAAW,EACd;QAAE,GAAGyJ,YAAY;QAAE,GAAGG;MAA4B,CAAC,EACnD,GAAG9J,cAAc,CAACsE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACsF,GAAG,KAAKL,QAAQ,CAAC,CAClD;MACD3D,kBAAkB,CAACqE,iBAAiB,CAAC;;MAErC;MACA3J,iBAAiB,CAAC,IAAI,CAAC;MACvB4C,aAAa,CAAC;QACZC,gBAAgB,EAAE,CAAC;QACnBC,kBAAkB,EAAE,CAAC;QACrBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACFzD,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAgC,iBAAiB,CAAC,UAAUmB,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,UAAU,GAAG,UAAU,gBAAgB,CAAC;MACvG1B,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACAsI,UAAU,CAAC,MAAM;QACftI,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOkE,GAAG,EAAE;MAAA,IAAAqE,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ7G,OAAO,CAAC5D,KAAK,CAAC,sBAAsB,EAAE;QACpCyG,OAAO,EAAER,GAAG,CAACQ,OAAO;QACpBhD,MAAM,GAAA6G,cAAA,GAAErE,GAAG,CAACS,QAAQ,cAAA4D,cAAA,uBAAZA,cAAA,CAAc7G,MAAM;QAC5BW,IAAI,GAAAmG,cAAA,GAAEtE,GAAG,CAACS,QAAQ,cAAA6D,cAAA,uBAAZA,cAAA,CAAcnG;MACtB,CAAC,CAAC;MACFnE,QAAQ,CAAC,EAAAuK,cAAA,GAAAvE,GAAG,CAACS,QAAQ,cAAA8D,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcpG,IAAI,cAAAqG,mBAAA,uBAAlBA,mBAAA,CAAoBhE,OAAO,KAAI,yBAAyB,CAAC;IACpE;EACF,CAAC;EAED,MAAMiE,aAAa,GAAIrG,OAAO,IAAK;IACjC,IAAIsG,QAAQ,GAAG,CAAC,GAAGtG,OAAO,CAAC;;IAE3B;IACA,MAAMuG,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC;IACxBsC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAInK,SAAS,KAAK,OAAO,EAAE;MACzBiK,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMqG,UAAU,GAAG,IAAIxC,IAAI,CAAC7D,CAAC,CAACkE,aAAa,CAAC;QAC5C,OAAOmC,UAAU,CAACC,YAAY,CAAC,CAAC,KAAKH,KAAK,CAACG,YAAY,CAAC,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIrK,SAAS,KAAK,UAAU,EAAE;MACnC,MAAMsK,QAAQ,GAAG,IAAI1C,IAAI,CAACsC,KAAK,CAAC;MAChCI,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACxCP,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMqG,UAAU,GAAG,IAAIxC,IAAI,CAAC7D,CAAC,CAACkE,aAAa,CAAC;QAC5C,OAAOmC,UAAU,CAACC,YAAY,CAAC,CAAC,KAAKC,QAAQ,CAACD,YAAY,CAAC,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIrK,SAAS,KAAK,MAAM,EAAE;MAC/B,MAAMyK,OAAO,GAAG,IAAI7C,IAAI,CAACsC,KAAK,CAAC;MAC/BO,OAAO,CAACF,OAAO,CAACE,OAAO,CAACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACtCP,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMqG,UAAU,GAAG,IAAIxC,IAAI,CAAC7D,CAAC,CAACkE,aAAa,CAAC;QAC5C,OAAOmC,UAAU,IAAIF,KAAK,IAAIE,UAAU,IAAIK,OAAO;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIzK,SAAS,KAAK,OAAO,EAAE;MAChC,MAAM0K,UAAU,GAAG,IAAI9C,IAAI,CAACsC,KAAK,CAAC/B,WAAW,CAAC,CAAC,EAAE+B,KAAK,CAACnC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,MAAM4C,SAAS,GAAG,IAAI/C,IAAI,CAACsC,KAAK,CAAC/B,WAAW,CAAC,CAAC,EAAE+B,KAAK,CAACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACxEkC,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMqG,UAAU,GAAG,IAAIxC,IAAI,CAAC7D,CAAC,CAACkE,aAAa,CAAC;QAC5C,OAAOmC,UAAU,IAAIM,UAAU,IAAIN,UAAU,GAAGO,SAAS;MAC3D,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIzK,UAAU,EAAE;MACd+J,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMqG,UAAU,GAAG,IAAIxC,IAAI,CAAC7D,CAAC,CAACkE,aAAa,CAAC;QAC5C,MAAM2C,KAAK,GAAGR,UAAU,CAACS,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC/D,OAAO,GAAGH,KAAK,KAAK,KAAK1K,UAAU;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIE,WAAW,EAAE;MACf6J,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC;QAAA,IAAAiH,YAAA,EAAAC,qBAAA,EAAAC,cAAA;QAAA,OAC1B,EAAAF,YAAA,GAACjH,CAAC,CAACoH,SAAS,cAAAH,YAAA,wBAAAC,qBAAA,GAAXD,YAAA,CAAaI,QAAQ,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClL,WAAW,CAACiL,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GACxEnH,CAAC,CAACO,WAAW,cAAA4G,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClL,WAAW,CAACiL,WAAW,CAAC,CAAC,CAAC,CAAC;MAAA,CACpE,CAAC;IACH;;IAEA;IACA,IAAI/K,eAAe,IAAIA,eAAe,KAAK,KAAK,EAAE;MAChD2J,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACU,aAAa,KAAKnE,eAAe,CAAC;IACtE;;IAEA;IACA,IAAIE,aAAa,IAAIA,aAAa,KAAK,KAAK,EAAE;MAC5CyJ,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACO,WAAW,KAAK9D,aAAa,CAAC;IAClE;;IAEA;IACA,IAAIM,UAAU,KAAK,MAAM,IAAIJ,YAAY,IAAIA,YAAY,KAAK,KAAK,EAAE;MACnEuJ,QAAQ,GAAGA,QAAQ,CAACnG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAKrC,YAAY,CAAC;IAC5D;;IAEA;IACA,OAAOuJ,QAAQ,CAAC7B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,MAAMiD,KAAK,GAAG,IAAI3D,IAAI,CAAC9G,UAAU,KAAK,SAAS,GAAGuH,CAAC,CAACJ,aAAa,GAAGI,CAAC,CAACmB,YAAY,IAAInB,CAAC,CAACJ,aAAa,CAAC;MACtG,MAAMuD,KAAK,GAAG,IAAI5D,IAAI,CAAC9G,UAAU,KAAK,SAAS,GAAGwH,CAAC,CAACL,aAAa,GAAGK,CAAC,CAACkB,YAAY,IAAIlB,CAAC,CAACL,aAAa,CAAC;MACtG,OAAOuD,KAAK,GAAGD,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,YAAY,GAAGjM,cAAc,CAAC4F,MAAM,GAAG1F,WAAW,CAAC0F,MAAM;EAC/D,MAAMsG,eAAe,GAAGhM,WAAW,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,UAAU,CAAC,CAACqC,MAAM;EAC/E,MAAMuG,aAAa,GAAGjM,WAAW,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,QAAQ,CAAC,CAACqC,MAAM;EAC3E,MAAMwG,cAAc,GAAGH,YAAY,GAAG,CAAC,GAAG,CAAEC,eAAe,GAAGD,YAAY,GAAI,GAAG,EAAEhE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACjG,MAAMoE,UAAU,GAAGJ,YAAY,GAAG,CAAC,GAAG,CAAEE,aAAa,GAAGF,YAAY,GAAI,GAAG,EAAEhE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAC3F,MAAMrF,mBAAmB,GACvB1C,WAAW,CAAC0F,MAAM,GAAG,CAAC,GAClB,CAAC1F,WAAW,CAAC4H,MAAM,CAAC,CAACC,GAAG,EAAExD,CAAC,KAAKwD,GAAG,IAAIxD,CAAC,CAACnB,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGlD,WAAW,CAAC0F,MAAM,EAAEqC,OAAO,CAAC,CAAC,CAAC,GACpG,CAAC;EACP,MAAMpF,qBAAqB,GACzB3C,WAAW,CAAC0F,MAAM,GAAG,CAAC,GAClB,CAAC1F,WAAW,CAAC4H,MAAM,CAAC,CAACC,GAAG,EAAExD,CAAC,KAAKwD,GAAG,IAAIxD,CAAC,CAAClB,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGnD,WAAW,CAAC0F,MAAM,EAAEqC,OAAO,CAAC,CAAC,CAAC,GACtG,CAAC;EAEP,MAAMqE,WAAW,GAAGA,CAACtE,MAAM,EAAEuE,OAAO,GAAG,IAAI,kBACzC1O,OAAA;IAAK2O,SAAS,EAAC,MAAM;IAAAC,QAAA,EAClB,CAAC,GAAGrI,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,GAAG,CAAC,CAACgI,CAAC,EAAEC,CAAC,kBACtB9O,OAAA,CAAC7B,MAAM;MAELwQ,SAAS,EAAE,WAAWG,CAAC,GAAG3E,MAAM,GAAG,iBAAiB,GAAG,eAAe,IACpEuE,OAAO,GAAG,gBAAgB,GAAG,EAAE,EAC9B;MACHA,OAAO,EAAEA,OAAO,GAAG,MAAMA,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,GAAG;IAAK,GAJ1CA,CAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMC,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IACrC;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI3N,OAAO,EAAE;IACX,oBAAO/B,OAAA,CAACjC,MAAM;MAAAgR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACElP,OAAA;IAAK2O,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvC5O,OAAA;MAAK2O,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnD5O,OAAA,CAAClC,MAAM;QAAC6R,aAAa,EAAEA,CAAA,KAAM,CAAC;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnClP,OAAA;QAAM2O,SAAS,EAAC,4BAA4B;QAACiB,KAAK,EAAE;UAAEtP,UAAU,EAAE,oCAAoCH,YAAY,CAACC,OAAO,OAAOD,YAAY,CAACG,UAAU;QAAI,CAAE;QAAAsO,QAAA,eAC5J5O,OAAA;UAAK2O,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/B3M,KAAK,iBACJjC,OAAA,CAAChC,MAAM,CAAC6R,GAAG;YACTC,OAAO,EAAE;cAAET,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCK,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9Bf,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7E5O,OAAA;cAAK2O,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5O,OAAA;gBAAK2O,SAAS,EAAC,2BAA2B;gBAACqB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAArB,QAAA,eAChF5O,OAAA;kBACEkQ,QAAQ,EAAC,SAAS;kBAClBC,CAAC,EAAC,yNAAyN;kBAC3NC,QAAQ,EAAC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlP,OAAA;gBAAG2O,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAE3M;cAAK;gBAAA8M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAEDlP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;YAACC,OAAO,EAAE;cAAET,OAAO,EAAE;YAAE,CAAE;YAACU,OAAO,EAAE;cAAEV,OAAO,EAAE;YAAE,CAAE;YAACE,UAAU,EAAE;cAAEc,QAAQ,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAC1F5O,OAAA;cAAK2O,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/F5O,OAAA;gBAAA4O,QAAA,gBACE5O,OAAA;kBAAI2O,SAAS,EAAC,qCAAqC;kBAACiB,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACC;kBAAQ,CAAE;kBAAAwO,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrHlP,OAAA;kBAAG4P,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACI;kBAAK,CAAE;kBAAAqO,QAAA,GAAC,gBAAc,EAAC,CAAA/M,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,IAAI,KAAI,YAAY;gBAAA;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACNlP,OAAA;gBAAK2O,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzB5O,OAAA,CAAChC,MAAM,CAACsS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1B9B,OAAO,EAAEA,CAAA,KAAM5K,qBAAqB,CAAC,IAAI,CAAE;kBAC3C6K,SAAS,EAAC,mDAAmD;kBAC7DiB,KAAK,EAAE;oBAAEtP,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;kBAAI,CAAE;kBAAAuO,QAAA,gBAEvG5O,OAAA,CAAClB,WAAW;oBAAC6P,SAAS,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAClC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlP,OAAA;cAAK2O,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5O,OAAA;gBAAK2O,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAC5C5O,OAAA;kBACE0O,OAAO,EAAEA,CAAA,KAAMlL,UAAU,CAAC,SAAS,CAAE;kBACrCmL,SAAS,EAAC,iCAAiC;kBAC3CiB,KAAK,EAAE;oBACLc,YAAY,EAAEnN,OAAO,KAAK,SAAS,GAAG,aAAapD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;oBAClFU,KAAK,EAAEyC,OAAO,KAAK,SAAS,GAAGpD,YAAY,CAACC,OAAO,GAAG,SAAS;oBAC/Da,eAAe,EAAEsC,OAAO,KAAK,SAAS,GAAG,GAAGpD,YAAY,CAACC,OAAO,IAAI,GAAG;kBACzE,CAAE;kBAAAwO,QAAA,gBAEF5O,OAAA,CAACpB,gBAAgB;oBAAC+P,SAAS,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEtD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlP,OAAA;kBACE0O,OAAO,EAAEA,CAAA,KAAMlL,UAAU,CAAC,WAAW,CAAE;kBACvCmL,SAAS,EAAC,iCAAiC;kBAC3CiB,KAAK,EAAE;oBACLc,YAAY,EAAEnN,OAAO,KAAK,WAAW,GAAG,aAAapD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;oBACpFU,KAAK,EAAEyC,OAAO,KAAK,WAAW,GAAGpD,YAAY,CAACC,OAAO,GAAG,SAAS;oBACjEa,eAAe,EAAEsC,OAAO,KAAK,WAAW,GAAG,GAAGpD,YAAY,CAACC,OAAO,IAAI,GAAG;kBAC3E,CAAE;kBAAAwO,QAAA,gBAEF5O,OAAA,CAACrB,UAAU;oBAACgQ,SAAS,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL3L,OAAO,KAAK,SAAS,iBACpBvD,OAAA,CAAAE,SAAA;cAAA0O,QAAA,gBAEE5O,OAAA,CAAChC,MAAM,CAAC6R,GAAG;gBACTc,QAAQ,EAAExB,SAAU;gBACpBW,OAAO,EAAC,QAAQ;gBAChBc,WAAW,EAAC,MAAM;gBAClBC,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBnC,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBAErE5O,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5I5O,OAAA;oBAAK2O,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC5O,OAAA;sBAAK2O,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACL3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAwO,QAAA,eACF5O,OAAA,CAAC1B,UAAU;wBAACqQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACNlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAG2O,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACzFlP,OAAA;wBAAG2O,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,EAAER;sBAAY;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACblP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5I5O,OAAA;oBAAK2O,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC5O,OAAA;sBAAK2O,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACL3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAwO,QAAA,eACF5O,OAAA,CAACzB,YAAY;wBAACoQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACNlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAG2O,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC3FlP,OAAA;wBAAG2O,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,GAAEL,cAAc,EAAC,GAAC;sBAAA;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC/FlP,OAAA;wBAAG2O,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,GAAC,WAAS,EAACJ,UAAU,EAAC,GAAC;sBAAA;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACblP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5I5O,OAAA;oBAAK2O,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC5O,OAAA;sBAAK2O,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACL3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAwO,QAAA,eACF5O,OAAA,CAACxB,aAAa;wBAACmQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E,CAAC,eACNlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAG2O,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAsB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAClGlP,OAAA;wBAAG2O,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,GAAE7J,mBAAmB,EAAC,IAAE;sBAAA;wBAAAgK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACblP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5I5O,OAAA;oBAAK2O,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC5O,OAAA;sBAAK2O,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACL3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAwO,QAAA,eACF5O,OAAA,CAACxB,aAAa;wBAACmQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E,CAAC,eACNlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAG2O,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACpGlP,OAAA;wBAAG2O,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,GAAE5J,qBAAqB,EAAC,IAAE;sBAAA;wBAAA+J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGblP,OAAA;gBAAK2O,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB5O,OAAA;kBAAK2O,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5C5O,OAAA;oBACE0O,OAAO,EAAEA,CAAA,KAAMhL,aAAa,CAAC,SAAS,CAAE;oBACxCiL,SAAS,EAAC,+BAA+B;oBACzCiB,KAAK,EAAE;sBACLc,YAAY,EAAEjN,UAAU,KAAK,SAAS,GAAG,aAAatD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;sBACrFU,KAAK,EAAE2C,UAAU,KAAK,SAAS,GAAGtD,YAAY,CAACC,OAAO,GAAG;oBAC3D,CAAE;oBAAAwO,QAAA,GACH,mBACkB,EAACzM,cAAc,CAAC4F,MAAM,EAAC,GAC1C;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlP,OAAA;oBACE0O,OAAO,EAAEA,CAAA,KAAMhL,aAAa,CAAC,MAAM,CAAE;oBACrCiL,SAAS,EAAC,+BAA+B;oBACzCiB,KAAK,EAAE;sBACLc,YAAY,EAAEjN,UAAU,KAAK,MAAM,GAAG,aAAatD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;sBAClFU,KAAK,EAAE2C,UAAU,KAAK,MAAM,GAAGtD,YAAY,CAACC,OAAO,GAAG;oBACxD,CAAE;oBAAAwO,QAAA,GACH,gBACe,EAACvM,WAAW,CAAC0F,MAAM,EAAC,GACpC;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,eACN,CACH,EAGA3L,OAAO,KAAK,WAAW,iBACtBvD,OAAA;cAAK2O,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5O,OAAA;gBAAK2O,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAC/F5O,OAAA;kBAAI2O,SAAS,EAAC,mBAAmB;kBAACiB,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACC;kBAAQ,CAAE;kBAAAwO,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClGlP,OAAA;kBAAK2O,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzB5O,OAAA;oBACE+Q,KAAK,EAAE5M,kBAAmB;oBAC1B6M,QAAQ,EAAGC,CAAC,IAAK9H,8BAA8B,CAAC8H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAChEpC,SAAS,EAAC,6CAA6C;oBACvDiB,KAAK,EAAE;sBACLuB,OAAO,EAAE,MAAM;sBACfC,SAAS,EAAE,MAAM;sBACjBC,WAAW,EAAE,SAAS;sBACtB,QAAQ,EAAE;wBAAEA,WAAW,EAAElR,YAAY,CAACC;sBAAQ;oBAChD,CAAE;oBAAAwO,QAAA,gBAEF5O,OAAA;sBAAQ+Q,KAAK,EAAC,MAAM;sBAAAnC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvClP,OAAA;sBAAQ+Q,KAAK,EAAC,OAAO;sBAAAnC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzClP,OAAA;sBAAQ+Q,KAAK,EAAC,MAAM;sBAAAnC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvClP,OAAA;sBAAQ+Q,KAAK,EAAC,KAAK;sBAAAnC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCxR,OAAA,CAAChC,MAAM,CAAC6R,GAAG;gBACTC,OAAO,EAAE;kBAAET,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAG,CAAE;gBAC/BK,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAE,CAAE;gBAC9Bf,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBAErE5O,OAAA;kBAAI2O,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjFlP,OAAA;kBAAK2O,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD5O,OAAA;oBAAA4O,QAAA,GAAG,cAAY,EAACzK,kBAAkB;kBAAA;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvClP,OAAA;oBAAA4O,QAAA,GAAG,iBAAe,EAACrM,UAAU,CAACwF,MAAM;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzClP,OAAA;oBAAA4O,QAAA,GAAG,uBAAqB,EAAC6C,IAAI,CAACC,SAAS,CAACrN,aAAa,CAACE,kBAAkB,CAAC;kBAAA;oBAAAwK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9ElP,OAAA;oBAAA4O,QAAA,GAAG,mBAAiB,EAAC5F,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACM,yBAAyB,CAAC,CAACoD,MAAM;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrFlP,OAAA;oBAAA4O,QAAA,GAAG,YAAU,EAAC5F,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACO,kBAAkB,CAAC,CAACmD,MAAM;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvElP,OAAA;oBAAA4O,QAAA,GAAG,UAAQ,EAACvK,aAAa,CAACQ,YAAY,CAACkD,MAAM,EAAC,SAAO;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNlP,OAAA;kBAAK2O,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5O,OAAA;oBACE0O,OAAO,EAAEA,CAAA,KAAMpK,gBAAgB,CAAC2D,kBAAkB,CAAC,CAAC,CAAE;oBACtD0G,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,EAC/E;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlP,OAAA;oBACE0O,OAAO,EAAEA,CAAA,KAAM1G,kBAAkB,CAACzF,UAAU,CAAE;oBAC9CoM,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,EACjF;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlP,OAAA;oBACE0O,OAAO,EAAEnF,UAAW;oBACpBoF,SAAS,EAAC,wEAAwE;oBAAAC,QAAA,EACnF;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,eAGDlP,OAAA;gBAAK2O,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAEzD5O,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpE5O,OAAA;oBAAI2O,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE9O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAwO,QAAA,EAAC;kBAA0B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClHlP,OAAA;oBAAK2O,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDvK,aAAa,CAACE,kBAAkB,IAAIyE,MAAM,CAAC4I,MAAM,CAACvN,aAAa,CAACE,kBAAkB,CAAC,CAACsN,IAAI,CAACC,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,gBACvG9R,OAAA,CAACL,GAAG;sBACF0G,IAAI,EAAE;wBACJhF,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;wBACzC0Q,QAAQ,EAAE,CACR;0BACE1L,IAAI,EAAE,CACJhC,aAAa,CAACE,kBAAkB,CAACC,QAAQ,IAAI,CAAC,EAC9CH,aAAa,CAACE,kBAAkB,CAACE,OAAO,IAAI,CAAC,EAC7CJ,aAAa,CAACE,kBAAkB,CAACG,MAAM,IAAI,CAAC,CAC7C;0BACDzD,eAAe,EAAE,CACf,GAAGd,YAAY,CAACK,MAAM,IAAI;0BAAG;0BAC7B,GAAGL,YAAY,CAACC,OAAO,IAAI;0BAAE;0BAC7B,wBAAwB,CAAK;0BAAA,CAC9B;0BACDiR,WAAW,EAAE,CACXlR,YAAY,CAACK,MAAM,EACnBL,YAAY,CAACC,OAAO,EACpB,sBAAsB,CACvB;0BACD4R,WAAW,EAAE;wBACf,CAAC;sBAEL,CAAE;sBACFC,OAAO,EAAE;wBACPC,UAAU,EAAE,IAAI;wBAChBC,mBAAmB,EAAE,KAAK;wBAC1BpR,OAAO,EAAE;0BACPK,MAAM,EAAE;4BACNgR,QAAQ,EAAE,QAAQ;4BAClB/Q,MAAM,EAAE;8BACNC,aAAa,EAAE,IAAI;8BACnBC,OAAO,EAAE,EAAE;8BACXZ,IAAI,EAAE;gCACJE,IAAI,EAAE;8BACR;4BACF;0BACF,CAAC;0BACDG,OAAO,EAAE;4BACPqR,SAAS,EAAE;8BACTC,KAAK,EAAE,SAAAA,CAASC,OAAO,EAAE;gCACvB,MAAMD,KAAK,GAAGC,OAAO,CAACD,KAAK,IAAI,EAAE;gCACjC,MAAMvB,KAAK,GAAGwB,OAAO,CAACC,GAAG,IAAI,CAAC;gCAC9B,MAAMnJ,KAAK,GAAGkJ,OAAO,CAACE,OAAO,CAACpM,IAAI,CAAC4D,MAAM,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;gCAC7D,MAAMyH,UAAU,GAAGrJ,KAAK,GAAG,CAAC,GAAGsJ,IAAI,CAACC,KAAK,CAAE7B,KAAK,GAAG1H,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;gCACpE,OAAO,GAAGiJ,KAAK,KAAKvB,KAAK,KAAK2B,UAAU,IAAI;8BAC9C;4BACF;0BACF;wBACF;sBACF,CAAE;sBACFG,QAAQ,EAAEA,CAAA,KAAMhN,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE;sBAC/DgN,OAAO,EAAG7Q,KAAK,IAAK4D,OAAO,CAAC5D,KAAK,CAAC,kBAAkB,EAAEA,KAAK;oBAAE;sBAAA8M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,gBAEFlP,OAAA;sBAAK2O,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC5O,OAAA;wBAAK2O,SAAS,EAAC,sCAAsC;wBAACqB,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAAC8C,MAAM,EAAC,cAAc;wBAAAnE,QAAA,eACzG5O,OAAA;0BAAMgT,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC/C,CAAC,EAAC;wBAAsM;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3Q,CAAC,eACNlP,OAAA;wBAAA4O,QAAA,EAAG;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGblP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpE5O,OAAA;oBAAI2O,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE9O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAwO,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnHlP,OAAA;oBAAK2O,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDvK,aAAa,CAACM,yBAAyB,IAAIqE,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACM,yBAAyB,CAAC,CAACoD,MAAM,GAAG,CAAC,gBACzG/H,OAAA,CAACJ,GAAG;sBACFyG,IAAI,EAAE;wBACJhF,MAAM,EAAE2H,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACM,yBAAyB,CAAC;wBAC5DoN,QAAQ,EAAE,CACR;0BACEO,KAAK,EAAE,UAAU;0BACjBjM,IAAI,EAAE2C,MAAM,CAAC4I,MAAM,CAACvN,aAAa,CAACM,yBAAyB,CAAC,CAACkC,GAAG,CAACsM,CAAC,IAAIA,CAAC,CAAC3O,QAAQ,IAAI,CAAC,CAAC;0BACtFvD,eAAe,EAAE,GAAGd,YAAY,CAACK,MAAM,IAAI;0BAC3C6Q,WAAW,EAAElR,YAAY,CAACK,MAAM;0BAChCwR,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,SAAS;0BAChBjM,IAAI,EAAE2C,MAAM,CAAC4I,MAAM,CAACvN,aAAa,CAACM,yBAAyB,CAAC,CAACkC,GAAG,CAACsM,CAAC,IAAIA,CAAC,CAAC1O,OAAO,IAAI,CAAC,CAAC;0BACrFxD,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;0BAC5CiR,WAAW,EAAElR,YAAY,CAACC,OAAO;0BACjC4R,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,QAAQ;0BACfjM,IAAI,EAAE2C,MAAM,CAAC4I,MAAM,CAACvN,aAAa,CAACM,yBAAyB,CAAC,CAACkC,GAAG,CAACsM,CAAC,IAAIA,CAAC,CAACzO,MAAM,IAAI,CAAC,CAAC;0BACpFzD,eAAe,EAAE,wBAAwB;0BACzCoQ,WAAW,EAAE,sBAAsB;0BACnCW,WAAW,EAAE;wBACf,CAAC;sBAEL,CAAE;sBACFC,OAAO,EAAE;wBACPC,UAAU,EAAE,IAAI;wBAChBC,mBAAmB,EAAE,KAAK;wBAC1BiB,MAAM,EAAE;0BACNC,CAAC,EAAE;4BACDC,OAAO,EAAE,IAAI;4BACbC,KAAK,EAAE;8BACLC,WAAW,EAAE,EAAE;8BACfC,WAAW,EAAE;4BACf;0BACF,CAAC;0BACD/D,CAAC,EAAE;4BACD4D,OAAO,EAAE,IAAI;4BACbI,WAAW,EAAE,IAAI;4BACjBH,KAAK,EAAE;8BACLI,QAAQ,EAAE;4BACZ;0BACF;wBACF,CAAC;wBACD5S,OAAO,EAAE;0BACPK,MAAM,EAAE;4BACNgR,QAAQ,EAAE,QAAQ;4BAClB/Q,MAAM,EAAE;8BACNC,aAAa,EAAE,IAAI;8BACnBC,OAAO,EAAE,EAAE;8BACXZ,IAAI,EAAE;gCACJE,IAAI,EAAE;8BACR;4BACF;0BACF,CAAC;0BACDG,OAAO,EAAE;4BACP4S,IAAI,EAAE,OAAO;4BACbC,SAAS,EAAE;0BACb;wBACF;sBACF;oBAAE;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAEFlP,OAAA;sBAAK2O,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC5O,OAAA;wBAAK2O,SAAS,EAAC,sCAAsC;wBAACqB,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAAC8C,MAAM,EAAC,cAAc;wBAAAnE,QAAA,eACzG5O,OAAA;0BAAMgT,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC/C,CAAC,EAAC;wBAAsM;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3Q,CAAC,eACNlP,OAAA;wBAAA4O,QAAA,EAAG;sBAA2B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGblP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpE5O,OAAA;oBAAI2O,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE9O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAwO,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3GlP,OAAA;oBAAK2O,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9B5O,OAAA;sBAAO2O,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,gBACpD5O,OAAA;wBAAO2O,SAAS,EAAC,YAAY;wBAAAC,QAAA,eAC3B5O,OAAA;0BAAA4O,QAAA,gBACE5O,OAAA;4BAAI2O,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRlP,OAAA;wBAAO2O,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EACjDvK,aAAa,CAACO,kBAAkB,IAAIoE,MAAM,CAACC,IAAI,CAAC5E,aAAa,CAACO,kBAAkB,CAAC,CAACmD,MAAM,GAAG,CAAC,GAC3FiB,MAAM,CAAC8K,OAAO,CAACzP,aAAa,CAACO,kBAAkB,CAAC,CAACiC,GAAG,CAAC,CAAC,CAACmD,OAAO,EAAE3D,IAAI,CAAC,EAAE0N,KAAK,kBAC1E/T,OAAA;0BAAgB2O,SAAS,EAAEoF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;0BAAAnF,QAAA,gBACrE5O,OAAA;4BAAI2O,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAC1E5E;0BAAO;4BAAA+E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DvI,IAAI,CAACgD,KAAK,IAAI;0BAAC;4BAAA0F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DvI,IAAI,CAACgD,KAAK,GAAG,CAAC,GAAG,GAAGsJ,IAAI,CAACC,KAAK,CAAEvM,IAAI,CAAC7B,QAAQ,GAAG6B,IAAI,CAACgD,KAAK,GAAI,GAAG,CAAC,GAAG,GAAG;0BAAK;4BAAA0F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5E,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DvI,IAAI,CAACtB,mBAAmB,GAAG,CAAC,gBAC3B/E,OAAA;8BAAK2O,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC5O,OAAA;gCAAM2O,SAAS,EAAC,MAAM;gCAAAC,QAAA,EAAEvI,IAAI,CAACtB;8BAAmB;gCAAAgK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EACvDT,WAAW,CAACuF,UAAU,CAAC3N,IAAI,CAACtB,mBAAmB,CAAC,CAAC;4BAAA;8BAAAgK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/C,CAAC,GACJ;0BAAK;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DvI,IAAI,CAACrB,qBAAqB,GAAG,CAAC,gBAC7BhF,OAAA;8BAAK2O,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChC5O,OAAA;gCAAM2O,SAAS,EAAC,MAAM;gCAAAC,QAAA,EAAEvI,IAAI,CAACrB;8BAAqB;gCAAA+J,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EACzDT,WAAW,CAACuF,UAAU,CAAC3N,IAAI,CAACrB,qBAAqB,CAAC,CAAC;4BAAA;8BAAA+J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjD,CAAC,GACJ;0BAAK;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA,GAzBE6E,KAAK;0BAAAhF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA0BV,CACL,CAAC,gBAEFlP,OAAA;0BAAA4O,QAAA,eACE5O,OAAA;4BAAIiU,OAAO,EAAC,GAAG;4BAACtF,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,eACrE5O,OAAA;8BAAK2O,SAAS,EAAC,4BAA4B;8BAAAC,QAAA,gBACzC5O,OAAA;gCAAK2O,SAAS,EAAC,8BAA8B;gCAACqB,IAAI,EAAC,MAAM;gCAACC,OAAO,EAAC,WAAW;gCAAC8C,MAAM,EAAC,cAAc;gCAAAnE,QAAA,eACjG5O,OAAA;kCAAMgT,aAAa,EAAC,OAAO;kCAACC,cAAc,EAAC,OAAO;kCAACC,WAAW,EAAE,CAAE;kCAAC/C,CAAC,EAAC;gCAAyH;kCAAApB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9L,CAAC,yCAER;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACL;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGblP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpE5O,OAAA;oBAAI2O,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE9O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAwO,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrGlP,OAAA;oBAAK2O,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDvK,aAAa,CAACQ,YAAY,IAAIR,aAAa,CAACQ,YAAY,CAACkD,MAAM,GAAG,CAAC,gBAClE/H,OAAA,CAACJ,GAAG;sBACFyG,IAAI,EAAE;wBACJhF,MAAM,EAAEgD,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACqN,CAAC,IAAIA,CAAC,CAAC5K,KAAK,CAAC;wBACpDyI,QAAQ,EAAE,CACR;0BACEO,KAAK,EAAE,UAAU;0BACjBjM,IAAI,EAAEhC,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACqN,CAAC,IAAIA,CAAC,CAAC1P,QAAQ,IAAI,CAAC,CAAC;0BAC1DvD,eAAe,EAAE,GAAGd,YAAY,CAACK,MAAM,IAAI;0BAC3C6Q,WAAW,EAAElR,YAAY,CAACK,MAAM;0BAChCwR,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,SAAS;0BAChBjM,IAAI,EAAEhC,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACqN,CAAC,IAAIA,CAAC,CAACzP,OAAO,IAAI,CAAC,CAAC;0BACzDxD,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;0BAC5CiR,WAAW,EAAElR,YAAY,CAACC,OAAO;0BACjC4R,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,QAAQ;0BACfjM,IAAI,EAAEhC,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACqN,CAAC,IAAIA,CAAC,CAACxP,MAAM,IAAI,CAAC,CAAC;0BACxDzD,eAAe,EAAE,wBAAwB;0BACzCoQ,WAAW,EAAE,sBAAsB;0BACnCW,WAAW,EAAE;wBACf,CAAC;sBAEL,CAAE;sBACFC,OAAO,EAAE;wBACPC,UAAU,EAAE,IAAI;wBAChBC,mBAAmB,EAAE,KAAK;wBAC1BiB,MAAM,EAAE;0BACNC,CAAC,EAAE;4BACDC,OAAO,EAAE,IAAI;4BACbC,KAAK,EAAE;8BACLC,WAAW,EAAE,EAAE;8BACfC,WAAW,EAAE;4BACf;0BACF,CAAC;0BACD/D,CAAC,EAAE;4BACD4D,OAAO,EAAE,IAAI;4BACbI,WAAW,EAAE,IAAI;4BACjBH,KAAK,EAAE;8BACLI,QAAQ,EAAE;4BACZ;0BACF;wBACF,CAAC;wBACD5S,OAAO,EAAE;0BACPK,MAAM,EAAE;4BACNgR,QAAQ,EAAE,QAAQ;4BAClB/Q,MAAM,EAAE;8BACNC,aAAa,EAAE,IAAI;8BACnBC,OAAO,EAAE,EAAE;8BACXZ,IAAI,EAAE;gCACJE,IAAI,EAAE;8BACR;4BACF;0BACF,CAAC;0BACDG,OAAO,EAAE;4BACP4S,IAAI,EAAE,OAAO;4BACbC,SAAS,EAAE;0BACb;wBACF;sBACF;oBAAE;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAEFlP,OAAA;sBAAK2O,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxC5O,OAAA;wBAAK2O,SAAS,EAAC,sCAAsC;wBAACqB,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAAC8C,MAAM,EAAC,cAAc;wBAAAnE,QAAA,eACzG5O,OAAA;0BAAMgT,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC/C,CAAC,EAAC;wBAAgF;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrJ,CAAC,eACNlP,OAAA;wBAAA4O,QAAA,EAAG;sBAAuB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNlP,OAAA,CAAChC,MAAM,CAAC6R,GAAG;gBACTC,OAAO,EAAE;kBAAET,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAG,CAAE;gBAC/BK,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAE,CAAE;gBAC9BH,UAAU,EAAE;kBAAEoC,KAAK,EAAE;gBAAI,CAAE;gBAC3BhD,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAEzE5O,OAAA;kBAAI2O,SAAS,EAAC,4BAA4B;kBAACiB,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACC;kBAAQ,CAAE;kBAAAwO,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/GlP,OAAA;kBAAK2O,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpD5O,OAAA;oBAAK2O,SAAS,EAAC,gBAAgB;oBAACiB,KAAK,EAAE;sBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;oBAAK,CAAE;oBAAAwO,QAAA,gBACtF5O,OAAA;sBAAI2O,SAAS,EAAC,0BAA0B;sBAACiB,KAAK,EAAE;wBAAE9O,KAAK,EAAEX,YAAY,CAACC;sBAAQ,CAAE;sBAAAwO,QAAA,EAAC;oBAAyB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/GlP,OAAA;sBAAK2O,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC5O,OAAA;wBAAK2O,SAAS,EAAC,yBAAyB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,GAC7EvK,aAAa,CAACS,cAAc,CAACC,mBAAmB,EAAC,IACpD;sBAAA;wBAAAgK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNlP,OAAA;wBAAK2O,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAClBH,WAAW,CAACuF,UAAU,CAAC3P,aAAa,CAACS,cAAc,CAACC,mBAAmB,CAAC;sBAAC;wBAAAgK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlP,OAAA;oBAAK2O,SAAS,EAAC,gBAAgB;oBAACiB,KAAK,EAAE;sBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;oBAAK,CAAE;oBAAAwO,QAAA,gBACtF5O,OAAA;sBAAI2O,SAAS,EAAC,0BAA0B;sBAACiB,KAAK,EAAE;wBAAE9O,KAAK,EAAEX,YAAY,CAACC;sBAAQ,CAAE;sBAAAwO,QAAA,EAAC;oBAA2B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjHlP,OAAA;sBAAK2O,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChC5O,OAAA;wBAAK2O,SAAS,EAAC,yBAAyB;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,GAC7EvK,aAAa,CAACS,cAAc,CAACE,qBAAqB,EAAC,IACtD;sBAAA;wBAAA+J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNlP,OAAA;wBAAK2O,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAClBH,WAAW,CAACuF,UAAU,CAAC3P,aAAa,CAACS,cAAc,CAACE,qBAAqB,CAAC;sBAAC;wBAAA+J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,EAGA3L,OAAO,KAAK,SAAS,iBACpBvD,OAAA,CAAChC,MAAM,CAAC6R,GAAG;cACTc,QAAQ,EAAExB,SAAU;cACpBW,OAAO,EAAC,QAAQ;cAChBc,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBnC,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5H5O,OAAA;gBAAK2O,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClB5O,OAAA;kBAAK2O,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,gBAC/F5O,OAAA;oBAAI2O,SAAS,EAAC,mBAAmB;oBAACiB,KAAK,EAAE;sBAAE9O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAwO,QAAA,EACtEnL,UAAU,KAAK,SAAS,GAAG,iBAAiB,GAAG;kBAAc;oBAAAsL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACLlP,OAAA;oBAAK2O,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBAC/D5O,OAAA;sBAAK2O,SAAS,EAAC,UAAU;sBAAAC,QAAA,gBACvB5O,OAAA;wBACE2J,IAAI,EAAC,MAAM;wBACXwK,WAAW,EAAC,mCAAmC;wBAC/CpD,KAAK,EAAEhO,WAAY;wBACnBiO,QAAQ,EAAGC,CAAC,IAAKjO,cAAc,CAACiO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAChDpC,SAAS,EAAC,0DAA0D;wBACpEiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAElR,YAAY,CAACC;0BAAQ;wBAChD;sBAAE;wBAAA2O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFlP,OAAA,CAACtB,QAAQ;wBAACiQ,SAAS,EAAC;sBAAqC;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,eAGNlP,OAAA;sBAAK2O,SAAS,EAAC,YAAY;sBAAAC,QAAA,eACzB5O,OAAA;wBACE0O,OAAO,EAAEA,CAAA,KAAM0F,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAE;wBACpF5F,SAAS,EAAC,+DAA+D;wBACzEiB,KAAK,EAAE;0BACLyB,WAAW,EAAE,SAAS;0BACtBvQ,KAAK,EAAEX,YAAY,CAACI,IAAI;0BACxBgP,UAAU,EAAE;wBACd,CAAE;wBACFiF,WAAW,EAAGvD,CAAC,IAAMA,CAAC,CAACwD,aAAa,CAAC7E,KAAK,CAAC3O,eAAe,GAAG,SAAW;wBACxEyT,UAAU,EAAGzD,CAAC,IAAMA,CAAC,CAACwD,aAAa,CAAC7E,KAAK,CAAC3O,eAAe,GAAG,aAAe;wBAAA2N,QAAA,gBAE3E5O,OAAA,CAACvB,QAAQ;0BAACkQ,SAAS,EAAC;wBAAoB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,YAC7C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlP,OAAA;kBAAK2U,EAAE,EAAC,gBAAgB;kBAAChG,SAAS,EAAC,mDAAmD;kBAACiB,KAAK,EAAE;oBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;kBAAK,CAAE;kBAAAwO,QAAA,eAC7I5O,OAAA;oBAAK2O,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD5O,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAO2O,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzGlP,OAAA;wBACE+Q,KAAK,EAAEpO,SAAU;wBACjBqO,QAAQ,EAAGC,CAAC,IAAKrO,YAAY,CAACqO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAC9CpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAElR,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAwO,QAAA,gBAEF5O,OAAA;0BAAQ+Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACtClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpClP,OAAA;0BAAQ+Q,KAAK,EAAC,UAAU;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClP,OAAA;0BAAQ+Q,KAAK,EAAC,MAAM;0BAAAnC,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAO2O,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACnGlP,OAAA;wBACE+Q,KAAK,EAAElO,UAAW;wBAClBmO,QAAQ,EAAGC,CAAC,IAAKnO,aAAa,CAACmO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAC/CpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAElR,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAwO,QAAA,gBAEF5O,OAAA;0BAAQ+Q,KAAK,EAAC,EAAE;0BAAAnC,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvClP,OAAA;0BAAQ+Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAO2O,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC7GlP,OAAA;wBACE+Q,KAAK,EAAE9N,eAAgB;wBACvB+N,QAAQ,EAAGC,CAAC,IAAK/N,kBAAkB,CAAC+N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBACpDpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAElR,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAwO,QAAA,gBAEF5O,OAAA;0BAAQ+Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAc;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAC1C/J,gBAAgB,CAAC0B,GAAG,CAAC+N,SAAS,iBAC7B5U,OAAA;0BAAwB+Q,KAAK,EAAE6D,SAAU;0BAAAhG,QAAA,EAAEgG;wBAAS,GAAvCA,SAAS;0BAAA7F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAuC,CAC9D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENlP,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAO2O,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtGlP,OAAA;wBACE+Q,KAAK,EAAE5N,aAAc;wBACrB6N,QAAQ,EAAGC,CAAC,IAAK7N,gBAAgB,CAAC6N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAClDpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAElR,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAwO,QAAA,gBAEF5O,OAAA;0BAAQ+Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EACxCjK,cAAc,CAAC4B,GAAG,CAACmD,OAAO,iBACzBhK,OAAA;0BAAsB+Q,KAAK,EAAE/G,OAAQ;0BAAA4E,QAAA,EAAE5E;wBAAO,GAAjCA,OAAO;0BAAA+E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAmC,CACxD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,EAGLzL,UAAU,KAAK,MAAM,iBACpBzD,OAAA;sBAAA4O,QAAA,gBACE5O,OAAA;wBAAO2O,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAqO,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACrGlP,OAAA;wBACE+Q,KAAK,EAAE1N,YAAa;wBACpB2N,QAAQ,EAAGC,CAAC,IAAK3N,eAAe,CAAC2N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBACjDpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAElR,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAwO,QAAA,gBAEF5O,OAAA;0BAAQ+Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzClP,OAAA;0BAAQ+Q,KAAK,EAAC,UAAU;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1ClP,OAAA;0BAAQ+Q,KAAK,EAAC,QAAQ;0BAAAnC,QAAA,EAAC;wBAAM;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAERlP,OAAA;kBAAK2O,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9B5O,OAAA;oBAAO2O,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpD5O,OAAA;sBAAO4P,KAAK,EAAE;wBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;sBAAK,CAAE;sBAAAwO,QAAA,eAC7D5O,OAAA;wBAAA4O,QAAA,gBACE5O,OAAA;0BAAI2O,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE9O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAwO,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAClIlP,OAAA;0BAAI2O,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE9O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAwO,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrIlP,OAAA;0BAAI2O,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE9O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAwO,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrIlP,OAAA;0BAAI2O,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE9O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAwO,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACtIzL,UAAU,KAAK,MAAM,iBACpBzD,OAAA,CAAAE,SAAA;0BAAA0O,QAAA,gBACE5O,OAAA;4BAAI2O,SAAS,EAAC,kEAAkE;4BAACiB,KAAK,EAAE;8BAAE9O,KAAK,EAAEX,YAAY,CAACC;4BAAQ,CAAE;4BAAAwO,QAAA,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACpIlP,OAAA;4BAAI2O,SAAS,EAAC,kEAAkE;4BAACiB,KAAK,EAAE;8BAAE9O,KAAK,EAAEX,YAAY,CAACC;4BAAQ,CAAE;4BAAAwO,QAAA,EAAC;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA,eACxI,CACH,EACAzL,UAAU,KAAK,SAAS,iBACvBzD,OAAA;0BAAI2O,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE9O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAwO,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CACrI;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACRlP,OAAA;sBAAO2O,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjDjC,aAAa,CAAClJ,UAAU,KAAK,SAAS,GAAGtB,cAAc,GAAGE,WAAW,CAAC,CAAC0F,MAAM,KAAK,CAAC,gBAClF/H,OAAA;wBAAA4O,QAAA,eACE5O,OAAA;0BAAIiU,OAAO,EAAExQ,UAAU,KAAK,SAAS,GAAG,CAAC,GAAG,CAAE;0BAACkL,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC9E5O,OAAA;4BAAK2O,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxD5O,OAAA;8BAAK2O,SAAS,EAAC,8BAA8B;8BAACqB,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAAC8C,MAAM,EAAC,cAAc;8BAAAnE,QAAA,eACjG5O,OAAA;gCAAMgT,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAAC/C,CAAC,EAAC;8BAAiI;gCAAApB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtM,CAAC,eACNlP,OAAA;8BAAI2O,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAAC,KAC7C,EAACnL,UAAU,KAAK,SAAS,GAAG,SAAS,GAAG,WAAW,EAAC,UACzD;4BAAA;8BAAAsL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACLlP,OAAA;8BAAG2O,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAC9B3M,KAAK,GACF,yFAAyF,GACzFwB,UAAU,KAAK,SAAS,GACtB,mGAAmG,GACnG;4BAAsF;8BAAAsL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3F,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAELvC,aAAa,CAAClJ,UAAU,KAAK,SAAS,GAAGtB,cAAc,GAAGE,WAAW,CAAC,CAACwE,GAAG,CAAE6C,MAAM;wBAAA,IAAAmL,iBAAA;wBAAA,oBAChF7U,OAAA,CAAChC,MAAM,CAAC8W,EAAE;0BAERhF,OAAO,EAAE;4BAAET,OAAO,EAAE;0BAAE,CAAE;0BACxBU,OAAO,EAAE;4BAAEV,OAAO,EAAE;0BAAE,CAAE;0BACxBV,SAAS,EAAC,iCAAiC;0BAC3CD,OAAO,EAAEA,CAAA,KAAMhM,iBAAiB,CAACgH,MAAM,CAAE;0BAAAkF,QAAA,gBAEzC5O,OAAA;4BAAI2O,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAC1E,IAAIrE,IAAI,CAAC9G,UAAU,KAAK,SAAS,GAAGiG,MAAM,CAACkB,aAAa,GAAGlB,MAAM,CAACyC,YAAY,IAAIzC,MAAM,CAACkB,aAAa,CAAC,CAACmK,kBAAkB,CAAC;0BAAC;4BAAAhG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3H,CAAC,eACLlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAE,EAAAiG,iBAAA,GAAAnL,MAAM,CAACoE,SAAS,cAAA+G,iBAAA,uBAAhBA,iBAAA,CAAkB9G,QAAQ,KAAI;0BAAK;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC5GlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAElF,MAAM,CAACzC,WAAW,IAAI;0BAAK;4BAAA8H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpGlP,OAAA;4BAAI2O,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAElF,MAAM,CAACtC,aAAa,IAAI;0BAAK;4BAAA2H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,EACrGzL,UAAU,KAAK,MAAM,iBACpBzD,OAAA,CAAAE,SAAA;4BAAA0O,QAAA,gBACE5O,OAAA;8BAAI2O,SAAS,EAAC,6BAA6B;8BAAAC,QAAA,eACzC5O,OAAA;gCACE2O,SAAS,EAAC,oEAAoE;gCAC9EiB,KAAK,EAAE;kCACL3O,eAAe,EAAEyI,MAAM,CAAChE,MAAM,KAAK,UAAU,GAAG,GAAGvF,YAAY,CAACK,MAAM,IAAI,GAAG,SAAS;kCACtFM,KAAK,EAAE4I,MAAM,CAAChE,MAAM,KAAK,UAAU,GAAGvF,YAAY,CAACK,MAAM,GAAG;gCAC9D,CAAE;gCAAAoO,QAAA,EAEDlF,MAAM,CAAChE;8BAAM;gCAAAqJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACLlP,OAAA;8BAAI2O,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,EAAElF,MAAM,CAACwC,cAAc,IAAI;4BAAK;8BAAA6C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA,eACvG,CACH,EACAzL,UAAU,KAAK,SAAS,iBACvBzD,OAAA;4BAAI2O,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,eACjD5O,OAAA;8BACE0O,OAAO,EAAEA,CAAA,KAAMhM,iBAAiB,CAACgH,MAAM,CAAE;8BACzCkG,KAAK,EAAE;gCAAE9O,KAAK,EAAEX,YAAY,CAACC;8BAAQ,CAAE;8BACvCuO,SAAS,EAAC,iBAAiB;8BAAAC,QAAA,EAC5B;4BAED;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CACL;wBAAA,GAtCIxF,MAAM,CAACsC,GAAG;0BAAA+C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuCN,CAAC;sBAAA,CACb;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELzM,cAAc,iBACbzC,OAAA;MAAK2O,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F5O,OAAA,CAAChC,MAAM,CAAC6R,GAAG;QACTC,OAAO,EAAE;UAAEU,KAAK,EAAE,GAAG;UAAEnB,OAAO,EAAE;QAAE,CAAE;QACpCU,OAAO,EAAE;UAAES,KAAK,EAAE,CAAC;UAAEnB,OAAO,EAAE;QAAE,CAAE;QAClCV,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzF5O,OAAA;UAAK2O,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5O,OAAA;YAAK2O,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD5O,OAAA;cAAI2O,SAAS,EAAC,oBAAoB;cAACiB,KAAK,EAAE;gBAAE9O,KAAK,EAAEX,YAAY,CAACC;cAAQ,CAAE;cAAAwO,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9FlP,OAAA;cAAQ0O,OAAO,EAAEA,CAAA,KAAMhM,iBAAiB,CAAC,IAAI,CAAE;cAACiM,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC3F5O,OAAA;gBAAK2O,SAAS,EAAC,SAAS;gBAACqB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAC8C,MAAM,EAAC,cAAc;gBAAAnE,QAAA,eAC5E5O,OAAA;kBAAMgT,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAC/C,CAAC,EAAC;gBAAsB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlP,OAAA;YAAK2O,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExB5O,OAAA;cAAK2O,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAwO,QAAA,gBACtF5O,OAAA;gBAAI2O,SAAS,EAAC,8CAA8C;gBAACiB,KAAK,EAAE;kBAAE9O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAwO,QAAA,gBAClG5O,OAAA,CAAC/B,QAAQ;kBAAC0Q,SAAS,EAAC,cAAc;kBAACiB,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACC;kBAAQ;gBAAE;kBAAA2O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAE/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlP,OAAA;gBAAK2O,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD5O,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DlP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,EAAAlN,qBAAA,GAAAe,cAAc,CAACqL,SAAS,cAAApM,qBAAA,uBAAxBA,qBAAA,CAA0BqM,QAAQ,KAAI;kBAAK;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,EAAAjN,sBAAA,GAAAc,cAAc,CAACqL,SAAS,cAAAnM,sBAAA,uBAAxBA,sBAAA,CAA0BqT,UAAU,KAAI;kBAAK;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlP,OAAA;cAAK2O,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAwO,QAAA,gBACtF5O,OAAA;gBAAI2O,SAAS,EAAC,8CAA8C;gBAACiB,KAAK,EAAE;kBAAE9O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAwO,QAAA,gBAClG5O,OAAA,CAAC9B,aAAa;kBAACyQ,SAAS,EAAC,cAAc;kBAACiB,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACC;kBAAQ;gBAAE;kBAAA2O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAEpF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlP,OAAA;gBAAK2O,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5O,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DlP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnM,cAAc,CAACwE,WAAW,IAAI;kBAAK;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,OAAOnM,cAAc,CAACsE,SAAS,KAAK,QAAQ,GAAGtE,cAAc,CAACsE,SAAS,CAACA,SAAS,GAAGtE,cAAc,CAACsE,SAAS,IAAI;kBAAK;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnM,cAAc,CAAC2E,aAAa,IAAI;kBAAK;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtCnM,cAAc,CAACmI,aAAa,GAAG,IAAIL,IAAI,CAAC9H,cAAc,CAACmI,aAAa,CAAC,CAACqK,cAAc,CAAC,CAAC,GAAG;kBAAK;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnM,cAAc,CAACgD,OAAO,IAAI;kBAAY;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlP,OAAA;cAAK2O,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAwO,QAAA,gBACtF5O,OAAA;gBAAI2O,SAAS,EAAC,8CAA8C;gBAACiB,KAAK,EAAE;kBAAE9O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAwO,QAAA,gBAClG5O,OAAA,CAAChB,SAAS;kBAAC2P,SAAS,EAAC,cAAc;kBAACiB,KAAK,EAAE;oBAAE9O,KAAK,EAAEX,YAAY,CAACC;kBAAQ;gBAAE;kBAAA2O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEhF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlP,OAAA,CAACF,kBAAkB;gBACjBoV,WAAW,EAAEzS,cAAc,CAACyS,WAAW,IAAI,EAAG;gBAC9C9N,aAAa,EAAE3E,cAAc,CAAC2E;cAAc;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLzM,cAAc,CAACiD,MAAM,KAAK,SAAS,iBAClC1F,OAAA;cAAK2O,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAwO,QAAA,gBACtF5O,OAAA;gBAAI2O,SAAS,EAAC,4BAA4B;gBAACiB,KAAK,EAAE;kBAAE9O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAwO,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrGlP,OAAA;gBAAK2O,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5O,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC5ET,WAAW,CAACpJ,UAAU,CAACE,gBAAgB,EAAG4E,MAAM,IAC/C7E,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,gBAAgB,EAAE4E;kBAAO,CAAC,CAC3D,CAAC;gBAAA;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC9ET,WAAW,CAACpJ,UAAU,CAACG,kBAAkB,EAAG2E,MAAM,IACjD7E,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEG,kBAAkB,EAAE2E;kBAAO,CAAC,CAC7D,CAAC;gBAAA;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9ElP,OAAA;oBACE+Q,KAAK,EAAE1L,UAAU,CAACI,OAAQ;oBAC1BuL,QAAQ,EAAGC,CAAC,IAAK3L,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEI,OAAO,EAAEwL,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAE;oBAC3EpC,SAAS,EAAC,oDAAoD;oBAC9DiB,KAAK,EAAE;sBACLuB,OAAO,EAAE,MAAM;sBACfC,SAAS,EAAE,MAAM;sBACjBC,WAAW,EAAE,SAAS;sBACtB,QAAQ,EAAE;wBAAEA,WAAW,EAAElR,YAAY,CAACC;sBAAQ;oBAChD,CAAE;oBACF+U,IAAI,EAAC,GAAG;oBACRhB,WAAW,EAAC;kBAA8B;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3ElP,OAAA;oBAAK2O,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9B5O,OAAA;sBACE2J,IAAI,EAAC,QAAQ;sBACb+E,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEK,MAAM,EAAE;sBAAW,CAAC,CAAE;sBACpEiJ,SAAS,EAAC,8DAA8D;sBACxEiB,KAAK,EAAE;wBACL3O,eAAe,EAAEoE,UAAU,CAACK,MAAM,KAAK,UAAU,GAC7CvF,YAAY,CAACK,MAAM,GACnB,SAAS;wBACbM,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,UAAU,GACnC,SAAS,GACTvF,YAAY,CAACI,IAAI;wBACrB6U,UAAU,EAAE/P,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG,QAAQ;wBAC/D2P,MAAM,EAAEhQ,UAAU,CAACK,MAAM,KAAK,UAAU,GACpC,aAAavF,YAAY,CAACK,MAAM,EAAE,GAClC;sBACN,CAAE;sBAAAoO,QAAA,gBAEF5O,OAAA,CAAC5B,OAAO;wBAACuQ,SAAS,EAAC,MAAM;wBAACiB,KAAK,EAAE;0BAC/B9O,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,SAAS,GAAGvF,YAAY,CAACK;wBACrE;sBAAE;wBAAAuO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTlP,OAAA;sBACE2J,IAAI,EAAC,QAAQ;sBACb+E,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEK,MAAM,EAAE;sBAAS,CAAC,CAAE;sBAClEiJ,SAAS,EAAC,8DAA8D;sBACxEiB,KAAK,EAAE;wBACL3O,eAAe,EAAEoE,UAAU,CAACK,MAAM,KAAK,QAAQ,GAC3C,SAAS,GACT,SAAS;wBACb5E,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,QAAQ,GACjC,SAAS,GACTvF,YAAY,CAACI,IAAI;wBACrB6U,UAAU,EAAE/P,UAAU,CAACK,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ;wBAC7D2P,MAAM,EAAEhQ,UAAU,CAACK,MAAM,KAAK,QAAQ,GAClC,mBAAmB,GACnB;sBACN,CAAE;sBAAAkJ,QAAA,gBAEF5O,OAAA,CAAC3B,OAAO;wBAACsQ,SAAS,EAAC,MAAM;wBAACiB,KAAK,EAAE;0BAC/B9O,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG;wBACtD;sBAAE;wBAAAqJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAEP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlP,OAAA;kBAAK2O,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBACjD5O,OAAA;oBAAI2O,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAE/EvL,cAAc,gBACb3D,OAAA;oBAAK2O,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnB5O,OAAA;sBAAK2O,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrD5O,OAAA;wBAAG2O,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAA0B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnElP,OAAA,CAAChC,MAAM,CAACsS,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1B9B,OAAO,EAAEA,CAAA,KAAMpJ,aAAa,CAAC8G,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEzG,mBAAmB,EAAEhC;wBAAe,CAAC,CAAC,CAAE;wBACzFgL,SAAS,EAAC,mDAAmD;wBAC7DiB,KAAK,EAAE;0BAAEtP,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;wBAAI,CAAE;wBAAAuO,QAAA,gBAEvG5O,OAAA,CAAClB,WAAW;0BAAC6P,SAAS,EAAC;wBAAM;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,SAClC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,EAEL7J,UAAU,CAACM,mBAAmB,iBAC7B3F,OAAA;sBAAK2O,SAAS,EAAC,uBAAuB;sBACpCiB,KAAK,EAAE;wBACLyB,WAAW,EAAE,SAAS;wBACtBpQ,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;sBAC1C,CAAE;sBAAAwO,QAAA,gBACF5O,OAAA;wBAAI2O,SAAS,EAAC,0BAA0B;wBAACiB,KAAK,EAAE;0BAAE9O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAwO,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACtG7J,UAAU,CAACM,mBAAmB,CAAC2P,UAAU,CAAC,YAAY,CAAC,gBACtDtV,OAAA;wBAAKuV,GAAG,EAAElQ,UAAU,CAACM,mBAAoB;wBAAC6P,GAAG,EAAC,WAAW;wBAAC7G,SAAS,EAAC;sBAAkB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEzFlP,OAAA;wBAAG2O,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAEvJ,UAAU,CAACM;sBAAmB;wBAAAoJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CACtF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENlP,OAAA;oBAAK2O,SAAS,EAAC,qBAAqB;oBAClCiB,KAAK,EAAE;sBACL3O,eAAe,EAAE,GAAGd,YAAY,CAACE,SAAS,IAAI;sBAC9CgV,MAAM,EAAE,aAAalV,YAAY,CAACE,SAAS;oBAC7C,CAAE;oBAAAuO,QAAA,gBACF5O,OAAA;sBAAG2O,SAAS,EAAC,cAAc;sBAACiB,KAAK,EAAE;wBAAE9O,KAAK,EAAEX,YAAY,CAACE;sBAAU,CAAE;sBAAAuO,QAAA,EAAC;oBAEtE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJlP,OAAA,CAAChC,MAAM,CAACsS,MAAM;sBACZC,UAAU,EAAE;wBAAEC,KAAK,EAAE;sBAAK,CAAE;sBAC5BC,QAAQ,EAAE;wBAAED,KAAK,EAAE;sBAAK,CAAE;sBAC1B9B,OAAO,EAAEA,CAAA,KAAM5K,qBAAqB,CAAC,IAAI,CAAE;sBAC3C6K,SAAS,EAAC,2DAA2D;sBACrEiB,KAAK,EAAE;wBAAE3O,eAAe,EAAEd,YAAY,CAACE;sBAAU,CAAE;sBAAAuO,QAAA,gBAEnD5O,OAAA,CAAClB,WAAW;wBAAC6P,SAAS,EAAC;sBAAM;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,qBAClC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENlP,OAAA;kBAAK2O,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1C5O,OAAA,CAAChC,MAAM,CAACsS,MAAM;oBACZC,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAC1B9B,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACjJ,cAAc,CAACuJ,GAAG,CAAE;oBACtDyJ,QAAQ,EAAEpQ,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAoB;oBACtHgJ,SAAS,EAAC,kDAAkD;oBAC5DiB,KAAK,EAAE;sBACL3O,eAAe,EAAEoE,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GACxH,SAAS,GACT,aAAa;sBACjBrF,UAAU,EAAE+E,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GACnH,SAAS,GACTN,UAAU,CAACK,MAAM,KAAK,UAAU,GAC9B,6BAA6BvF,YAAY,CAACK,MAAM,KAAKL,YAAY,CAACK,MAAM,GAAG,GAC3E,6CAA6C;sBACnDM,KAAK,EAAEuE,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GAC9G,SAAS,GACT,SAAS;sBACb+P,MAAM,EAAErQ,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GAC/G,aAAa,GACb;oBACN,CAAE;oBAAAiJ,QAAA,GAEDvJ,UAAU,CAACK,MAAM,KAAK,UAAU,gBAC/B1F,OAAA,CAAC5B,OAAO;sBAACuQ,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEpClP,OAAA,CAAC3B,OAAO;sBAACsQ,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CACpC,EAAC,SACK,EAAC7J,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,UAAU,GAAG,SAAS;kBAAA;oBAAAqJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGAzM,cAAc,CAACiD,MAAM,KAAK,SAAS,iBAClC1F,OAAA;cAAK2O,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAwO,QAAA,gBACtF5O,OAAA;gBAAI2O,SAAS,EAAC,4BAA4B;gBAACiB,KAAK,EAAE;kBAAE9O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAwO,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGlP,OAAA;gBAAK2O,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB5O,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnM,cAAc,CAACyJ,cAAc,IAAI;kBAAK;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpElP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtCnM,cAAc,CAAC0J,YAAY,GAAG,IAAI5B,IAAI,CAAC9H,cAAc,CAAC0J,YAAY,CAAC,CAAC8I,cAAc,CAAC,CAAC,GAAG;kBAAK;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACvET,WAAW,CAAChM,cAAc,CAAC8C,gBAAgB,IAAI,CAAC,CAAC;gBAAA;kBAAAwJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzET,WAAW,CAAChM,cAAc,CAAC+C,kBAAkB,IAAI,CAAC,CAAC;gBAAA;kBAAAuJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9DlP,OAAA;oBAAG2O,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnM,cAAc,CAACgD,OAAO,IAAI;kBAAY;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNlP,OAAA;kBAAA4O,QAAA,gBACE5O,OAAA;oBAAI2O,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7DlP,OAAA;oBACE2O,SAAS,EAAC,oEAAoE;oBAC9EiB,KAAK,EAAE;sBACL3O,eAAe,EAAEwB,cAAc,CAACiD,MAAM,KAAK,UAAU,GAAG,GAAGvF,YAAY,CAACK,MAAM,IAAI,GAAG,SAAS;sBAC9FM,KAAK,EAAE2B,cAAc,CAACiD,MAAM,KAAK,UAAU,GAAGvF,YAAY,CAACK,MAAM,GAAG;oBACtE,CAAE;oBAAAoO,QAAA,EAEDnM,cAAc,CAACiD;kBAAM;oBAAAqJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGNlP,OAAA;kBAAK2O,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBACjD5O,OAAA;oBAAI2O,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC/EzM,cAAc,CAACkD,mBAAmB,gBACjC3F,OAAA;oBAAK2O,SAAS,EAAC,uBAAuB;oBAACiB,KAAK,EAAE;sBAC5CyB,WAAW,EAAE,SAAS;sBACtBpQ,eAAe,EAAEd,YAAY,CAACG;oBAChC,CAAE;oBAAAsO,QAAA,EACCnM,cAAc,CAACkD,mBAAmB,CAAC2P,UAAU,CAAC,YAAY,CAAC,gBAC1DtV,OAAA;sBAAKuV,GAAG,EAAE9S,cAAc,CAACkD,mBAAoB;sBAAC6P,GAAG,EAAC,WAAW;sBAAC7G,SAAS,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAErFlP,OAAA;sBAAG2O,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAEnM,cAAc,CAACkD;oBAAmB;sBAAAoJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAC9E;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENlP,OAAA;oBAAG2O,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAC9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAGArL,kBAAkB,iBACjB7D,OAAA;MAAK2O,SAAS,EAAC,iGAAiG;MAAAC,QAAA,eAC9G5O,OAAA,CAAChC,MAAM,CAAC6R,GAAG;QACTC,OAAO,EAAE;UAAET,OAAO,EAAE,CAAC;UAAEmB,KAAK,EAAE;QAAI,CAAE;QACpCT,OAAO,EAAE;UAAEV,OAAO,EAAE,CAAC;UAAEmB,KAAK,EAAE;QAAE,CAAE;QAClCjB,UAAU,EAAE;UAAE5F,IAAI,EAAE,QAAQ;UAAEgM,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAE;QAC5DjH,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAExF5O,OAAA;UAAK2O,SAAS,EAAC,kBAAkB;UAACiB,KAAK,EAAE;YACvCtP,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;UAC1F,CAAE;UAAAuO,QAAA,eACA5O,OAAA;YAAK2O,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD5O,OAAA;cAAI2O,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC5D5O,OAAA,CAAClB,WAAW;gBAAC6P,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlP,OAAA;cACE0O,OAAO,EAAEA,CAAA,KAAM5K,qBAAqB,CAAC,KAAK,CAAE;cAC5C6K,SAAS,EAAC,kDAAkD;cAAAC,QAAA,eAE5D5O,OAAA;gBAAK2O,SAAS,EAAC,SAAS;gBAACqB,IAAI,EAAC,MAAM;gBAAC+C,MAAM,EAAC,cAAc;gBAAC9C,OAAO,EAAC,WAAW;gBAAArB,QAAA,eAC5E5O,OAAA;kBAAMgT,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAAC/C,CAAC,EAAC;gBAAsB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlP,OAAA;UAAK2O,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClB5O,OAAA;YAAK2O,SAAS,EAAC,gCAAgC;YAC7CiB,KAAK,EAAE;cACL3O,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;cAC5CiR,WAAW,EAAElR,YAAY,CAACC;YAC5B,CAAE;YAAAwO,QAAA,eACF5O,OAAA;cAAG4P,KAAK,EAAE;gBAAE9O,KAAK,EAAEX,YAAY,CAACC;cAAQ,CAAE;cAAAwO,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENlP,OAAA,CAACH,gBAAgB;YACfgW,gBAAgB,EAAElS,cAAe;YACjCmS,iBAAiB,EAAGxO,SAAS,IAAK;cAChC1D,iBAAiB,CAAC0D,SAAS,CAAC;cAC5B;cACAgF,UAAU,CAAC,MAAMxI,qBAAqB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;YACtD;UAAE;YAAAiL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAGAnL,gBAAgB,iBACf/D,OAAA,CAAChC,MAAM,CAAC6R,GAAG;MACTC,OAAO,EAAE;QAAET,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCK,OAAO,EAAE;QAAEV,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE;MAAE,CAAE;MAC9BqG,IAAI,EAAE;QAAE1G,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE;MAAG,CAAE;MAC5Bf,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eAEhF5O,OAAA;QAAK2O,SAAS,EAAC,gFAAgF;QAC7FiB,KAAK,EAAE;UAAEyB,WAAW,EAAElR,YAAY,CAACK;QAAO,CAAE;QAAAoO,QAAA,gBAC5C5O,OAAA;UAAK2O,SAAS,EAAC,uBAAuB;UAACiB,KAAK,EAAE;YAAE3O,eAAe,EAAE,GAAGd,YAAY,CAACK,MAAM;UAAK,CAAE;UAAAoO,QAAA,eAC5F5O,OAAA,CAAC5B,OAAO;YAACuQ,SAAS,EAAC,SAAS;YAACiB,KAAK,EAAE;cAAE9O,KAAK,EAAEX,YAAY,CAACK;YAAO;UAAE;YAAAuO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNlP,OAAA;UAAK2O,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrB5O,OAAA;YAAI2O,SAAS,EAAC,aAAa;YAACiB,KAAK,EAAE;cAAE9O,KAAK,EAAEX,YAAY,CAACI;YAAK,CAAE;YAAAqO,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ElP,OAAA;YAAG4P,KAAK,EAAE;cAAE9O,KAAK,EAAE;YAAU,CAAE;YAAA8N,QAAA,EAAE3K;UAAc;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNlP,OAAA;UACE0O,OAAO,EAAEA,CAAA,KAAM1K,mBAAmB,CAAC,KAAK,CAAE;UAC1C2K,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7C5O,OAAA,CAAC3B,OAAO;YAAA0Q,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzN,EAAA,CAhyDID,mBAAmB;EAAA,QACN7D,WAAW,EACJE,OAAO;AAAA;AAAAmY,EAAA,GAF3BxU,mBAAmB;AAkyDzB,eAAeA,mBAAmB;AAAC,IAAAwU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}