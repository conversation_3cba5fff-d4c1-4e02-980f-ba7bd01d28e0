{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\assistant\\\\Patients.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AssistantSidebar from './AssistantSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaUsers, FaUserAlt, FaNotesMedical, FaCalendarAlt } from 'react-icons/fa';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\n// Helper function to format appointment date and time in Cairo timezone\nconst formatAppointmentDateTime = appointment => {\n  try {\n    // Get the date from the appointment\n    const date = new Date(appointment.start || appointment.date || appointment.appointmentDate);\n\n    // Format the date in Cairo timezone (UTC+2)\n    const dateOptions = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      timeZone: 'Africa/Cairo'\n    };\n    const formattedDate = date.toLocaleDateString('en-US', dateOptions);\n\n    // Get the time from the appointment object or use the date's time\n    let timeString = appointment.time;\n\n    // If time is not available in the appointment object, format the time from the date\n    if (!timeString) {\n      const timeOptions = {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true,\n        timeZone: 'Africa/Cairo'\n      };\n      timeString = date.toLocaleTimeString('en-US', timeOptions);\n    }\n    return `${formattedDate} at ${timeString}`;\n  } catch (error) {\n    console.error('Error formatting appointment date:', error);\n    return 'Date not available';\n  }\n};\nconst Patients = () => {\n  _s();\n  var _patientDetails$drId, _patientDetails$medic, _patientDetails$medic2, _patientDetails$appoi;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patients, setPatients] = useState([]);\n  const [selectedPatient, setSelectedPatient] = useState(null);\n  const [patientDetails, setPatientDetails] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchPatients = async () => {\n      if (!user || !token) {\n        setError('Please log in to view data.');\n        setLoading(false);\n        return;\n      }\n      try {\n        setLoading(true);\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        const response = await axios.get(`http://localhost:5000/api/assistant/patients`, config);\n        setPatients(response.data || []);\n        if (response.data.length === 0) setError('No patients found for your university.');\n      } catch (err) {\n        var _err$response, _err$response2;\n        console.error('Fetch error:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n        setError('Failed to load patients');\n        if (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 401) navigate('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPatients();\n  }, [user, token, navigate]);\n  useEffect(() => {\n    const fetchPatientDetails = async () => {\n      if (selectedPatient && selectedPatient.nationalId) {\n        try {\n          const config = {\n            headers: {\n              Authorization: `Bearer ${token}`\n            }\n          };\n          const patientResponse = await axios.get(`http://localhost:5000/api/assistant/patients/${selectedPatient.nationalId}`, config);\n          setPatientDetails(patientResponse.data);\n        } catch (err) {\n          var _err$response3;\n          console.error('Error fetching patient details:', ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.data) || err.message);\n          setError('Failed to load patient details.');\n        }\n      }\n    };\n    fetchPatientDetails();\n  }, [selectedPatient, token]);\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(AssistantSidebar, {\n          isOpen: sidebarOpen,\n          setIsOpen: setSidebarOpen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-8\",\n          children: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex\",\n      children: [/*#__PURE__*/_jsxDEV(AssistantSidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"Patients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage and view all patients in your university\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), error ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-6 py-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: \"All Patients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), patients.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full divide-y divide-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"National ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"Assigned Student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"Actions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  className: \"bg-white divide-y divide-gray-200\",\n                  children: patients.map(patient => {\n                    var _patient$drId;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"hover:bg-gray-50 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                        children: patient.fullName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 176,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: patient.nationalId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 179,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: ((_patient$drId = patient.drId) === null || _patient$drId === void 0 ? void 0 : _patient$drId.name) || 'None'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 182,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => setSelectedPatient(patient),\n                          className: \"text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200\",\n                          children: \"View Details\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 186,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 185,\n                        columnNumber: 29\n                      }, this)]\n                    }, patient._id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-2 text-sm font-medium text-gray-900\",\n                children: \"No patients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"No patients found for your university.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), selectedPatient && patientDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"Patient Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSelectedPatient(null);\n                setPatientDetails(null);\n              },\n              className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-blue-600 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaUserAlt, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), \"Personal Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-4 rounded-lg shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Full Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1 font-medium\",\n                    children: patientDetails.fullName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"National ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: patientDetails.nationalId\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: patientDetails.phoneNumber || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Age\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: patientDetails.age || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Assigned Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1 font-medium\",\n                    children: ((_patientDetails$drId = patientDetails.drId) === null || _patientDetails$drId === void 0 ? void 0 : _patientDetails$drId.name) || 'None'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-blue-600 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaNotesMedical, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this), \"Medical History\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-4 rounded-lg shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Chief Complaint\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: ((_patientDetails$medic = patientDetails.medicalInfo) === null || _patientDetails$medic === void 0 ? void 0 : _patientDetails$medic.chiefComplaint) || 'None reported'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pt-2 border-t border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Current Medications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: ((_patientDetails$medic2 = patientDetails.medicalInfo) === null || _patientDetails$medic2 === void 0 ? void 0 : _patientDetails$medic2.currentMedications) || 'None reported'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-blue-600 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), \"Appointments\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-4 rounded-lg shadow-sm\",\n              children: ((_patientDetails$appoi = patientDetails.appointments) === null || _patientDetails$appoi === void 0 ? void 0 : _patientDetails$appoi.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"divide-y divide-gray-100\",\n                children: patientDetails.appointments.map((appt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-3 first:pt-0 last:pb-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-100 rounded-full p-2 mr-3 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                        className: \"h-4 w-4 text-blue-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-between items-start\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-medium text-gray-900\",\n                          children: appt.title || appt.description || 'Appointment'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 299,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${appt.status === 'completed' ? 'bg-green-100 text-green-800' : appt.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-amber-100 text-amber-800'}`,\n                          children: appt.status ? appt.status.charAt(0).toUpperCase() + appt.status.slice(1) : 'Pending'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 300,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: [appt.date, \" at \", appt.time]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 27\n                  }, this)\n                }, appt._id || index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-6 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"mx-auto h-10 w-10 text-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-2 text-sm text-gray-500 italic\",\n                  children: \"No appointments scheduled.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 140,\n    columnNumber: 5\n  }, this);\n};\n_s(Patients, \"8AxZnt4BQ8v/BwdphiAcCoBlVqU=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Patients;\nexport default Patients;\nvar _c;\n$RefreshReg$(_c, \"Patients\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Assistant<PERSON><PERSON><PERSON>", "Loader", "motion", "FaUsers", "FaUserAlt", "FaNotesMedical", "FaCalendarAlt", "jsxDEV", "_jsxDEV", "websiteColorPalette", "primary", "secondary", "background", "text", "accent", "formatAppointmentDateTime", "appointment", "date", "Date", "start", "appointmentDate", "dateOptions", "year", "month", "day", "timeZone", "formattedDate", "toLocaleDateString", "timeString", "time", "timeOptions", "hour", "minute", "hour12", "toLocaleTimeString", "error", "console", "Patients", "_s", "_patientDetails$drId", "_patientDetails$medic", "_patientDetails$medic2", "_patientDetails$appoi", "sidebarOpen", "setSidebarOpen", "patients", "setPatients", "selectedPatient", "setSelectedPatient", "patientDetails", "setPatientDetails", "loading", "setLoading", "setError", "navigate", "user", "token", "fetchPatients", "config", "headers", "Authorization", "response", "get", "data", "length", "err", "_err$response", "_err$response2", "message", "status", "fetchPatientDetails", "nationalId", "patientResponse", "_err$response3", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "setIsOpen", "map", "patient", "_patient$drId", "fullName", "drId", "name", "onClick", "_id", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "phoneNumber", "age", "medicalInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentMedications", "appointments", "appt", "index", "title", "description", "char<PERSON>t", "toUpperCase", "slice", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/assistant/Patients.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from '../student/Navbar';\r\nimport AssistantSidebar from './AssistantSidebar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport { FaUsers, FaUserAlt, FaNotesMedical, FaCalendarAlt } from 'react-icons/fa';\r\n\r\n// Website color palette\r\nconst websiteColorPalette = {\r\n  primary: '#0077B6',\r\n  secondary: '#20B2AA',\r\n  background: '#FFFFFF',\r\n  text: '#333333',\r\n  accent: '#28A745'\r\n};\r\n\r\n// Helper function to format appointment date and time in Cairo timezone\r\nconst formatAppointmentDateTime = (appointment) => {\r\n  try {\r\n    // Get the date from the appointment\r\n    const date = new Date(appointment.start || appointment.date || appointment.appointmentDate);\r\n\r\n    // Format the date in Cairo timezone (UTC+2)\r\n    const dateOptions = {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n      timeZone: 'Africa/Cairo'\r\n    };\r\n    const formattedDate = date.toLocaleDateString('en-US', dateOptions);\r\n\r\n    // Get the time from the appointment object or use the date's time\r\n    let timeString = appointment.time;\r\n\r\n    // If time is not available in the appointment object, format the time from the date\r\n    if (!timeString) {\r\n      const timeOptions = {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true,\r\n        timeZone: 'Africa/Cairo'\r\n      };\r\n      timeString = date.toLocaleTimeString('en-US', timeOptions);\r\n    }\r\n\r\n    return `${formattedDate} at ${timeString}`;\r\n  } catch (error) {\r\n    console.error('Error formatting appointment date:', error);\r\n    return 'Date not available';\r\n  }\r\n};\r\n\r\nconst Patients = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [patients, setPatients] = useState([]);\r\n  const [selectedPatient, setSelectedPatient] = useState(null);\r\n  const [patientDetails, setPatientDetails] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n\r\n  useEffect(() => {\r\n    const fetchPatients = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view data.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n\r\n        const response = await axios.get(\r\n          `http://localhost:5000/api/assistant/patients`,\r\n          config\r\n        );\r\n        setPatients(response.data || []);\r\n\r\n        if (response.data.length === 0) setError('No patients found for your university.');\r\n      } catch (err) {\r\n        console.error('Fetch error:', err.response?.data || err.message);\r\n        setError('Failed to load patients');\r\n        if (err.response?.status === 401) navigate('/login');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchPatients();\r\n  }, [user, token, navigate]);\r\n\r\n  useEffect(() => {\r\n    const fetchPatientDetails = async () => {\r\n      if (selectedPatient && selectedPatient.nationalId) {\r\n        try {\r\n          const config = { headers: { Authorization: `Bearer ${token}` } };\r\n          const patientResponse = await axios.get(\r\n            `http://localhost:5000/api/assistant/patients/${selectedPatient.nationalId}`,\r\n            config\r\n          );\r\n          setPatientDetails(patientResponse.data);\r\n        } catch (err) {\r\n          console.error('Error fetching patient details:', err.response?.data || err.message);\r\n          setError('Failed to load patient details.');\r\n        }\r\n      }\r\n    };\r\n    fetchPatientDetails();\r\n  }, [selectedPatient, token]);\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <Navbar />\r\n        <div className=\"flex\">\r\n          <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n          <div className=\"flex-1 p-8\">\r\n            <Loader />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Navbar />\r\n      <div className=\"flex\">\r\n        <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        \r\n        <div className=\"flex-1 p-8\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <div className=\"mb-8\">\r\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Patients</h1>\r\n              <p className=\"text-gray-600\">Manage and view all patients in your university</p>\r\n            </div>\r\n\r\n            {error ? (\r\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n                <p className=\"text-red-800\">{error}</p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n                <div className=\"px-6 py-4 border-b border-gray-200\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900\">All Patients</h3>\r\n                </div>\r\n                \r\n                {patients.length > 0 ? (\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">National ID</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Assigned Student</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {patients.map((patient) => (\r\n                          <tr key={patient._id} className=\"hover:bg-gray-50 transition-colors duration-200\">\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                              {patient.fullName}\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                              {patient.nationalId}\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                              {patient.drId?.name || 'None'}\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                              <button\r\n                                onClick={() => setSelectedPatient(patient)}\r\n                                className=\"text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200\"\r\n                              >\r\n                                View Details\r\n                              </button>\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-12\">\r\n                    <FaUsers className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n                    <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No patients</h3>\r\n                    <p className=\"mt-1 text-sm text-gray-500\">No patients found for your university.</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Patient Details Modal */}\r\n      {selectedPatient && patientDetails && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6 border-b border-gray-200\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-semibold text-gray-900\">Patient Details</h2>\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedPatient(null);\r\n                    setPatientDetails(null);\r\n                  }}\r\n                  className=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\r\n                >\r\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-6 space-y-6\">\r\n              <div className=\"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\">\r\n                <h4 className=\"text-lg font-semibold text-blue-600 mb-4 flex items-center\">\r\n                  <FaUserAlt className=\"h-5 w-5 mr-2 text-blue-600\" />\r\n                  Personal Information\r\n                </h4>\r\n                <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Full Name</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1 font-medium\">{patientDetails.fullName}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{patientDetails.nationalId}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Phone</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{patientDetails.phoneNumber || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Age</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{patientDetails.age || 'N/A'}</p>\r\n                    </div>\r\n                    <div className=\"md:col-span-2\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Assigned Student</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1 font-medium\">{patientDetails.drId?.name || 'None'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\">\r\n                <h4 className=\"text-lg font-semibold text-blue-600 mb-4 flex items-center\">\r\n                  <FaNotesMedical className=\"h-5 w-5 mr-2 text-blue-600\" />\r\n                  Medical History\r\n                </h4>\r\n                <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Chief Complaint</h4>\r\n                      <p className=\"text-sm text-gray-900\">{patientDetails.medicalInfo?.chiefComplaint || 'None reported'}</p>\r\n                    </div>\r\n                    <div className=\"pt-2 border-t border-gray-100\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Current Medications</h4>\r\n                      <p className=\"text-sm text-gray-900\">{patientDetails.medicalInfo?.currentMedications || 'None reported'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\">\r\n                <h4 className=\"text-lg font-semibold text-blue-600 mb-4 flex items-center\">\r\n                  <FaCalendarAlt className=\"h-5 w-5 mr-2 text-blue-600\" />\r\n                  Appointments\r\n                </h4>\r\n                <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                  {patientDetails.appointments?.length > 0 ? (\r\n                    <div className=\"divide-y divide-gray-100\">\r\n                      {patientDetails.appointments.map((appt, index) => (\r\n                        <div key={appt._id || index} className=\"py-3 first:pt-0 last:pb-0\">\r\n                          <div className=\"flex items-start\">\r\n                            <div className=\"bg-blue-100 rounded-full p-2 mr-3 mt-1\">\r\n                              <FaCalendarAlt className=\"h-4 w-4 text-blue-600\" />\r\n                            </div>\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"flex justify-between items-start\">\r\n                                <div className=\"font-medium text-gray-900\">{appt.title || appt.description || 'Appointment'}</div>\r\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                                  appt.status === 'completed' ? 'bg-green-100 text-green-800' :\r\n                                  appt.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-amber-100 text-amber-800'\r\n                                }`}>\r\n                                  {appt.status ? appt.status.charAt(0).toUpperCase() + appt.status.slice(1) : 'Pending'}\r\n                                </span>\r\n                              </div>\r\n                              <div className=\"text-sm text-gray-600 mt-1\">\r\n                                {appt.date} at {appt.time}\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"py-6 text-center\">\r\n                      <FaCalendarAlt className=\"mx-auto h-10 w-10 text-gray-300\" />\r\n                      <p className=\"mt-2 text-sm text-gray-500 italic\">No appointments scheduled.</p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Patients; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAEC,aAAa,QAAQ,gBAAgB;;AAElF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,MAAMC,yBAAyB,GAAIC,WAAW,IAAK;EACjD,IAAI;IACF;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,WAAW,CAACG,KAAK,IAAIH,WAAW,CAACC,IAAI,IAAID,WAAW,CAACI,eAAe,CAAC;;IAE3F;IACA,MAAMC,WAAW,GAAG;MAClBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAMC,aAAa,GAAGT,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAEN,WAAW,CAAC;;IAEnE;IACA,IAAIO,UAAU,GAAGZ,WAAW,CAACa,IAAI;;IAEjC;IACA,IAAI,CAACD,UAAU,EAAE;MACf,MAAME,WAAW,GAAG;QAClBC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZR,QAAQ,EAAE;MACZ,CAAC;MACDG,UAAU,GAAGX,IAAI,CAACiB,kBAAkB,CAAC,OAAO,EAAEJ,WAAW,CAAC;IAC5D;IAEA,OAAO,GAAGJ,aAAa,OAAOE,UAAU,EAAE;EAC5C,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,OAAO,oBAAoB;EAC7B;AACF,CAAC;AAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEkB,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM4D,QAAQ,GAAG1D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2D,IAAI;IAAEC;EAAM,CAAC,GAAG1D,OAAO,CAAC,CAAC;EAEjCH,SAAS,CAAC,MAAM;IACd,MAAM8D,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBH,QAAQ,CAAC,6BAA6B,CAAC;QACvCD,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACFA,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMM,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUJ,KAAK;UAAG;QAAE,CAAC;QAEhE,MAAMK,QAAQ,GAAG,MAAMhE,KAAK,CAACiE,GAAG,CAC9B,8CAA8C,EAC9CJ,MACF,CAAC;QACDZ,WAAW,CAACe,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;QAEhC,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,KAAK,CAAC,EAAEX,QAAQ,CAAC,wCAAwC,CAAC;MACpF,CAAC,CAAC,OAAOY,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA;QACZ/B,OAAO,CAACD,KAAK,CAAC,cAAc,EAAE,EAAA+B,aAAA,GAAAD,GAAG,CAACJ,QAAQ,cAAAK,aAAA,uBAAZA,aAAA,CAAcH,IAAI,KAAIE,GAAG,CAACG,OAAO,CAAC;QAChEf,QAAQ,CAAC,yBAAyB,CAAC;QACnC,IAAI,EAAAc,cAAA,GAAAF,GAAG,CAACJ,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcE,MAAM,MAAK,GAAG,EAAEf,QAAQ,CAAC,QAAQ,CAAC;MACtD,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDK,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3B3D,SAAS,CAAC,MAAM;IACd,MAAM2E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAIvB,eAAe,IAAIA,eAAe,CAACwB,UAAU,EAAE;QACjD,IAAI;UACF,MAAMb,MAAM,GAAG;YAAEC,OAAO,EAAE;cAAEC,aAAa,EAAE,UAAUJ,KAAK;YAAG;UAAE,CAAC;UAChE,MAAMgB,eAAe,GAAG,MAAM3E,KAAK,CAACiE,GAAG,CACrC,gDAAgDf,eAAe,CAACwB,UAAU,EAAE,EAC5Eb,MACF,CAAC;UACDR,iBAAiB,CAACsB,eAAe,CAACT,IAAI,CAAC;QACzC,CAAC,CAAC,OAAOE,GAAG,EAAE;UAAA,IAAAQ,cAAA;UACZrC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAE,EAAAsC,cAAA,GAAAR,GAAG,CAACJ,QAAQ,cAAAY,cAAA,uBAAZA,cAAA,CAAcV,IAAI,KAAIE,GAAG,CAACG,OAAO,CAAC;UACnFf,QAAQ,CAAC,iCAAiC,CAAC;QAC7C;MACF;IACF,CAAC;IACDiB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACvB,eAAe,EAAES,KAAK,CAAC,CAAC;EAE5B,MAAMkB,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACE3C,OAAA;MAAK0E,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC3E,OAAA,CAACT,MAAM;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACV/E,OAAA;QAAK0E,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3E,OAAA,CAACR,gBAAgB;UAACwF,MAAM,EAAE7C,WAAY;UAAC8C,SAAS,EAAE7C;QAAe;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpE/E,OAAA;UAAK0E,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB3E,OAAA,CAACP,MAAM;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/E,OAAA;IAAK0E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC3E,OAAA,CAACT,MAAM;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV/E,OAAA;MAAK0E,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB3E,OAAA,CAACR,gBAAgB;QAACwF,MAAM,EAAE7C,WAAY;QAAC8C,SAAS,EAAE7C;MAAe;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpE/E,OAAA;QAAK0E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB3E,OAAA;UAAK0E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3E,OAAA;YAAK0E,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB3E,OAAA;cAAI0E,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnE/E,OAAA;cAAG0E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,EAELpD,KAAK,gBACJ3B,OAAA;YAAK0E,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClE3E,OAAA;cAAG0E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEhD;YAAK;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAEN/E,OAAA;YAAK0E,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF3E,OAAA;cAAK0E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjD3E,OAAA;gBAAI0E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EAEL1C,QAAQ,CAACmB,MAAM,GAAG,CAAC,gBAClBxD,OAAA;cAAK0E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B3E,OAAA;gBAAO0E,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBACpD3E,OAAA;kBAAO0E,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAC3B3E,OAAA;oBAAA2E,QAAA,gBACE3E,OAAA;sBAAI0E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxG/E,OAAA;sBAAI0E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/G/E,OAAA;sBAAI0E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpH/E,OAAA;sBAAI0E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACR/E,OAAA;kBAAO0E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EACjDtC,QAAQ,CAAC6C,GAAG,CAAEC,OAAO;oBAAA,IAAAC,aAAA;oBAAA,oBACpBpF,OAAA;sBAAsB0E,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,gBAC/E3E,OAAA;wBAAI0E,SAAS,EAAC,+DAA+D;wBAAAC,QAAA,EAC1EQ,OAAO,CAACE;sBAAQ;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC,eACL/E,OAAA;wBAAI0E,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC9DQ,OAAO,CAACpB;sBAAU;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACL/E,OAAA;wBAAI0E,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC9D,EAAAS,aAAA,GAAAD,OAAO,CAACG,IAAI,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,IAAI,KAAI;sBAAM;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC,eACL/E,OAAA;wBAAI0E,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,eAC/D3E,OAAA;0BACEwF,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAAC2C,OAAO,CAAE;0BAC3CT,SAAS,EAAC,8EAA8E;0BAAAC,QAAA,EACzF;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC;oBAAA,GAjBEI,OAAO,CAACM,GAAG;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkBhB,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAEN/E,OAAA;cAAK0E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3E,OAAA,CAACL,OAAO;gBAAC+E,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvD/E,OAAA;gBAAI0E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvE/E,OAAA;gBAAG0E,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLxC,eAAe,IAAIE,cAAc,iBAChCzC,OAAA;MAAK0E,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F3E,OAAA;QAAK0E,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAChF3E,OAAA;UAAK0E,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3C3E,OAAA;YAAK0E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD3E,OAAA;cAAI0E,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE/E,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM;gBACbhD,kBAAkB,CAAC,IAAI,CAAC;gBACxBE,iBAAiB,CAAC,IAAI,CAAC;cACzB,CAAE;cACFgC,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAE5E3E,OAAA;gBAAK0E,SAAS,EAAC,SAAS;gBAACgB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAAjB,QAAA,eAC5E3E,OAAA;kBAAM6F,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA;UAAK0E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3E,OAAA;YAAK0E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE3E,OAAA;cAAI0E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE3E,OAAA,CAACJ,SAAS;gBAAC8E,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/E,OAAA;cAAK0E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAChD3E,OAAA;gBAAK0E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD3E,OAAA;kBAAA2E,QAAA,gBACE3E,OAAA;oBAAI0E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChE/E,OAAA;oBAAG0E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAElC,cAAc,CAAC4C;kBAAQ;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACN/E,OAAA;kBAAA2E,QAAA,gBACE3E,OAAA;oBAAI0E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClE/E,OAAA;oBAAG0E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAElC,cAAc,CAACsB;kBAAU;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACN/E,OAAA;kBAAA2E,QAAA,gBACE3E,OAAA;oBAAI0E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5D/E,OAAA;oBAAG0E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAElC,cAAc,CAACwD,WAAW,IAAI;kBAAK;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACN/E,OAAA;kBAAA2E,QAAA,gBACE3E,OAAA;oBAAI0E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1D/E,OAAA;oBAAG0E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAElC,cAAc,CAACyD,GAAG,IAAI;kBAAK;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACN/E,OAAA;kBAAK0E,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B3E,OAAA;oBAAI0E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvE/E,OAAA;oBAAG0E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAE,EAAA5C,oBAAA,GAAAU,cAAc,CAAC6C,IAAI,cAAAvD,oBAAA,uBAAnBA,oBAAA,CAAqBwD,IAAI,KAAI;kBAAM;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/E,OAAA;YAAK0E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE3E,OAAA;cAAI0E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE3E,OAAA,CAACH,cAAc;gBAAC6E,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/E,OAAA;cAAK0E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAChD3E,OAAA;gBAAK0E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB3E,OAAA;kBAAA2E,QAAA,gBACE3E,OAAA;oBAAI0E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3E/E,OAAA;oBAAG0E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE,EAAA3C,qBAAA,GAAAS,cAAc,CAAC0D,WAAW,cAAAnE,qBAAA,uBAA1BA,qBAAA,CAA4BoE,cAAc,KAAI;kBAAe;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,eACN/E,OAAA;kBAAK0E,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5C3E,OAAA;oBAAI0E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/E/E,OAAA;oBAAG0E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE,EAAA1C,sBAAA,GAAAQ,cAAc,CAAC0D,WAAW,cAAAlE,sBAAA,uBAA1BA,sBAAA,CAA4BoE,kBAAkB,KAAI;kBAAe;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/E,OAAA;YAAK0E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE3E,OAAA;cAAI0E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE3E,OAAA,CAACF,aAAa;gBAAC4E,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/E,OAAA;cAAK0E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC/C,EAAAzC,qBAAA,GAAAO,cAAc,CAAC6D,YAAY,cAAApE,qBAAA,uBAA3BA,qBAAA,CAA6BsB,MAAM,IAAG,CAAC,gBACtCxD,OAAA;gBAAK0E,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACtClC,cAAc,CAAC6D,YAAY,CAACpB,GAAG,CAAC,CAACqB,IAAI,EAAEC,KAAK,kBAC3CxG,OAAA;kBAA6B0E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eAChE3E,OAAA;oBAAK0E,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/B3E,OAAA;sBAAK0E,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,eACrD3E,OAAA,CAACF,aAAa;wBAAC4E,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACN/E,OAAA;sBAAK0E,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrB3E,OAAA;wBAAK0E,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C3E,OAAA;0BAAK0E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAE4B,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,WAAW,IAAI;wBAAa;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAClG/E,OAAA;0BAAM0E,SAAS,EAAE,2EACf6B,IAAI,CAAC1C,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC3D0C,IAAI,CAAC1C,MAAM,KAAK,WAAW,GAAG,yBAAyB,GAAG,6BAA6B,EACtF;0BAAAc,QAAA,EACA4B,IAAI,CAAC1C,MAAM,GAAG0C,IAAI,CAAC1C,MAAM,CAAC8C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGL,IAAI,CAAC1C,MAAM,CAACgD,KAAK,CAAC,CAAC,CAAC,GAAG;wBAAS;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN/E,OAAA;wBAAK0E,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,GACxC4B,IAAI,CAAC9F,IAAI,EAAC,MAAI,EAAC8F,IAAI,CAAClF,IAAI;sBAAA;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAnBEwB,IAAI,CAACd,GAAG,IAAIe,KAAK;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBtB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN/E,OAAA;gBAAK0E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B3E,OAAA,CAACF,aAAa;kBAAC4E,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7D/E,OAAA;kBAAG0E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjD,EAAA,CAjRID,QAAQ;EAAA,QAOKzC,WAAW,EACJE,OAAO;AAAA;AAAAwH,EAAA,GAR3BjF,QAAQ;AAmRd,eAAeA,QAAQ;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}