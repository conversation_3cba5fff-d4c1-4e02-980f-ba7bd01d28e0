import { Link } from 'react-router-dom';
import {
  FaHome,
  FaCalendarAlt,
  FaChartLine,
  <PERSON>a<PERSON><PERSON><PERSON>,
  Fa<PERSON><PERSON>board<PERSON>ist,
  FaUsers,
} from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const AssistantSidebar = ({ isOpen, setIsOpen }) => {
  const { user } = useAuth();

  const navItems = [
    { name: 'Dashboard', icon: <FaHome className="h-5 w-5" />, path: '/assistant/dashboard' },
    { name: 'Appointments', icon: <FaCalendarAlt className="h-5 w-5" />, path: '/assistant/appointments' },
    { name: 'Patients', icon: <FaUsers className="h-5 w-5" />, path: '/assistant/patients' },
    { name: 'Procedure Requests', icon: <FaClipboardList className="h-5 w-5" />, path: '/assistant/procedure-requests' },
    { name: 'Analytics', icon: <FaChartLine className="h-5 w-5" />, path: '/assistant/analytics' },
  ];

  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      <div
        className={`fixed md:relative z-20 h-full transition-all duration-300 ease-in-out ${
          isOpen ? 'translate-x-0 w-64' : '-translate-x-full md:translate-x-0 w-20'
        } bg-white shadow-lg flex flex-col`}
      >
        <div className="p-4 flex items-center justify-center">
          <Link to="/" className="flex items-center justify-center">
            <FaTooth className="w-8 h-8 transition-transform duration-300 hover:scale-110" style={{ color: colorPalette.primary }} />
          </Link>
          {isOpen && (
            <Link to="/" className="text-2xl font-bold ml-2 transition-colors duration-300" style={{ color: colorPalette.primary }}>
              DENT<span style={{ color: colorPalette.secondary }}>LYZER</span>
            </Link>
          )}
        </div>

        <nav className="flex-1 px-2 py-4 overflow-y-auto">
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.name}>
                <Link
                  to={item.path}
                  className="flex items-center p-3 rounded-lg transition-colors group hover:bg-blue-50"
                  style={{
                    color: colorPalette.text
                  }}
                  onClick={() => window.innerWidth < 768 && setIsOpen(false)}
                >
                  <span className="group-hover:text-blue-600" style={{ color: colorPalette.text }}>{item.icon}</span>
                  {isOpen && <span className="ml-3">{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="p-4 border-t border-gray-200">
          <Link to="/profile" className="flex items-center">
            <div className="h-10 w-10 rounded-full flex items-center justify-center"
              style={{
                backgroundColor: `${colorPalette.primary}15`,
                color: colorPalette.primary
              }}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            {isOpen && (
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">{user?.name || 'Assistant'}</p>
                <p className="text-xs text-gray-500">{user?.university || 'University'}</p>
              </div>
            )}
          </Link>
        </div>
      </div>
    </>
  );
};

export default AssistantSidebar;
