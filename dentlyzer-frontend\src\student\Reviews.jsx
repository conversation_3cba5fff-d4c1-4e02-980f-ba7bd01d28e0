import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import Loader from '../components/Loader';
import { FaStar,FaUserMd, FaCalendarAlt, FaFilePdf, FaArrowLeft, FaCheckCircle, FaTimesCircle, FaHourglassHalf,
  FaFilter, FaSearch, FaClipboardCheck, FaChartBar } from 'react-icons/fa';

const Reviews = () => {
  const navigate = useNavigate();
  const { user, token } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pendingReviews, setPendingReviews] = useState([]);
  const [doneReviews, setDoneReviews] = useState([]);
  const [selectedReview, setSelectedReview] = useState(null);
  const [dayFilter, setDayFilter] = useState('all');
  const [hourFilter, setHourFilter] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('pending');
  const [procedureTypeAnalytics, setProcedureTypeAnalytics] = useState({});
  const [procedureFilter, setProcedureFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showReviewModal, setShowReviewModal] = useState(false);



  useEffect(() => {
    const fetchReviews = async () => {
      if (!user || !token) {
        setError('Please log in to view your reviews.');
        navigate('/login');
        return;
      }
      if (user.role !== 'student' || !user.studentId) {
        setError('Access restricted to students.');
        navigate('/login');
        return;
      }

      try {
        setLoading(true);
        console.log('Fetching reviews for student:', user.studentId);

        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get(`http://localhost:5000/api/reviews/student?studentId=${user.studentId}`, config);
        const reviews = response.data;

        console.log('Response received:', response);

        if (!Array.isArray(reviews)) {
          console.error('Invalid response format:', reviews);
          throw new Error('Invalid response format: Reviews must be an array.');
        }

        console.log('Fetched reviews:', reviews);

        // Filter out signature storage reviews and group by status
        const filteredReviews = reviews.filter(r =>
          r.procedureType !== 'Signature Storage' &&
          r.patientId?.nationalId !== 'signature-storage'
        );

        setPendingReviews(filteredReviews.filter(r => r.status === 'pending'));
        setDoneReviews(filteredReviews.filter(r => r.status !== 'pending'));

        // Group reviews by procedure type for analytics
        const procedureTypes = {};
        filteredReviews.forEach(review => {
          const type = review.procedureType || 'Unknown';
          if (!procedureTypes[type]) {
            procedureTypes[type] = {
              total: 0,
              accepted: 0,
              denied: 0,
              pending: 0
            };
          }
          procedureTypes[type].total++;
          if (review.status === 'accepted') procedureTypes[type].accepted++;
          else if (review.status === 'denied') procedureTypes[type].denied++;
          else procedureTypes[type].pending++;
        });

        // Store procedure type analytics
        setProcedureTypeAnalytics(procedureTypes);

        setLoading(false);
      } catch (err) {
        console.error('Fetch reviews error:', err);
        console.error('Response data:', err.response?.data);
        console.error('Response status:', err.response?.status);

        if (err.response?.status === 401) {
          setError('Session expired. Please log in again.');
          navigate('/login');
        } else if (err.response?.status === 403) {
          setError('You do not have permission to view reviews.');
        } else if (err.response?.status === 404) {
          // If student not found, show empty state but don't show error
          console.log('Student not found, showing empty state');
          setPendingReviews([]);
          setDoneReviews([]);
          setProcedureTypeAnalytics({});
          setLoading(false);
        } else {
          setError(err.response?.data?.message || 'Failed to load reviews. Please try again later.');
        }
        setLoading(false);
      }
    };
    fetchReviews();
  }, [user, token, navigate]);

  const filterReviews = (reviews) => {
    if (!Array.isArray(reviews)) return [];

    let filtered = reviews;

    // Day filter
    const today = new Date();
    if (dayFilter === 'today') {
      filtered = filtered.filter(r => {
        const reviewDate = new Date(r.submittedDate);
        return reviewDate.toDateString() === today.toDateString();
      });
    } else if (dayFilter === 'tomorrow') {
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      filtered = filtered.filter(r => {
        const reviewDate = new Date(r.submittedDate);
        return reviewDate.toDateString() === tomorrow.toDateString();
      });
    } else if (dayFilter === 'week') {
      const weekEnd = new Date(today);
      weekEnd.setDate(weekEnd.getDate() + 7);
      filtered = filtered.filter(r => {
        const reviewDate = new Date(r.submittedDate);
        return reviewDate >= today && reviewDate <= weekEnd;
      });
    } else if (dayFilter === 'month') {
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
      filtered = filtered.filter(r => {
        const reviewDate = new Date(r.submittedDate);
        return reviewDate >= monthStart && reviewDate < nextMonth;
      });
    }

    // Hour filter
    if (hourFilter) {
      filtered = filtered.filter(r => {
        const reviewDate = new Date(r.submittedDate);
        const hours = reviewDate.getHours().toString().padStart(2, '0');
        return `${hours}:00` === hourFilter;
      });
    }

    // Procedure type filter
    if (procedureFilter && procedureFilter !== 'all') {
      filtered = filtered.filter(r => r.procedureType === procedureFilter);
    }

    // Status filter (only applies to the "done" tab)
    if (activeTab === 'done' && statusFilter && statusFilter !== 'all') {
      filtered = filtered.filter(r => r.status === statusFilter);
    }

    // Search by patient name or procedure type
    if (searchQuery) {
      filtered = filtered.filter(r =>
        (r.patientId?.fullName?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (r.procedureType?.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Sort by submission date (newest first)
    return filtered.sort((a, b) => new Date(b.submittedDate) - new Date(a.submittedDate));
  };

  // Calculate basic review counts for display
  const totalReviews = pendingReviews.length + doneReviews.length;
  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;
  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;
  const pendingReviewsCount = pendingReviews.length;

  // Calculate step-by-step feedback statistics
  const stepFeedbackStats = doneReviews.reduce((stats, review) => {
    if (review.reviewSteps && review.reviewSteps.length > 0) {
      const completedSteps = review.reviewSteps.filter(step => step.completed);
      const acceptedSteps = completedSteps.filter((step, index) => 
        review.stepStatuses?.[index] === 'accepted' || 
        step.supervisorStatus === 'accepted'
      ).length;
      const declinedSteps = completedSteps.filter((step, index) => 
        review.stepStatuses?.[index] === 'declined' || 
        step.supervisorStatus === 'declined'
      ).length;
      
      stats.totalSteps += completedSteps.length;
      stats.acceptedSteps += acceptedSteps;
      stats.declinedSteps += declinedSteps;
    }
    return stats;
  }, { totalSteps: 0, acceptedSteps: 0, declinedSteps: 0 });

  const stepAcceptanceRate = stepFeedbackStats.totalSteps > 0 
    ? Math.round((stepFeedbackStats.acceptedSteps / stepFeedbackStats.totalSteps) * 100) 
    : 0;

  const renderStars = (rating) => (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <FaStar
          key={i}
          className={`h-5 w-5 ${i < (rating || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
  );

  const handleReviewClick = (review) => {
    setSelectedReview(review);
    setShowReviewModal(true);
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: { staggerChildren: 0.1 },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm transition-all duration-300"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">My Reviews</h1>
                  <p className="text-[#333333]">Track your performance feedback</p>
                </div>
                <motion.button whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Link
                    to="/dashboard"
                    className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                  >
                    <FaArrowLeft className="h-5 w-5 mr-2" />
                    Back to Dashboard
                  </Link>
                </motion.button>
              </div>

              {/* Summary Stats */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="mb-8"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                      <FaClipboardCheck className="text-[#0077B6] h-5 w-5" />
                    </div>
                    <h3 className="text-xl font-bold text-[#0077B6]">Review Summary</h3>
                  </div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Link
                      to="/analytics"
                      className="px-6 py-3 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-lg hover:shadow-lg transition-all duration-300 text-sm flex items-center font-medium"
                    >
                      <FaChartBar className="mr-2 h-4 w-4" />
                      View Full Analytics
                    </Link>
                  </motion.div>
                </div>

                {/* Enhanced Stats Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#0077B6] group"
                  >
                    <div className="flex items-center">
                      <div className="bg-[rgba(0,119,182,0.1)] w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-[rgba(0,119,182,0.2)] transition-colors duration-300">
                        <FaClipboardCheck className="h-6 w-6 text-[#0077B6]" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Total Reviews</p>
                        <p className="text-2xl font-bold text-[#0077B6]">{totalReviews}</p>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group"
                  >
                    <div className="flex items-center">
                      <div className="bg-green-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300">
                        <FaCheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Accepted</p>
                        <p className="text-2xl font-bold text-green-600">{acceptedReviews}</p>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#F59E0B] group"
                  >
                    <div className="flex items-center">
                      <div className="bg-yellow-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-yellow-100 transition-colors duration-300">
                        <FaHourglassHalf className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Pending</p>
                        <p className="text-2xl font-bold text-yellow-600">{pendingReviewsCount}</p>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#EF4444] group"
                  >
                    <div className="flex items-center">
                      <div className="bg-red-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-100 transition-colors duration-300">
                        <FaTimesCircle className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Denied</p>
                        <p className="text-2xl font-bold text-red-600">{deniedReviews}</p>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Step-by-Step Feedback Stats */}
                {stepFeedbackStats.totalSteps > 0 && (
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-6">
                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-green-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-green-100 transition-colors duration-300">
                          <FaCheckCircle className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Step Acceptance Rate</p>
                          <p className="text-2xl font-bold text-green-600">{stepAcceptanceRate}%</p>
                          <p className="text-xs text-gray-500">{stepFeedbackStats.acceptedSteps}/{stepFeedbackStats.totalSteps} steps</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#28A745] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-blue-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-blue-100 transition-colors duration-300">
                          <FaCheckCircle className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Accepted Steps</p>
                          <p className="text-2xl font-bold text-blue-600">{stepFeedbackStats.acceptedSteps}</p>
                          <p className="text-xs text-gray-500">Individual steps</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      variants={item}
                      className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] hover:border-[#EF4444] group"
                    >
                      <div className="flex items-center">
                        <div className="bg-red-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 group-hover:bg-red-100 transition-colors duration-300">
                          <FaTimesCircle className="h-6 w-6 text-red-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Declined Steps</p>
                          <p className="text-2xl font-bold text-red-600">{stepFeedbackStats.declinedSteps}</p>
                          <p className="text-xs text-gray-500">Individual steps</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                )}
              </motion.div>

              {/* Enhanced Tabs */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl shadow-sm overflow-hidden mb-8 border border-[rgba(0,119,182,0.1)]"
              >
                <div className="flex">
                  <button
                    onClick={() => setActiveTab('pending')}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-all duration-300 ${
                      activeTab === 'pending'
                        ? 'border-[#20B2AA] text-[#0077B6] bg-[rgba(0,119,182,0.05)]'
                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)] hover:bg-gray-50'
                    }`}
                  >
                    <FaHourglassHalf className="mr-2 h-4 w-4" />
                    Pending Reviews
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                      activeTab === 'pending'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {pendingReviewsCount}
                    </span>
                  </button>
                  <button
                    onClick={() => setActiveTab('done')}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-all duration-300 ${
                      activeTab === 'done'
                        ? 'border-[#20B2AA] text-[#0077B6] bg-[rgba(0,119,182,0.05)]'
                        : 'border-transparent text-gray-500 hover:text-[#333333] hover:border-[rgba(32,178,170,0.3)] hover:bg-gray-50'
                    }`}
                  >
                    <FaCheckCircle className="mr-2 h-4 w-4" />
                    Done Reviews
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                      activeTab === 'done'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {doneReviews.length}
                    </span>
                  </button>
                </div>
              </motion.div>

              {/* Compact Filters and Search */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <FaFilter className="text-[#0077B6] h-4 w-4 mr-2" />
                    <h3 className="text-sm font-medium text-gray-700">Filters</h3>
                  </div>
                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setDayFilter('all');
                      setHourFilter('');
                      setProcedureFilter('all');
                      setStatusFilter('all');
                    }}
                    className="text-xs text-[#0077B6] hover:text-[#20B2AA] transition-colors duration-200"
                  >
                    Clear all
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-3">
                  {/* Compact Search */}
                  <div className="relative col-span-1 md:col-span-2">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        placeholder="Search patients or procedures..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors"
                      />
                    </div>
                  </div>

                  {/* Compact Date Filter */}
                  <div>
                    <select
                      value={dayFilter}
                      onChange={(e) => setDayFilter(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors"
                    >
                      <option value="all">All Dates</option>
                      <option value="today">Today</option>
                      <option value="tomorrow">Tomorrow</option>
                      <option value="week">This Week</option>
                      <option value="month">This Month</option>
                    </select>
                  </div>

                  {/* Compact Hour Filter */}
                  <div>
                    <select
                      value={hourFilter}
                      onChange={(e) => setHourFilter(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors"
                    >
                      <option value="">All Hours</option>
                      <option value="09:00">09:00 AM</option>
                      <option value="10:00">10:00 AM</option>
                      <option value="11:00">11:00 AM</option>
                      <option value="12:00">12:00 PM</option>
                      <option value="13:00">01:00 PM</option>
                      <option value="14:00">02:00 PM</option>
                      <option value="15:00">03:00 PM</option>
                    </select>
                  </div>

                  {/* Compact Procedure Type Filter */}
                  <div>
                    <select
                      value={procedureFilter}
                      onChange={(e) => setProcedureFilter(e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors"
                    >
                      <option value="all">All Procedures</option>
                      <option value="Operative">Operative</option>
                      <option value="Fixed Prosthodontics">Fixed Prosthodontics</option>
                      <option value="Removable Prosthodontics">Removable Prosthodontics</option>
                      <option value="Endodontics">Endodontics</option>
                      <option value="Periodontics">Periodontics</option>
                    </select>
                  </div>

                  {/* Compact Status Filter (only for done tab) */}
                  {activeTab === 'done' && (
                    <div>
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-[#20B2AA] focus:border-[#20B2AA] transition-colors"
                      >
                        <option value="all">All Statuses</option>
                        <option value="accepted">Accepted</option>
                        <option value="denied">Denied</option>
                      </select>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Enhanced Reviews Table */}
              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-center mb-6">
                    <div className="bg-[rgba(0,119,182,0.1)] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                      {activeTab === 'pending' ? (
                        <FaHourglassHalf className="text-[#0077B6] h-5 w-5" />
                      ) : (
                        <FaCheckCircle className="text-[#0077B6] h-5 w-5" />
                      )}
                    </div>
                    <h2 className="text-xl font-bold text-[#0077B6]">
                      {activeTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'}
                    </h2>
                    <span className="ml-3 px-3 py-1 rounded-full text-sm font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      {filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).length} reviews
                    </span>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {activeTab === 'pending' ? 'Submitted Date' : 'Reviewed Date'}
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Procedure</th>
                          {activeTab === 'done' && (
                            <>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supervisor</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Step Feedback</th>
                            </>
                          )}
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? (
                          <tr>
                            <td colSpan={activeTab === 'pending' ? 4 : 7} className="px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <svg className="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                <h3 className="text-lg font-medium text-gray-900">
                                  No {activeTab === 'pending' ? 'pending' : 'completed'} reviews
                                </h3>
                                <p className="mt-1 text-gray-500">No reviews match the selected filters.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          filterReviews(activeTab === 'pending' ? pendingReviews : doneReviews).map((review) => (
                            <motion.tr
                              key={review._id}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              className="hover:bg-gray-50 cursor-pointer"
                              onClick={() => setSelectedReview(review)}
                            >
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {activeTab === 'pending'
                                  ? new Date(review.submittedDate).toLocaleDateString()
                                  : new Date(review.reviewedDate || review.submittedDate).toLocaleDateString()
                                }
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {review.patientId?.fullName || 'N/A'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {review.procedureType || 'N/A'}
                              </td>
                              {activeTab === 'done' && (
                                <>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span
                                      className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        review.status === 'accepted' ? 'bg-[rgba(40,167,69,0.1)] text-[#28A745]' : 'bg-red-100 text-red-800'
                                      }`}
                                    >
                                      {review.status === 'accepted' ? 'Accepted' : review.status === 'denied' ? 'Declined' : review.status}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {review.supervisorName || 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {(() => {
                                      if (!review.reviewSteps || review.reviewSteps.length === 0) {
                                        return 'No steps';
                                      }
                                      
                                      const completedSteps = review.reviewSteps.filter(step => step.completed);
                                      if (completedSteps.length === 0) {
                                        return 'No completed steps';
                                      }
                                      
                                      const acceptedSteps = completedSteps.filter((step, index) => 
                                        review.stepStatuses?.[index] === 'accepted' || 
                                        step.supervisorStatus === 'accepted'
                                      ).length;
                                      const declinedSteps = completedSteps.filter((step, index) => 
                                        review.stepStatuses?.[index] === 'declined' || 
                                        step.supervisorStatus === 'declined'
                                      ).length;
                                      
                                      if (acceptedSteps === completedSteps.length) {
                                        return (
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600">
                                            <FaCheckCircle className="h-3 w-3 mr-1" />
                                            All Accepted
                                          </span>
                                        );
                                      } else if (declinedSteps === completedSteps.length) {
                                        return (
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600">
                                            <FaTimesCircle className="h-3 w-3 mr-1" />
                                            All Declined
                                          </span>
                                        );
                                      } else if (acceptedSteps > 0 && declinedSteps > 0) {
                                        return (
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-600">
                                            <FaHourglassHalf className="h-3 w-3 mr-1" />
                                            Mixed ({acceptedSteps}/{completedSteps.length})
                                          </span>
                                        );
                                      } else if (acceptedSteps > 0) {
                                        return (
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600">
                                            <FaCheckCircle className="h-3 w-3 mr-1" />
                                            {acceptedSteps}/{completedSteps.length} Accepted
                                          </span>
                                        );
                                      } else if (declinedSteps > 0) {
                                        return (
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600">
                                            <FaTimesCircle className="h-3 w-3 mr-1" />
                                            {declinedSteps}/{completedSteps.length} Declined
                                          </span>
                                        );
                                      } else {
                                        return (
                                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                            <FaHourglassHalf className="h-3 w-3 mr-1" />
                                            Pending Review
                                          </span>
                                        );
                                      }
                                    })()}
                                  </td>
                                </>
                              )}
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-[#0077B6]">
                                <button onClick={() => setSelectedReview(review)} className="hover:text-[#20B2AA] transition-all duration-300">View</button>
                              </td>
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {selectedReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-[#0077B6]">Review Details</h2>
                <button onClick={() => setSelectedReview(null)} className="text-gray-400 hover:text-[#0077B6] transition-all duration-300">
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {!selectedReview ? (
                <p className="text-sm text-gray-700">Loading review details...</p>
              ) : (
                <div className="space-y-6">
                  {/* Patient Information */}
                  <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
                    <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                      <FaUserMd className="h-5 w-5 mr-2 text-[#0077B6]" />
                      Patient Information
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Name</h4>
                        <p className="text-sm text-gray-900 mt-1">
                          {selectedReview.patientId?.fullName || 'N/A'}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                        <p className="text-sm text-gray-900 mt-1">
                          {selectedReview.patientId?.nationalId || 'N/A'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Review Details */}
                  <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
                    <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                      <FaCalendarAlt className="h-5 w-5 mr-2 text-[#0077B6]" />
                      Review Details
                    </h3>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Student</h4>
                          <p className="text-sm text-gray-900 mt-1">
                            {selectedReview.studentName || 'N/A'}
                          </p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Procedure Type</h4>
                          <p className="text-sm text-gray-900 mt-1">
                            {selectedReview.procedureType || 'N/A'}
                          </p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Submission Date</h4>
                          <p className="text-sm text-gray-900 mt-1">
                            {new Date(selectedReview.submittedDate).toLocaleString()}
                          </p>
                        </div>
                        {selectedReview.status !== 'pending' && selectedReview.reviewedDate && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-500">Reviewed Date</h4>
                            <p className="text-sm text-gray-900 mt-1">
                              {new Date(selectedReview.reviewedDate).toLocaleString()}
                            </p>
                          </div>
                        )}
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Status</h4>
                          <span
                            className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full mt-1 ${
                              selectedReview.status === 'accepted'
                                ? 'bg-[rgba(40,167,69,0.1)] text-[#28A745]'
                                : selectedReview.status === 'denied'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-[rgba(0,119,182,0.1)] text-[#0077B6]'
                            }`}
                          >
                            {selectedReview.status}
                          </span>
                        </div>
                      </div>

                      {/* Review Steps */}
                      <div className="mt-6">
                        <h4 className="text-sm font-medium text-gray-500 mb-2">Review Steps</h4>
                        
                        {/* Step-by-step feedback summary */}
                        {selectedReview.status !== 'pending' && selectedReview.reviewSteps && selectedReview.reviewSteps.length > 0 && (
                          <div className="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
                            <h5 className="text-sm font-medium text-gray-700 mb-2">Step-by-Step Feedback Summary</h5>
                            <div className="flex flex-wrap gap-3 text-xs">
                              {(() => {
                                const completedSteps = selectedReview.reviewSteps.filter(step => step.completed);
                                const acceptedSteps = completedSteps.filter((step, index) => 
                                  selectedReview.stepStatuses?.[index] === 'accepted' || 
                                  step.supervisorStatus === 'accepted'
                                ).length;
                                const declinedSteps = completedSteps.filter((step, index) => 
                                  selectedReview.stepStatuses?.[index] === 'declined' || 
                                  step.supervisorStatus === 'declined'
                                ).length;
                                const pendingSteps = completedSteps.length - acceptedSteps - declinedSteps;
                                
                                return (
                                  <>
                                    <span className="inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-600">
                                      <FaCheckCircle className="h-3 w-3 mr-1" />
                                      {acceptedSteps} Accepted
                                    </span>
                                    <span className="inline-flex items-center px-2 py-1 rounded-full bg-red-100 text-red-600">
                                      <FaTimesCircle className="h-3 w-3 mr-1" />
                                      {declinedSteps} Declined
                                    </span>
                                    {pendingSteps > 0 && (
                                      <span className="inline-flex items-center px-2 py-1 rounded-full bg-yellow-100 text-yellow-600">
                                        <FaHourglassHalf className="h-3 w-3 mr-1" />
                                        {pendingSteps} Pending
                                      </span>
                                    )}
                                    <span className="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-600">
                                      <FaClipboardCheck className="h-3 w-3 mr-1" />
                                      {completedSteps.length} Total Completed
                                    </span>
                                  </>
                                );
                              })()}
                            </div>
                          </div>
                        )}
                        
                        {selectedReview.reviewSteps && selectedReview.reviewSteps.length > 0 ? (
                          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                            <div className="divide-y divide-gray-200">
                              {selectedReview.reviewSteps.map((step, index) => {
                                // Get supervisor status for this step
                                const supervisorStatus = selectedReview.stepStatuses?.[index] || 
                                                       step.supervisorStatus || 
                                                       (step.completed ? 'pending' : 'not-done');
                                
                                return (
                                  <div
                                    key={index}
                                    className={`p-3 flex items-center justify-between ${
                                      step.completed 
                                        ? supervisorStatus === 'accepted'
                                          ? 'bg-green-50'
                                          : supervisorStatus === 'declined'
                                          ? 'bg-red-50'
                                          : 'bg-orange-50'
                                        : 'bg-gray-50'
                                    }`}
                                  >
                                    <div className="flex items-center">
                                      <span className="inline-block w-6 text-center mr-2 text-blue-600 font-bold">{index + 1}.</span>
                                      <span className="text-sm font-medium text-gray-900">{step.description}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      {/* Student completion status */}
                                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                        step.completed 
                                          ? 'bg-blue-100 text-blue-600' 
                                          : 'bg-gray-100 text-gray-600'
                                      }`}>
                                        {step.completed ? (
                                          <>
                                            <FaCheckCircle className="h-3 w-3 mr-1" />
                                            Completed
                                          </>
                                        ) : (
                                          <>
                                            <FaTimesCircle className="h-3 w-3 mr-1" />
                                            Not Done
                                          </>
                                        )}
                                      </span>
                                      
                                      {/* Supervisor decision (only show for completed steps) */}
                                      {step.completed && selectedReview.status !== 'pending' && (
                                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                          supervisorStatus === 'accepted'
                                            ? 'bg-green-100 text-green-600'
                                            : supervisorStatus === 'declined'
                                            ? 'bg-red-100 text-red-600'
                                            : 'bg-yellow-100 text-yellow-600'
                                        }`}>
                                          {supervisorStatus === 'accepted' ? (
                                            <>
                                              <FaCheckCircle className="h-3 w-3 mr-1" />
                                              Accepted
                                            </>
                                          ) : supervisorStatus === 'declined' ? (
                                            <>
                                              <FaTimesCircle className="h-3 w-3 mr-1" />
                                              Declined
                                            </>
                                          ) : (
                                            <>
                                              <FaHourglassHalf className="h-3 w-3 mr-1" />
                                              Pending
                                            </>
                                          )}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No review steps available</p>
                        )}
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Note</h4>
                        <p className="text-sm text-gray-900 mt-1 p-3 bg-white rounded border border-gray-200">
                          {selectedReview.note || 'No notes provided'}
                        </p>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Comment</h4>
                        <p className="text-sm text-gray-900 mt-1">
                          {selectedReview.comment || 'N/A'}
                        </p>
                      </div>

                      {selectedReview.chartId && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Dental Chart</h4>
                          <a
                            href={`/api/dental/chart/${selectedReview.chartId}/pdf`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 flex items-center mt-1"
                          >
                            <FaFilePdf className="h-5 w-5 mr-2 text-red-500" />
                            View Chart PDF
                          </a>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Supervisor Feedback */}
                  <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
                    <h3 className="text-lg font-semibold text-[#0077B6] mb-4">Supervisor Feedback</h3>
                    {selectedReview.status === 'pending' ? (
                      <p className="text-sm text-gray-700">Awaiting supervisor review...</p>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Supervisor</h4>
                          <p className="text-sm text-gray-900 mt-1">
                            {selectedReview.supervisorName || 'N/A'} (ID: {selectedReview.supervisorId?._id || 'N/A'})
                          </p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Status</h4>
                          <p className="text-sm text-gray-900 mt-1 capitalize">
                            {selectedReview.status === 'accepted' ? 'Accepted' :
                             selectedReview.status === 'denied' ? 'Declined' :
                             selectedReview.status || 'N/A'}
                          </p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Procedure Quality</h4>
                          {renderStars(selectedReview.procedureQuality)}
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Patient Interaction</h4>
                          {renderStars(selectedReview.patientInteraction)}
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Supervisor Note</h4>
                          <p className="text-sm text-gray-900 mt-1">
                            {selectedReview.note || 'No notes provided'}
                          </p>
                        </div>
                        {/* Supervisor Signature */}
                        {selectedReview.supervisorSignature && (
                          <div className="mt-4 pt-4 border-t border-gray-200">
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Supervisor Signature</h4>
                            <div className="border border-gray-300 rounded-lg bg-white p-4 flex justify-center">
                              {selectedReview.supervisorSignature.startsWith('data:image') ? (
                                <img src={selectedReview.supervisorSignature} alt="Signature" className="max-h-20" />
                              ) : (
                                <p className="font-signature text-lg">{selectedReview.supervisorSignature}</p>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Reviews;