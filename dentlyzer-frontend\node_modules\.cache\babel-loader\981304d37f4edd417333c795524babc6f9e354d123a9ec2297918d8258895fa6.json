{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\supervisor\\\\Dashboard.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaUserMd, FaCalendarAlt, FaStar, FaCheck, FaTimes, FaChartPie, FaPercentage, FaStarHalfAlt, FaFilter, FaSearch, FaChartBar, FaClipboardCheck, FaUserGraduate, FaSignature, FaCalendarCheck, FaListAlt, FaCheckCircle } from 'react-icons/fa';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';\nimport { Pie, Bar } from 'react-chartjs-2';\n\n// Import custom components\nimport SignatureManager from './SignatureManager';\nimport ReviewStepsDisplay from './ReviewStepsDisplay';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\n// Register ChartJS components\nChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);\n\n// Chart.js default configuration\nChartJS.defaults.font.family = 'Inter, system-ui, sans-serif';\nChartJS.defaults.font.size = 12;\nChartJS.defaults.color = '#6b7280';\nChartJS.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';\nChartJS.defaults.plugins.tooltip.titleColor = '#ffffff';\nChartJS.defaults.plugins.tooltip.bodyColor = '#ffffff';\nChartJS.defaults.plugins.legend.labels.usePointStyle = true;\nChartJS.defaults.plugins.legend.labels.padding = 20;\nconst SupervisorDashboard = () => {\n  _s();\n  var _selectedReview$patie, _selectedReview$patie2;\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [pendingReviews, setPendingReviews] = useState([]);\n  const [doneReviews, setDoneReviews] = useState([]);\n  const [allReviews, setAllReviews] = useState([]);\n  const [selectedReview, setSelectedReview] = useState(null);\n  const [dayFilter, setDayFilter] = useState('all');\n  const [hourFilter, setHourFilter] = useState('');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [procedureFilter, setProcedureFilter] = useState('all');\n  const [studentFilter, setStudentFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [mainTab, setMainTab] = useState('reviews');\n  const [reviewsTab, setReviewsTab] = useState('pending');\n  const [savedSignature, setSavedSignature] = useState(null);\n  const [showSignatureModal, setShowSignatureModal] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [analyticsTimeRange, setAnalyticsTimeRange] = useState('month');\n  const [analyticsData, setAnalyticsData] = useState({\n    statusDistribution: {\n      accepted: 0,\n      pending: 0,\n      denied: 0\n    },\n    procedureTypeDistribution: {},\n    studentPerformance: {},\n    reviewTrends: [],\n    qualityMetrics: {\n      avgProcedureQuality: 0,\n      avgPatientInteraction: 0\n    }\n  });\n  const [uniqueStudents, setUniqueStudents] = useState([]);\n  const [uniqueProcedures, setUniqueProcedures] = useState([]);\n  const [reviewForm, setReviewForm] = useState({\n    procedureQuality: 0,\n    patientInteraction: 0,\n    comment: '',\n    status: 'accepted',\n    supervisorSignature: null\n  });\n\n  // Fetch all reviews and signature data\n  useEffect(() => {\n    const fetchData = async () => {\n      console.log('Auth data:', {\n        user,\n        token\n      }); // Debug auth\n      if (!user || !token || user.role !== 'supervisor') {\n        setError('Please log in as a supervisor to view this dashboard.');\n        navigate('/login');\n        return;\n      }\n      try {\n        setLoading(true);\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n\n        // Fetch all reviews for the supervisor\n        console.log('Fetching all supervisor reviews');\n        const allReviewsRes = await axios.get('http://localhost:5000/api/reviews/supervisor', config);\n        console.log('All reviews response:', allReviewsRes.data);\n        const reviews = Array.isArray(allReviewsRes.data) ? allReviewsRes.data : [];\n        setAllReviews(reviews);\n\n        // Separate reviews by status\n        setPendingReviews(reviews.filter(r => r.status === 'pending'));\n        setDoneReviews(reviews.filter(r => r.status !== 'pending'));\n\n        // Extract unique students and procedure types for filtering\n        const students = [...new Set(reviews.map(r => {\n          var _r$studentId;\n          return ((_r$studentId = r.studentId) === null || _r$studentId === void 0 ? void 0 : _r$studentId.name) || r.studentName;\n        }))].filter(Boolean);\n        const procedures = [...new Set(reviews.map(r => r.procedureType))].filter(Boolean);\n        setUniqueStudents(students);\n        setUniqueProcedures(procedures);\n\n        // Fetch supervisor's saved signature if available\n        try {\n          const signatureRes = await axios.get('http://localhost:5000/api/supervisors/signature', config);\n          if (signatureRes.data && signatureRes.data.signature) {\n            setSavedSignature(signatureRes.data.signature);\n          }\n        } catch (signatureErr) {\n          console.error('Error fetching signature:', signatureErr);\n          // Non-critical error, continue without signature\n        }\n\n        // Fetch analytics data from the server\n        try {\n          console.log('Fetching analytics data for time range:', analyticsTimeRange);\n          const analyticsRes = await axios.get(`http://localhost:5000/api/reviews/analytics?timeRange=${analyticsTimeRange}`, config);\n          console.log('Analytics response:', analyticsRes.data);\n          if (analyticsRes.data) {\n            var _analyticsRes$data$st, _analyticsRes$data$st2, _analyticsRes$data$st3, _analyticsRes$data$qu, _analyticsRes$data$qu2;\n            // Ensure all required fields exist with proper defaults\n            const analyticsData = {\n              statusDistribution: {\n                accepted: ((_analyticsRes$data$st = analyticsRes.data.statusDistribution) === null || _analyticsRes$data$st === void 0 ? void 0 : _analyticsRes$data$st.accepted) || 0,\n                pending: ((_analyticsRes$data$st2 = analyticsRes.data.statusDistribution) === null || _analyticsRes$data$st2 === void 0 ? void 0 : _analyticsRes$data$st2.pending) || 0,\n                denied: ((_analyticsRes$data$st3 = analyticsRes.data.statusDistribution) === null || _analyticsRes$data$st3 === void 0 ? void 0 : _analyticsRes$data$st3.denied) || 0\n              },\n              procedureTypeDistribution: analyticsRes.data.procedureTypeDistribution || {},\n              studentPerformance: analyticsRes.data.studentPerformance || {},\n              reviewTrends: analyticsRes.data.reviewTrends || [],\n              qualityMetrics: {\n                avgProcedureQuality: ((_analyticsRes$data$qu = analyticsRes.data.qualityMetrics) === null || _analyticsRes$data$qu === void 0 ? void 0 : _analyticsRes$data$qu.avgProcedureQuality) || 0,\n                avgPatientInteraction: ((_analyticsRes$data$qu2 = analyticsRes.data.qualityMetrics) === null || _analyticsRes$data$qu2 === void 0 ? void 0 : _analyticsRes$data$qu2.avgPatientInteraction) || 0\n              }\n            };\n            setAnalyticsData(analyticsData);\n          }\n        } catch (analyticsErr) {\n          console.error('Error fetching analytics:', analyticsErr);\n          console.log('Falling back to client-side calculation');\n          // Fallback to client-side calculation if server analytics fails\n          if (reviews.length > 0) {\n            calculateAnalytics(reviews);\n          } else {\n            // If no reviews data, use sample data for testing\n            console.log('No reviews data available, using sample data');\n            setAnalyticsData(generateSampleData());\n          }\n        }\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response4$data, _err$response5, _err$response6;\n        console.error('Fetch error:', {\n          message: err.message,\n          status: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status,\n          data: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data,\n          headers: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.headers\n        });\n        const errorMessage = ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.message) || (((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : _err$response5.status) === 403 ? 'Access denied: Insufficient permissions' : 'Failed to load reviews. Please try again.');\n        setError(errorMessage);\n        if (((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : _err$response6.status) === 401) {\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          navigate('/login');\n        }\n        setPendingReviews([]);\n        setDoneReviews([]);\n        setAllReviews([]);\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate, analyticsTimeRange]);\n\n  // Fetch new analytics data when time range changes\n  const handleAnalyticsTimeRangeChange = newRange => {\n    setAnalyticsTimeRange(newRange);\n  };\n\n  // Generate sample data for testing charts\n  const generateSampleData = () => {\n    return {\n      statusDistribution: {\n        accepted: 15,\n        pending: 8,\n        denied: 3\n      },\n      procedureTypeDistribution: {\n        'Operative Dentistry': {\n          total: 10,\n          accepted: 7,\n          denied: 2,\n          pending: 1\n        },\n        'Endodontics': {\n          total: 8,\n          accepted: 5,\n          denied: 1,\n          pending: 2\n        },\n        'Periodontics': {\n          total: 6,\n          accepted: 3,\n          denied: 0,\n          pending: 3\n        },\n        'Fixed Prosthodontics': {\n          total: 2,\n          accepted: 0,\n          denied: 0,\n          pending: 2\n        }\n      },\n      studentPerformance: {\n        'John Doe': {\n          total: 8,\n          accepted: 6,\n          denied: 1,\n          pending: 1,\n          avgProcedureQuality: 4.2,\n          avgPatientInteraction: 4.5\n        },\n        'Jane Smith': {\n          total: 6,\n          accepted: 4,\n          denied: 1,\n          pending: 1,\n          avgProcedureQuality: 3.8,\n          avgPatientInteraction: 4.0\n        },\n        'Mike Johnson': {\n          total: 4,\n          accepted: 3,\n          denied: 0,\n          pending: 1,\n          avgProcedureQuality: 4.5,\n          avgPatientInteraction: 4.2\n        }\n      },\n      reviewTrends: [{\n        month: '1/2024',\n        total: 5,\n        accepted: 3,\n        denied: 1,\n        pending: 1\n      }, {\n        month: '2/2024',\n        total: 8,\n        accepted: 6,\n        denied: 1,\n        pending: 1\n      }, {\n        month: '3/2024',\n        total: 6,\n        accepted: 4,\n        denied: 1,\n        pending: 1\n      }, {\n        month: '4/2024',\n        total: 7,\n        accepted: 2,\n        denied: 0,\n        pending: 5\n      }],\n      qualityMetrics: {\n        avgProcedureQuality: 4.1,\n        avgPatientInteraction: 4.2\n      }\n    };\n  };\n\n  // Calculate analytics from reviews data\n  const calculateAnalytics = reviews => {\n    if (!Array.isArray(reviews) || reviews.length === 0) {\n      console.log('No reviews data available for analytics calculation');\n      return;\n    }\n    console.log('Calculating analytics for', reviews.length, 'reviews');\n\n    // Status distribution\n    const pending = reviews.filter(r => r.status === 'pending').length;\n    const accepted = reviews.filter(r => r.status === 'accepted').length;\n    const denied = reviews.filter(r => r.status === 'denied').length;\n    console.log('Status distribution:', {\n      pending,\n      accepted,\n      denied\n    });\n\n    // Procedure type distribution\n    const procedureTypes = {};\n    reviews.forEach(review => {\n      const type = review.procedureType || 'Unknown';\n      if (!procedureTypes[type]) {\n        procedureTypes[type] = {\n          total: 0,\n          accepted: 0,\n          denied: 0,\n          pending: 0\n        };\n      }\n      procedureTypes[type].total++;\n      if (review.status === 'accepted') procedureTypes[type].accepted++;else if (review.status === 'denied') procedureTypes[type].denied++;else procedureTypes[type].pending++;\n    });\n    console.log('Procedure types:', Object.keys(procedureTypes));\n\n    // Student performance\n    const studentPerformance = {};\n    reviews.forEach(review => {\n      var _review$studentId;\n      const studentName = review.studentName || ((_review$studentId = review.studentId) === null || _review$studentId === void 0 ? void 0 : _review$studentId.name) || 'Unknown';\n      if (!studentPerformance[studentName]) {\n        studentPerformance[studentName] = {\n          total: 0,\n          accepted: 0,\n          denied: 0,\n          pending: 0,\n          avgProcedureQuality: 0,\n          avgPatientInteraction: 0,\n          qualityRatings: [],\n          interactionRatings: []\n        };\n      }\n      studentPerformance[studentName].total++;\n      if (review.status === 'accepted') studentPerformance[studentName].accepted++;else if (review.status === 'denied') studentPerformance[studentName].denied++;else studentPerformance[studentName].pending++;\n      if (review.procedureQuality) {\n        studentPerformance[studentName].qualityRatings.push(review.procedureQuality);\n      }\n      if (review.patientInteraction) {\n        studentPerformance[studentName].interactionRatings.push(review.patientInteraction);\n      }\n    });\n\n    // Calculate averages for each student\n    Object.keys(studentPerformance).forEach(student => {\n      const {\n        qualityRatings,\n        interactionRatings\n      } = studentPerformance[student];\n      if (qualityRatings.length > 0) {\n        studentPerformance[student].avgProcedureQuality = (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);\n      }\n      if (interactionRatings.length > 0) {\n        studentPerformance[student].avgPatientInteraction = (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);\n      }\n\n      // Remove the arrays from the response\n      delete studentPerformance[student].qualityRatings;\n      delete studentPerformance[student].interactionRatings;\n    });\n    console.log('Student performance calculated for', Object.keys(studentPerformance).length, 'students');\n\n    // Review trends by month\n    const reviewsByMonth = {};\n    const now = new Date();\n    const sixMonthsAgo = new Date();\n    sixMonthsAgo.setMonth(now.getMonth() - 6);\n    reviews.forEach(review => {\n      const date = new Date(review.submittedDate);\n      if (date >= sixMonthsAgo) {\n        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;\n        if (!reviewsByMonth[monthYear]) {\n          reviewsByMonth[monthYear] = {\n            total: 0,\n            accepted: 0,\n            denied: 0,\n            pending: 0\n          };\n        }\n        reviewsByMonth[monthYear].total++;\n        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;else reviewsByMonth[monthYear].pending++;\n      }\n    });\n\n    // Convert to array and sort by date\n    const reviewTrends = Object.keys(reviewsByMonth).map(month => ({\n      month,\n      ...reviewsByMonth[month]\n    })).sort((a, b) => {\n      const [aMonth, aYear] = a.month.split('/').map(Number);\n      const [bMonth, bYear] = b.month.split('/').map(Number);\n      return aYear === bYear ? aMonth - bMonth : aYear - bYear;\n    });\n    console.log('Review trends calculated for', reviewTrends.length, 'months');\n\n    // Quality metrics\n    const doneReviewsArr = reviews.filter(r => r.status !== 'pending');\n    const avgProcedureQuality = doneReviewsArr.length > 0 ? (doneReviewsArr.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviewsArr.length).toFixed(1) : 0;\n    const avgPatientInteraction = doneReviewsArr.length > 0 ? (doneReviewsArr.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviewsArr.length).toFixed(1) : 0;\n    console.log('Quality metrics:', {\n      avgProcedureQuality,\n      avgPatientInteraction\n    });\n\n    // Update analytics state\n    const newAnalyticsData = {\n      statusDistribution: {\n        accepted,\n        pending,\n        denied\n      },\n      procedureTypeDistribution: procedureTypes,\n      studentPerformance,\n      reviewTrends,\n      qualityMetrics: {\n        avgProcedureQuality,\n        avgPatientInteraction\n      }\n    };\n    console.log('Setting analytics data:', newAnalyticsData);\n    setAnalyticsData(newAnalyticsData);\n  };\n  const handleReviewSubmit = async reviewId => {\n    if (reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0) {\n      setError('Please provide ratings for Procedure Quality and Patient Interaction');\n      return;\n    }\n    if (!reviewForm.supervisorSignature) {\n      setError('Please provide your signature to complete the review');\n      return;\n    }\n    try {\n      const config = {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      };\n      const updatedReview = {\n        status: reviewForm.status,\n        procedureQuality: reviewForm.procedureQuality,\n        patientInteraction: reviewForm.patientInteraction,\n        comment: reviewForm.comment || '',\n        supervisorSignature: reviewForm.supervisorSignature\n      };\n      console.log('Submitting review update:', {\n        reviewId,\n        ...updatedReview,\n        supervisorSignature: 'signature data present'\n      });\n      const response = await axios.put(`http://localhost:5000/api/reviews/${reviewId}`, updatedReview, config);\n      console.log('Review updated:', response.data);\n\n      // Save the signature for future use\n      setSavedSignature(reviewForm.supervisorSignature);\n\n      // Update local state\n      const reviewToMove = pendingReviews.find(r => r._id === reviewId);\n\n      // Make sure supervisor name is set in the local state\n      const updatedReviewWithSupervisor = {\n        ...updatedReview,\n        supervisorName: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown Supervisor',\n        reviewedDate: new Date()\n      };\n      setPendingReviews(prev => prev.filter(r => r._id !== reviewId));\n      setDoneReviews(prev => [...prev, {\n        ...reviewToMove,\n        ...updatedReviewWithSupervisor\n      }]);\n\n      // Recalculate analytics with the updated review\n      const allReviewsUpdated = [...doneReviews, {\n        ...reviewToMove,\n        ...updatedReviewWithSupervisor\n      }, ...pendingReviews.filter(r => r._id !== reviewId)];\n      calculateAnalytics(allReviewsUpdated);\n\n      // Reset form and close modal\n      setSelectedReview(null);\n      setReviewForm({\n        procedureQuality: 0,\n        patientInteraction: 0,\n        comment: '',\n        status: 'accepted',\n        supervisorSignature: null\n      });\n      setError('');\n\n      // Show success message in a styled popup\n      setSuccessMessage(`Review ${reviewForm.status === 'accepted' ? 'approved' : 'declined'} successfully!`);\n      setShowSuccessModal(true);\n\n      // Auto-hide the success message after 3 seconds\n      setTimeout(() => {\n        setShowSuccessModal(false);\n      }, 3000);\n    } catch (err) {\n      var _err$response7, _err$response8, _err$response9, _err$response9$data;\n      console.error('Review submit error:', {\n        message: err.message,\n        status: (_err$response7 = err.response) === null || _err$response7 === void 0 ? void 0 : _err$response7.status,\n        data: (_err$response8 = err.response) === null || _err$response8 === void 0 ? void 0 : _err$response8.data\n      });\n      setError(((_err$response9 = err.response) === null || _err$response9 === void 0 ? void 0 : (_err$response9$data = _err$response9.data) === null || _err$response9$data === void 0 ? void 0 : _err$response9$data.message) || 'Failed to submit review');\n    }\n  };\n  const filterReviews = reviews => {\n    let filtered = [...reviews];\n\n    // Day filter\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    if (dayFilter === 'today') {\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate.toDateString() === today.toDateString();\n      });\n    } else if (dayFilter === 'tomorrow') {\n      const tomorrow = new Date(today);\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate.toDateString() === tomorrow.toDateString();\n      });\n    } else if (dayFilter === 'week') {\n      const weekEnd = new Date(today);\n      weekEnd.setDate(weekEnd.getDate() + 7);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate >= today && reviewDate <= weekEnd;\n      });\n    } else if (dayFilter === 'month') {\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        return reviewDate >= monthStart && reviewDate < nextMonth;\n      });\n    }\n\n    // Hour filter\n    if (hourFilter) {\n      filtered = filtered.filter(r => {\n        const reviewDate = new Date(r.submittedDate);\n        const hours = reviewDate.getHours().toString().padStart(2, '0');\n        return `${hours}:00` === hourFilter;\n      });\n    }\n\n    // Search by patient name or student name\n    if (searchQuery) {\n      filtered = filtered.filter(r => {\n        var _r$patientId, _r$patientId$fullName, _r$studentName;\n        return ((_r$patientId = r.patientId) === null || _r$patientId === void 0 ? void 0 : (_r$patientId$fullName = _r$patientId.fullName) === null || _r$patientId$fullName === void 0 ? void 0 : _r$patientId$fullName.toLowerCase().includes(searchQuery.toLowerCase())) || ((_r$studentName = r.studentName) === null || _r$studentName === void 0 ? void 0 : _r$studentName.toLowerCase().includes(searchQuery.toLowerCase()));\n      });\n    }\n\n    // Procedure type filter\n    if (procedureFilter && procedureFilter !== 'all') {\n      filtered = filtered.filter(r => r.procedureType === procedureFilter);\n    }\n\n    // Student filter\n    if (studentFilter && studentFilter !== 'all') {\n      filtered = filtered.filter(r => r.studentName === studentFilter);\n    }\n\n    // Status filter (only applies to the \"done\" tab)\n    if (reviewsTab === 'done' && statusFilter && statusFilter !== 'all') {\n      filtered = filtered.filter(r => r.status === statusFilter);\n    }\n\n    // Sort by date (submittedDate for pending, reviewedDate for done)\n    return filtered.sort((a, b) => {\n      const dateA = new Date(reviewsTab === 'pending' ? a.submittedDate : a.reviewedDate || a.submittedDate);\n      const dateB = new Date(reviewsTab === 'pending' ? b.submittedDate : b.reviewedDate || b.submittedDate);\n      return dateB - dateA; // Newest first\n    });\n  };\n\n  // Analytics calculations\n  const totalReviews = pendingReviews.length + doneReviews.length;\n  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;\n  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;\n  const acceptanceRate = totalReviews > 0 ? (acceptedReviews / totalReviews * 100).toFixed(1) : 0;\n  const denialRate = totalReviews > 0 ? (deniedReviews / totalReviews * 100).toFixed(1) : 0;\n  const avgProcedureQuality = doneReviews.length > 0 ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1) : 0;\n  const avgPatientInteraction = doneReviews.length > 0 ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1) : 0;\n  const renderStars = (rating, onClick = null) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex\",\n    children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FaStar, {\n      className: `h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} ${onClick ? 'cursor-pointer' : ''}`,\n      onClick: onClick ? () => onClick(i + 1) : null\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 545,\n    columnNumber: 5\n  }, this);\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-6\",\n        style: {\n          background: `linear-gradient(to bottom right, ${colorPalette.primary}10, ${colorPalette.background})`\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [error && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-red-500 mr-3\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-700 font-medium\",\n                children: error\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold mb-1\",\n                  style: {\n                    color: colorPalette.primary\n                  },\n                  children: \"Supervisor Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: colorPalette.text\n                  },\n                  children: [\"Welcome back, \", (user === null || user === void 0 ? void 0 : user.name) || 'Supervisor']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex gap-3\",\n                children: /*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: () => setShowSignatureModal(true),\n                  className: \"px-4 py-2 text-white rounded-lg flex items-center\",\n                  style: {\n                    background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 21\n                  }, this), \" Signature\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex border-b border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setMainTab('reviews'),\n                  className: \"px-6 py-3 font-medium text-base\",\n                  style: {\n                    borderBottom: mainTab === 'reviews' ? `2px solid ${colorPalette.primary}` : 'none',\n                    color: mainTab === 'reviews' ? colorPalette.primary : '#6b7280',\n                    backgroundColor: mainTab === 'reviews' ? `${colorPalette.primary}10` : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaClipboardCheck, {\n                    className: \"h-5 w-5 inline mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 21\n                  }, this), \"Reviews\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setMainTab('analytics'),\n                  className: \"px-6 py-3 font-medium text-base\",\n                  style: {\n                    borderBottom: mainTab === 'analytics' ? `2px solid ${colorPalette.primary}` : 'none',\n                    color: mainTab === 'analytics' ? colorPalette.primary : '#6b7280',\n                    backgroundColor: mainTab === 'analytics' ? `${colorPalette.primary}10` : 'transparent'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(FaChartBar, {\n                    className: \"h-5 w-5 inline mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 21\n                  }, this), \"Analytics\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this), mainTab === 'reviews' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: container,\n                initial: \"hidden\",\n                whileInView: \"show\",\n                viewport: {\n                  once: true\n                },\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaChartPie, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Total Reviews\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: totalReviews\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 674,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 672,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaPercentage, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 688,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Acceptance Rate\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 691,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [acceptanceRate, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: [\"Decline: \", denialRate, \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 693,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 690,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaStarHalfAlt, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Avg. Procedure Quality\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [avgProcedureQuality, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 711,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: item,\n                  className: \"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\",\n                      style: {\n                        backgroundColor: `${colorPalette.primary}10`,\n                        color: colorPalette.primary\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FaStarHalfAlt, {\n                        className: \"h-6 w-6\",\n                        style: {\n                          color: colorPalette.primary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Avg. Patient Interaction\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-2xl font-bold\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [avgPatientInteraction, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 729,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex border-b border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setReviewsTab('pending'),\n                    className: \"px-4 py-2 font-medium text-sm\",\n                    style: {\n                      borderBottom: reviewsTab === 'pending' ? `2px solid ${colorPalette.primary}` : 'none',\n                      color: reviewsTab === 'pending' ? colorPalette.primary : '#6b7280'\n                    },\n                    children: [\"Pending Reviews (\", pendingReviews.length, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setReviewsTab('done'),\n                    className: \"px-4 py-2 font-medium text-sm\",\n                    style: {\n                      borderBottom: reviewsTab === 'done' ? `2px solid ${colorPalette.primary}` : 'none',\n                      color: reviewsTab === 'done' ? colorPalette.primary : '#6b7280'\n                    },\n                    children: [\"Done Reviews (\", doneReviews.length, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), mainTab === 'analytics' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-xl font-bold\",\n                  style: {\n                    color: colorPalette.primary\n                  },\n                  children: \"Analytics Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: analyticsTimeRange,\n                    onChange: e => handleAnalyticsTimeRangeChange(e.target.value),\n                    className: \"px-4 py-2 border border-gray-300 rounded-lg\",\n                    style: {\n                      outline: 'none',\n                      boxShadow: 'none',\n                      borderColor: '#e5e7eb',\n                      ':focus': {\n                        borderColor: colorPalette.primary\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"week\",\n                      children: \"Last Week\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 780,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"month\",\n                      children: \"Last Month\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 781,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"year\",\n                      children: \"Last Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 782,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"all\",\n                      children: \"All Time\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-semibold text-yellow-800 mb-2\",\n                  children: \"Debug Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-yellow-700 space-y-1 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Time Range: \", analyticsTimeRange]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Total Reviews: \", allReviews.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Status Distribution: \", JSON.stringify(analyticsData.statusDistribution)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Procedure Types: \", Object.keys(analyticsData.procedureTypeDistribution).length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Students: \", Object.keys(analyticsData.studentPerformance).length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Trends: \", analyticsData.reviewTrends.length, \" months\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setAnalyticsData(generateSampleData()),\n                    className: \"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600\",\n                    children: \"Load Sample Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => calculateAnalytics(allReviews),\n                    className: \"px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600\",\n                    children: \"Recalculate Analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.1\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Review Status Distribution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64 flex items-center justify-center\",\n                    children: analyticsData.statusDistribution && Object.values(analyticsData.statusDistribution).some(val => val > 0) ? /*#__PURE__*/_jsxDEV(Pie, {\n                      data: {\n                        labels: ['Accepted', 'Pending', 'Denied'],\n                        datasets: [{\n                          data: [analyticsData.statusDistribution.accepted || 0, analyticsData.statusDistribution.pending || 0, analyticsData.statusDistribution.denied || 0],\n                          backgroundColor: [`${colorPalette.accent}B3`,\n                          // accent with opacity\n                          `${colorPalette.primary}B3`,\n                          // primary with opacity\n                          'rgba(239, 68, 68, 0.7)' // red\n                          ],\n                          borderColor: [colorPalette.accent, colorPalette.primary, 'rgba(239, 68, 68, 1)'],\n                          borderWidth: 1\n                        }]\n                      },\n                      options: {\n                        responsive: true,\n                        maintainAspectRatio: false,\n                        plugins: {\n                          legend: {\n                            position: 'bottom',\n                            labels: {\n                              usePointStyle: true,\n                              padding: 20,\n                              font: {\n                                size: 12\n                              }\n                            }\n                          },\n                          tooltip: {\n                            callbacks: {\n                              label: function (context) {\n                                const label = context.label || '';\n                                const value = context.raw || 0;\n                                const total = context.dataset.data.reduce((a, b) => a + b, 0);\n                                const percentage = total > 0 ? Math.round(value / total * 100) : 0;\n                                return `${label}: ${value} (${percentage}%)`;\n                              }\n                            }\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 833,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 1,\n                          d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 888,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No review data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 890,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 886,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 824,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.2\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Procedure Type Distribution\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64 flex items-center justify-center\",\n                    children: analyticsData.procedureTypeDistribution && Object.keys(analyticsData.procedureTypeDistribution).length > 0 ? /*#__PURE__*/_jsxDEV(Bar, {\n                      data: {\n                        labels: Object.keys(analyticsData.procedureTypeDistribution),\n                        datasets: [{\n                          label: 'Accepted',\n                          data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.accepted || 0),\n                          backgroundColor: `${colorPalette.accent}B3`,\n                          borderColor: colorPalette.accent,\n                          borderWidth: 1\n                        }, {\n                          label: 'Pending',\n                          data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.pending || 0),\n                          backgroundColor: `${colorPalette.primary}B3`,\n                          borderColor: colorPalette.primary,\n                          borderWidth: 1\n                        }, {\n                          label: 'Denied',\n                          data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.denied || 0),\n                          backgroundColor: 'rgba(239, 68, 68, 0.7)',\n                          borderColor: 'rgba(239, 68, 68, 1)',\n                          borderWidth: 1\n                        }]\n                      },\n                      options: {\n                        responsive: true,\n                        maintainAspectRatio: false,\n                        scales: {\n                          x: {\n                            stacked: true,\n                            ticks: {\n                              maxRotation: 45,\n                              minRotation: 0\n                            }\n                          },\n                          y: {\n                            stacked: true,\n                            beginAtZero: true,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        },\n                        plugins: {\n                          legend: {\n                            position: 'bottom',\n                            labels: {\n                              usePointStyle: true,\n                              padding: 20,\n                              font: {\n                                size: 12\n                              }\n                            }\n                          },\n                          tooltip: {\n                            mode: 'index',\n                            intersect: false\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 906,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 1,\n                          d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 973,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 972,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No procedure data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 975,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 971,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 897,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.3\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Student Performance\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/_jsxDEV(\"table\", {\n                      className: \"min-w-full divide-y divide-gray-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        className: \"bg-gray-50\",\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Student\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 993,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Reviews\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 996,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Acceptance Rate\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 999,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Avg. Quality\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1002,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                            children: \"Avg. Interaction\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1005,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 992,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        className: \"bg-white divide-y divide-gray-200\",\n                        children: analyticsData.studentPerformance && Object.keys(analyticsData.studentPerformance).length > 0 ? Object.entries(analyticsData.studentPerformance).map(([student, data], index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: student\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1014,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.total || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1017,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.total > 0 ? `${Math.round(data.accepted / data.total * 100)}%` : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1020,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.avgProcedureQuality > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"mr-2\",\n                                children: data.avgProcedureQuality\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1026,\n                                columnNumber: 41\n                              }, this), renderStars(parseFloat(data.avgProcedureQuality))]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1025,\n                              columnNumber: 39\n                            }, this) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1023,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: data.avgPatientInteraction > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"mr-2\",\n                                children: data.avgPatientInteraction\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1034,\n                                columnNumber: 41\n                              }, this), renderStars(parseFloat(data.avgPatientInteraction))]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1033,\n                              columnNumber: 39\n                            }, this) : 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1031,\n                            columnNumber: 35\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1013,\n                          columnNumber: 33\n                        }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: /*#__PURE__*/_jsxDEV(\"td\", {\n                            colSpan: \"5\",\n                            className: \"px-6 py-4 text-center text-sm text-gray-500\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-col items-center\",\n                              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                className: \"h-12 w-12 text-gray-300 mb-2\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  strokeWidth: 1,\n                                  d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1046,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1045,\n                                columnNumber: 37\n                              }, this), \"No student performance data available\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1044,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1043,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1042,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1010,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 990,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 989,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 20\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: 0.4\n                  },\n                  className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold mb-4\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: \"Review Trends\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-64 flex items-center justify-center\",\n                    children: analyticsData.reviewTrends && analyticsData.reviewTrends.length > 0 ? /*#__PURE__*/_jsxDEV(Bar, {\n                      data: {\n                        labels: analyticsData.reviewTrends.map(t => t.month),\n                        datasets: [{\n                          label: 'Accepted',\n                          data: analyticsData.reviewTrends.map(t => t.accepted || 0),\n                          backgroundColor: `${colorPalette.accent}B3`,\n                          borderColor: colorPalette.accent,\n                          borderWidth: 1\n                        }, {\n                          label: 'Pending',\n                          data: analyticsData.reviewTrends.map(t => t.pending || 0),\n                          backgroundColor: `${colorPalette.primary}B3`,\n                          borderColor: colorPalette.primary,\n                          borderWidth: 1\n                        }, {\n                          label: 'Denied',\n                          data: analyticsData.reviewTrends.map(t => t.denied || 0),\n                          backgroundColor: 'rgba(239, 68, 68, 0.7)',\n                          borderColor: 'rgba(239, 68, 68, 1)',\n                          borderWidth: 1\n                        }]\n                      },\n                      options: {\n                        responsive: true,\n                        maintainAspectRatio: false,\n                        scales: {\n                          x: {\n                            stacked: true,\n                            ticks: {\n                              maxRotation: 45,\n                              minRotation: 0\n                            }\n                          },\n                          y: {\n                            stacked: true,\n                            beginAtZero: true,\n                            ticks: {\n                              stepSize: 1\n                            }\n                          }\n                        },\n                        plugins: {\n                          legend: {\n                            position: 'bottom',\n                            labels: {\n                              usePointStyle: true,\n                              padding: 20,\n                              font: {\n                                size: 12\n                              }\n                            }\n                          },\n                          tooltip: {\n                            mode: 'index',\n                            intersect: false\n                          }\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center text-gray-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"h-16 w-16 mx-auto mb-4 text-gray-300\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          strokeWidth: 1,\n                          d: \"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1135,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1134,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No trend data available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1137,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1133,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.5\n                },\n                className: \"bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold mb-4\",\n                  style: {\n                    color: colorPalette.primary\n                  },\n                  children: \"Quality Metrics Summary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 rounded-lg\",\n                    style: {\n                      backgroundColor: `${colorPalette.primary}10`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-md font-medium mb-2\",\n                      style: {\n                        color: colorPalette.primary\n                      },\n                      children: \"Average Procedure Quality\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1154,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mr-4\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [analyticsData.qualityMetrics.avgProcedureQuality, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1156,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex\",\n                        children: renderStars(parseFloat(analyticsData.qualityMetrics.avgProcedureQuality))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1159,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1155,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1153,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 rounded-lg\",\n                    style: {\n                      backgroundColor: `${colorPalette.primary}10`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-md font-medium mb-2\",\n                      style: {\n                        color: colorPalette.primary\n                      },\n                      children: \"Average Patient Interaction\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1165,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-3xl font-bold mr-4\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: [analyticsData.qualityMetrics.avgPatientInteraction, \"/5\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1167,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex\",\n                        children: renderStars(parseFloat(analyticsData.qualityMetrics.avgPatientInteraction))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1170,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1166,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1164,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this), mainTab === 'reviews' && /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: container,\n              initial: \"hidden\",\n              whileInView: \"show\",\n              viewport: {\n                once: true\n              },\n              className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold\",\n                    style: {\n                      color: colorPalette.primary\n                    },\n                    children: reviewsTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Search by patient or student name\",\n                        value: searchQuery,\n                        onChange: e => setSearchQuery(e.target.value),\n                        className: \"pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1196,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(FaSearch, {\n                        className: \"absolute left-3 top-3 text-gray-400\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1209,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => document.getElementById('filterDropdown').classList.toggle('hidden'),\n                        className: \"px-3 py-2 border border-gray-300 rounded-lg flex items-center\",\n                        style: {\n                          borderColor: '#e5e7eb',\n                          color: colorPalette.text,\n                          transition: 'all 0.2s ease'\n                        },\n                        onMouseOver: e => e.currentTarget.style.backgroundColor = '#f9fafb',\n                        onMouseOut: e => e.currentTarget.style.backgroundColor = 'transparent',\n                        children: [/*#__PURE__*/_jsxDEV(FaFilter, {\n                          className: \"mr-2 text-gray-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1225,\n                          columnNumber: 29\n                        }, this), \" Filters\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1214,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1213,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1194,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  id: \"filterDropdown\",\n                  className: \"mb-6 p-4 border border-gray-200 rounded-lg hidden\",\n                  style: {\n                    backgroundColor: `${colorPalette.primary}05`\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Date Range\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1235,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: dayFilter,\n                        onChange: e => setDayFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Dates\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1247,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"today\",\n                          children: \"Today\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1248,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"tomorrow\",\n                          children: \"Tomorrow\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1249,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"week\",\n                          children: \"This Week\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1250,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"month\",\n                          children: \"This Month\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1251,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1236,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1234,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1256,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: hourFilter,\n                        onChange: e => setHourFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"All Hours\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1268,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"09:00\",\n                          children: \"09:00 AM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1269,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"10:00\",\n                          children: \"10:00 AM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1270,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"11:00\",\n                          children: \"11:00 AM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1271,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"12:00\",\n                          children: \"12:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1272,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"13:00\",\n                          children: \"01:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1273,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"14:00\",\n                          children: \"02:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1274,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"15:00\",\n                          children: \"03:00 PM\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1275,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1257,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1255,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Procedure Type\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1280,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: procedureFilter,\n                        onChange: e => setProcedureFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Procedures\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1292,\n                          columnNumber: 29\n                        }, this), uniqueProcedures.map(procedure => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: procedure,\n                          children: procedure\n                        }, procedure, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1294,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1281,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1279,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1300,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: studentFilter,\n                        onChange: e => setStudentFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Students\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1312,\n                          columnNumber: 29\n                        }, this), uniqueStudents.map(student => /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: student,\n                          children: student\n                        }, student, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1314,\n                          columnNumber: 31\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1301,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1299,\n                      columnNumber: 25\n                    }, this), reviewsTab === 'done' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"block text-sm font-medium mb-1\",\n                        style: {\n                          color: colorPalette.text\n                        },\n                        children: \"Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1322,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: statusFilter,\n                        onChange: e => setStatusFilter(e.target.value),\n                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg\",\n                        style: {\n                          outline: 'none',\n                          boxShadow: 'none',\n                          borderColor: '#e5e7eb',\n                          ':focus': {\n                            borderColor: colorPalette.primary\n                          }\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"all\",\n                          children: \"All Statuses\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1334,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"accepted\",\n                          children: \"Accepted\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1335,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1336,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1323,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1321,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-x-auto\",\n                  children: /*#__PURE__*/_jsxDEV(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                      style: {\n                        backgroundColor: `${colorPalette.primary}05`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Date\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1347,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Patient\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1348,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Student\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1349,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Procedure\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1350,\n                          columnNumber: 27\n                        }, this), reviewsTab === 'done' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                            style: {\n                              color: colorPalette.primary\n                            },\n                            children: \"Status\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1353,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                            style: {\n                              color: colorPalette.primary\n                            },\n                            children: \"Supervisor\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1354,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true), reviewsTab === 'pending' && /*#__PURE__*/_jsxDEV(\"th\", {\n                          className: \"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\",\n                          style: {\n                            color: colorPalette.primary\n                          },\n                          children: \"Actions\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1358,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1346,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1345,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                      className: \"bg-white divide-y divide-gray-200\",\n                      children: filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                        children: /*#__PURE__*/_jsxDEV(\"td\", {\n                          colSpan: reviewsTab === 'pending' ? 5 : 6,\n                          className: \"px-6 py-8 text-center\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col items-center justify-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              className: \"h-12 w-12 text-gray-400 mb-4\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              stroke: \"currentColor\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1368,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1367,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                              className: \"text-lg font-medium text-gray-900\",\n                              children: [\"No \", reviewsTab === 'pending' ? 'pending' : 'completed', \" reviews\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1370,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"mt-1 text-gray-500\",\n                              children: error ? 'Failed to load reviews due to an error. Check your permissions or try logging in again.' : reviewsTab === 'pending' ? 'No pending reviews are available. Check back later or ensure student reviews have been submitted.' : 'No completed reviews found. Try adjusting the filters or review pending submissions.'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1373,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1366,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1365,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1364,\n                        columnNumber: 27\n                      }, this) : filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).map(review => {\n                        var _review$patientId;\n                        return /*#__PURE__*/_jsxDEV(motion.tr, {\n                          initial: {\n                            opacity: 0\n                          },\n                          animate: {\n                            opacity: 1\n                          },\n                          className: \"hover:bg-gray-50 cursor-pointer\",\n                          onClick: () => setSelectedReview(review),\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                            children: new Date(reviewsTab === 'pending' ? review.submittedDate : review.reviewedDate || review.submittedDate).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1392,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: ((_review$patientId = review.patientId) === null || _review$patientId === void 0 ? void 0 : _review$patientId.fullName) || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1395,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.studentName || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1396,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                            children: review.procedureType || 'N/A'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1397,\n                            columnNumber: 31\n                          }, this), reviewsTab === 'done' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap\",\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\",\n                                style: {\n                                  backgroundColor: review.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\n                                  color: review.status === 'accepted' ? colorPalette.accent : '#b91c1c'\n                                },\n                                children: review.status\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1401,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1400,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                              children: review.supervisorName || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1411,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true), reviewsTab === 'pending' && /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => setSelectedReview(review),\n                              style: {\n                                color: colorPalette.primary\n                              },\n                              className: \"hover:underline\",\n                              children: \"Review\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1416,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1415,\n                            columnNumber: 33\n                          }, this)]\n                        }, review._id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1385,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1362,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1344,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1189,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this), selectedReview && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          scale: 0.9,\n          opacity: 0\n        },\n        animate: {\n          scale: 1,\n          opacity: 1\n        },\n        className: \"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold\",\n              style: {\n                color: colorPalette.primary\n              },\n              children: \"Review Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1448,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedReview(null),\n              className: \"text-gray-400 hover:text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4 flex items-center\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaUserMd, {\n                  className: \"h-5 w-5 mr-2\",\n                  style: {\n                    color: colorPalette.primary\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1460,\n                  columnNumber: 21\n                }, this), \"Patient Information\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1465,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: ((_selectedReview$patie = selectedReview.patientId) === null || _selectedReview$patie === void 0 ? void 0 : _selectedReview$patie.fullName) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1466,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1464,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"National ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1469,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: ((_selectedReview$patie2 = selectedReview.patientId) === null || _selectedReview$patie2 === void 0 ? void 0 : _selectedReview$patie2.nationalId) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1470,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1468,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4 flex items-center\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"h-5 w-5 mr-2\",\n                  style: {\n                    color: colorPalette.primary\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1478,\n                  columnNumber: 21\n                }, this), \"Review Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1483,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.studentName || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1484,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Student ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1487,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: typeof selectedReview.studentId === 'object' ? selectedReview.studentId.studentId : selectedReview.studentId || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1488,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1486,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Procedure\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1491,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.procedureType || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1492,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Submission Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1495,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.submittedDate ? new Date(selectedReview.submittedDate).toLocaleString() : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Student Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1501,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.comment || 'No comment'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1500,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1481,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4 flex items-center\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaListAlt, {\n                  className: \"h-5 w-5 mr-2\",\n                  style: {\n                    color: colorPalette.primary\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1510,\n                  columnNumber: 21\n                }, this), \"Procedure Steps\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ReviewStepsDisplay, {\n                reviewSteps: selectedReview.reviewSteps || [],\n                procedureType: selectedReview.procedureType\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1513,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1508,\n              columnNumber: 17\n            }, this), selectedReview.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: \"Submit Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1522,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Procedure Quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1525,\n                    columnNumber: 25\n                  }, this), renderStars(reviewForm.procedureQuality, rating => setReviewForm({\n                    ...reviewForm,\n                    procedureQuality: rating\n                  }))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1524,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Patient Interaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1531,\n                    columnNumber: 25\n                  }, this), renderStars(reviewForm.patientInteraction, rating => setReviewForm({\n                    ...reviewForm,\n                    patientInteraction: rating\n                  }))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1530,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1537,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: reviewForm.comment,\n                    onChange: e => setReviewForm({\n                      ...reviewForm,\n                      comment: e.target.value\n                    }),\n                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg\",\n                    style: {\n                      outline: 'none',\n                      boxShadow: 'none',\n                      borderColor: '#e5e7eb',\n                      ':focus': {\n                        borderColor: colorPalette.primary\n                      }\n                    },\n                    rows: \"4\",\n                    placeholder: \"Add any comments or feedback\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1538,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1536,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Review Decision\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1553,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-4 mt-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setReviewForm({\n                        ...reviewForm,\n                        status: 'accepted'\n                      }),\n                      className: \"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\",\n                      style: {\n                        backgroundColor: reviewForm.status === 'accepted' ? colorPalette.accent : '#f3f4f6',\n                        color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.text,\n                        fontWeight: reviewForm.status === 'accepted' ? '500' : 'normal',\n                        border: reviewForm.status === 'accepted' ? `2px solid ${colorPalette.accent}` : 'none'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                        className: \"mr-2\",\n                        style: {\n                          color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.accent\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1572,\n                        columnNumber: 29\n                      }, this), \"Accept\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1555,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => setReviewForm({\n                        ...reviewForm,\n                        status: 'denied'\n                      }),\n                      className: \"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\",\n                      style: {\n                        backgroundColor: reviewForm.status === 'denied' ? '#ef4444' : '#f3f4f6',\n                        color: reviewForm.status === 'denied' ? '#ffffff' : colorPalette.text,\n                        fontWeight: reviewForm.status === 'denied' ? '500' : 'normal',\n                        border: reviewForm.status === 'denied' ? '2px solid #dc2626' : 'none'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                        className: \"mr-2\",\n                        style: {\n                          color: reviewForm.status === 'denied' ? '#ffffff' : '#ef4444'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1594,\n                        columnNumber: 29\n                      }, this), \"Decline\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1577,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1554,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1552,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6 border-t pt-4 border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Signature\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1604,\n                    columnNumber: 25\n                  }, this), savedSignature ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex justify-between items-center mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"You have a saved signature\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1609,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                        whileHover: {\n                          scale: 1.05\n                        },\n                        whileTap: {\n                          scale: 0.95\n                        },\n                        onClick: () => setReviewForm(prev => ({\n                          ...prev,\n                          supervisorSignature: savedSignature\n                        })),\n                        className: \"px-4 py-2 text-white rounded-lg flex items-center\",\n                        style: {\n                          background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                          className: \"mr-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1617,\n                          columnNumber: 33\n                        }, this), \" Sign\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1610,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1608,\n                      columnNumber: 29\n                    }, this), reviewForm.supervisorSignature && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"border rounded-lg p-4\",\n                      style: {\n                        borderColor: '#e5e7eb',\n                        backgroundColor: `${colorPalette.primary}05`\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-medium mb-2\",\n                        style: {\n                          color: colorPalette.primary\n                        },\n                        children: \"Signature Preview\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1627,\n                        columnNumber: 33\n                      }, this), reviewForm.supervisorSignature.startsWith('data:image') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: reviewForm.supervisorSignature,\n                        alt: \"Signature\",\n                        className: \"max-h-16 mx-auto\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1629,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-signature text-lg text-center\",\n                        children: reviewForm.supervisorSignature\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1631,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1622,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1607,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 rounded-lg mb-4\",\n                    style: {\n                      backgroundColor: `${colorPalette.secondary}10`,\n                      border: `1px solid ${colorPalette.secondary}30`\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm mb-2\",\n                      style: {\n                        color: colorPalette.secondary\n                      },\n                      children: \"You don't have a saved signature. Please create one first.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1642,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      onClick: () => setShowSignatureModal(true),\n                      className: \"px-4 py-2 text-white rounded-lg flex items-center text-sm\",\n                      style: {\n                        backgroundColor: colorPalette.secondary\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                        className: \"mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1652,\n                        columnNumber: 31\n                      }, this), \" Create Signature\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1645,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1637,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1603,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-end gap-4 mt-6\",\n                  children: /*#__PURE__*/_jsxDEV(motion.button, {\n                    whileHover: {\n                      scale: 1.05\n                    },\n                    whileTap: {\n                      scale: 0.95\n                    },\n                    onClick: () => handleReviewSubmit(selectedReview._id),\n                    disabled: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature,\n                    className: \"px-6 py-3 rounded-lg flex items-center shadow-md\",\n                    style: {\n                      backgroundColor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? '#d1d5db' : 'transparent',\n                      background: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? '#d1d5db' : reviewForm.status === 'accepted' ? `linear-gradient(to right, ${colorPalette.accent}, ${colorPalette.accent})` : 'linear-gradient(to right, #ef4444, #b91c1c)',\n                      color: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? '#6b7280' : '#ffffff',\n                      cursor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature ? 'not-allowed' : 'pointer'\n                    },\n                    children: [reviewForm.status === 'accepted' ? /*#__PURE__*/_jsxDEV(FaCheck, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1683,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(FaTimes, {\n                      className: \"h-5 w-5 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1685,\n                      columnNumber: 29\n                    }, this), \"Submit \", reviewForm.status === 'accepted' ? 'Approval' : 'Decline']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1659,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1658,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1523,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 19\n            }, this), selectedReview.status !== 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6 rounded-lg\",\n              style: {\n                backgroundColor: `${colorPalette.primary}10`\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                style: {\n                  color: colorPalette.primary\n                },\n                children: \"Supervisor Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1697,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Supervisor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1700,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.supervisorName || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1701,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1699,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Reviewed Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1704,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.reviewedDate ? new Date(selectedReview.reviewedDate).toLocaleString() : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1705,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1703,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Procedure Quality\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1710,\n                    columnNumber: 25\n                  }, this), renderStars(selectedReview.procedureQuality || 0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1709,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Patient Interaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1714,\n                    columnNumber: 25\n                  }, this), renderStars(selectedReview.patientInteraction || 0)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1713,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1718,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: selectedReview.comment || 'No comment'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1719,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1717,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1722,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\",\n                    style: {\n                      backgroundColor: selectedReview.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\n                      color: selectedReview.status === 'accepted' ? colorPalette.accent : '#b91c1c'\n                    },\n                    children: selectedReview.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1723,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1721,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 pt-4 border-t border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Supervisor Signature\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1736,\n                    columnNumber: 25\n                  }, this), selectedReview.supervisorSignature ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"border rounded-lg p-4\",\n                    style: {\n                      borderColor: '#e5e7eb',\n                      backgroundColor: colorPalette.background\n                    },\n                    children: selectedReview.supervisorSignature.startsWith('data:image') ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: selectedReview.supervisorSignature,\n                      alt: \"Signature\",\n                      className: \"max-h-20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1743,\n                      columnNumber: 31\n                    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"font-signature text-lg\",\n                      children: selectedReview.supervisorSignature\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1745,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1738,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500\",\n                    children: \"No signature provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1749,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1735,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1698,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1696,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1456,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1446,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1441,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1440,\n      columnNumber: 9\n    }, this), showSignatureModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          type: \"spring\",\n          stiffness: 300,\n          damping: 30\n        },\n        className: \"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 rounded-t-xl\",\n          style: {\n            background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-white flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaSignature, {\n                className: \"mr-3 h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1775,\n                columnNumber: 19\n              }, this), \"Manage Your Signature\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1774,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSignatureModal(false),\n              className: \"text-white hover:text-blue-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1783,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1782,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1778,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1773,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1770,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 rounded-lg mb-6 border-l-4\",\n            style: {\n              backgroundColor: `${colorPalette.primary}10`,\n              borderColor: colorPalette.primary\n            },\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: colorPalette.primary\n              },\n              children: \"Create or update your signature below. This signature will be used for all your review approvals.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1795,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1790,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(SignatureManager, {\n            initialSignature: savedSignature,\n            onSignatureSelect: signature => {\n              setSavedSignature(signature);\n              // Close modal after a short delay to show success\n              setTimeout(() => setShowSignatureModal(false), 1500);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1800,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1789,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1764,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1763,\n      columnNumber: 9\n    }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -50\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      exit: {\n        opacity: 0,\n        y: 50\n      },\n      className: \"fixed top-0 inset-x-0 flex justify-center items-start z-50 pt-6 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl border-l-4 p-4 flex items-center max-w-md w-full\",\n        style: {\n          borderColor: colorPalette.accent\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-full mr-4\",\n          style: {\n            backgroundColor: `${colorPalette.accent}20`\n          },\n          children: /*#__PURE__*/_jsxDEV(FaCheck, {\n            className: \"text-xl\",\n            style: {\n              color: colorPalette.accent\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1824,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1823,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-medium\",\n            style: {\n              color: colorPalette.text\n            },\n            children: \"Success!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1827,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#6b7280'\n            },\n            children: successMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1828,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1826,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowSuccessModal(false),\n          className: \"text-gray-400 hover:text-gray-500\",\n          children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1834,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1830,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1821,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1815,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 576,\n    columnNumber: 5\n  }, this);\n};\n_s(SupervisorDashboard, \"TbERPC93FG96KvaLyst8zD4TTjQ=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = SupervisorDashboard;\nexport default SupervisorDashboard;\nvar _c;\n$RefreshReg$(_c, \"SupervisorDashboard\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Loader", "motion", "FaUserMd", "FaCalendarAlt", "FaStar", "FaCheck", "FaTimes", "FaChart<PERSON>ie", "FaPercentage", "FaStarHalfAlt", "FaFilter", "FaSearch", "FaChartBar", "FaClipboardCheck", "FaUserGraduate", "FaSignature", "FaCalendarCheck", "FaListAlt", "FaCheckCircle", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "CategoryScale", "LinearScale", "BarElement", "Title", "Pie", "Bar", "SignatureManager", "ReviewStepsDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "colorPalette", "primary", "secondary", "background", "text", "accent", "register", "defaults", "font", "family", "size", "color", "plugins", "tooltip", "backgroundColor", "titleColor", "bodyColor", "legend", "labels", "usePointStyle", "padding", "SupervisorDashboard", "_s", "_selectedReview$patie", "_selectedReview$patie2", "navigate", "user", "token", "loading", "setLoading", "error", "setError", "pendingReviews", "setPendingReviews", "doneReviews", "setDoneReviews", "allReviews", "setAllReviews", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedReview", "dayFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hourFilter", "setHour<PERSON><PERSON>er", "searchQuery", "setSearch<PERSON>uery", "procedureFilter", "setProcedureFilter", "studentFilter", "setStudentFilter", "statusFilter", "setStatus<PERSON>ilter", "mainTab", "setMainTab", "reviewsTab", "setReviewsTab", "savedSignature", "setSavedSignature", "showSignatureModal", "setShowSignatureModal", "showSuccessModal", "setShowSuccessModal", "successMessage", "setSuccessMessage", "analyticsTimeRange", "setAnalyticsTimeRange", "analyticsData", "setAnalyticsData", "statusDistribution", "accepted", "pending", "denied", "procedureTypeDistribution", "studentPerformance", "reviewTrends", "qualityMetrics", "avgProcedureQuality", "avgPatientInteraction", "uniqueStudents", "setUniqueStudents", "uniqueProcedures", "setUniqueProcedures", "reviewForm", "setReviewForm", "procedureQuality", "patientInteraction", "comment", "status", "supervisorSignature", "fetchData", "console", "log", "role", "config", "headers", "Authorization", "allReviewsRes", "get", "data", "reviews", "Array", "isArray", "filter", "r", "students", "Set", "map", "_r$studentId", "studentId", "name", "studentName", "Boolean", "procedures", "procedureType", "signatureRes", "signature", "signatureErr", "analyticsRes", "_analyticsRes$data$st", "_analyticsRes$data$st2", "_analyticsRes$data$st3", "_analyticsRes$data$qu", "_analyticsRes$data$qu2", "analyticsErr", "length", "calculateAnalytics", "generateSampleData", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response4$data", "_err$response5", "_err$response6", "message", "response", "errorMessage", "localStorage", "removeItem", "handleAnalyticsTimeRangeChange", "newRange", "total", "month", "procedureTypes", "for<PERSON>ach", "review", "type", "Object", "keys", "_review$studentId", "qualityRatings", "interactionRatings", "push", "student", "reduce", "sum", "rating", "toFixed", "reviewsByMonth", "now", "Date", "sixMonthsAgo", "setMonth", "getMonth", "date", "submittedDate", "monthYear", "getFullYear", "sort", "a", "b", "aMonth", "aYear", "split", "Number", "b<PERSON>onth", "bYear", "doneReviewsArr", "newAnalyticsData", "handleReviewSubmit", "reviewId", "updatedReview", "put", "reviewToMove", "find", "_id", "updatedReviewWithSupervisor", "<PERSON><PERSON><PERSON>", "reviewedDate", "prev", "allReviewsUpdated", "setTimeout", "_err$response7", "_err$response8", "_err$response9", "_err$response9$data", "filterReviews", "filtered", "today", "setHours", "reviewDate", "toDateString", "tomorrow", "setDate", "getDate", "weekEnd", "monthStart", "nextMonth", "hours", "getHours", "toString", "padStart", "_r$patientId", "_r$patientId$fullName", "_r$studentName", "patientId", "fullName", "toLowerCase", "includes", "dateA", "dateB", "totalReviews", "acceptedReviews", "deniedReviews", "acceptanceRate", "denialRate", "renderStars", "onClick", "className", "children", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "toggleSidebar", "style", "div", "initial", "animate", "fill", "viewBox", "fillRule", "d", "clipRule", "duration", "button", "whileHover", "scale", "whileTap", "borderBottom", "variants", "whileInView", "viewport", "once", "value", "onChange", "e", "target", "outline", "boxShadow", "borderColor", "process", "env", "NODE_ENV", "JSON", "stringify", "delay", "values", "some", "val", "datasets", "borderWidth", "options", "responsive", "maintainAspectRatio", "position", "callbacks", "label", "context", "raw", "dataset", "percentage", "Math", "round", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "p", "scales", "x", "stacked", "ticks", "maxRotation", "minRotation", "beginAtZero", "stepSize", "mode", "intersect", "entries", "index", "parseFloat", "colSpan", "t", "placeholder", "document", "getElementById", "classList", "toggle", "onMouseOver", "currentTarget", "onMouseOut", "id", "procedure", "_review$patientId", "tr", "toLocaleDateString", "nationalId", "toLocaleString", "reviewSteps", "rows", "fontWeight", "border", "startsWith", "src", "alt", "disabled", "cursor", "stiffness", "damping", "initialSignature", "onSignatureSelect", "exit", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Co<PERSON>/dentlyzer-frontend/src/supervisor/Dashboard.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from '../student/Navbar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport {\r\n  FaUserMd, FaCalendarAlt, FaStar, FaCheck, FaTimes,\r\n  FaChartPie, FaPercentage, FaStarHalfAlt, FaFilter, FaSearch,\r\n  FaChartBar, FaClipboardCheck, FaUserGraduate, FaSignature,\r\n  FaCalendarCheck, FaListAlt, FaCheckCircle\r\n} from 'react-icons/fa';\r\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';\r\nimport { Pie, Bar } from 'react-chartjs-2';\r\n\r\n// Import custom components\r\nimport SignatureManager from './SignatureManager';\r\nimport ReviewStepsDisplay from './ReviewStepsDisplay';\r\n\r\n// Website color palette\r\nconst colorPalette = {\r\n  primary: '#0077B6',\r\n  secondary: '#20B2AA',\r\n  background: '#FFFFFF',\r\n  text: '#333333',\r\n  accent: '#28A745'\r\n};\r\n\r\n// Register ChartJS components\r\nChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);\r\n\r\n// Chart.js default configuration\r\nChartJS.defaults.font.family = 'Inter, system-ui, sans-serif';\r\nChartJS.defaults.font.size = 12;\r\nChartJS.defaults.color = '#6b7280';\r\nChartJS.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';\r\nChartJS.defaults.plugins.tooltip.titleColor = '#ffffff';\r\nChartJS.defaults.plugins.tooltip.bodyColor = '#ffffff';\r\nChartJS.defaults.plugins.legend.labels.usePointStyle = true;\r\nChartJS.defaults.plugins.legend.labels.padding = 20;\r\n\r\nconst SupervisorDashboard = () => {\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [pendingReviews, setPendingReviews] = useState([]);\r\n  const [doneReviews, setDoneReviews] = useState([]);\r\n  const [allReviews, setAllReviews] = useState([]);\r\n  const [selectedReview, setSelectedReview] = useState(null);\r\n  const [dayFilter, setDayFilter] = useState('all');\r\n  const [hourFilter, setHourFilter] = useState('');\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [procedureFilter, setProcedureFilter] = useState('all');\r\n  const [studentFilter, setStudentFilter] = useState('all');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [mainTab, setMainTab] = useState('reviews');\r\n  const [reviewsTab, setReviewsTab] = useState('pending');\r\n  const [savedSignature, setSavedSignature] = useState(null);\r\n  const [showSignatureModal, setShowSignatureModal] = useState(false);\r\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [analyticsTimeRange, setAnalyticsTimeRange] = useState('month');\r\n  const [analyticsData, setAnalyticsData] = useState({\r\n    statusDistribution: { accepted: 0, pending: 0, denied: 0 },\r\n    procedureTypeDistribution: {},\r\n    studentPerformance: {},\r\n    reviewTrends: [],\r\n    qualityMetrics: {\r\n      avgProcedureQuality: 0,\r\n      avgPatientInteraction: 0\r\n    }\r\n  });\r\n  const [uniqueStudents, setUniqueStudents] = useState([]);\r\n  const [uniqueProcedures, setUniqueProcedures] = useState([]);\r\n\r\n  const [reviewForm, setReviewForm] = useState({\r\n    procedureQuality: 0,\r\n    patientInteraction: 0,\r\n    comment: '',\r\n    status: 'accepted',\r\n    supervisorSignature: null\r\n  });\r\n\r\n\r\n\r\n  // Fetch all reviews and signature data\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      console.log('Auth data:', { user, token }); // Debug auth\r\n      if (!user || !token || user.role !== 'supervisor') {\r\n        setError('Please log in as a supervisor to view this dashboard.');\r\n        navigate('/login');\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setLoading(true);\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n\r\n        // Fetch all reviews for the supervisor\r\n        console.log('Fetching all supervisor reviews');\r\n        const allReviewsRes = await axios.get('http://localhost:5000/api/reviews/supervisor', config);\r\n        console.log('All reviews response:', allReviewsRes.data);\r\n\r\n        const reviews = Array.isArray(allReviewsRes.data) ? allReviewsRes.data : [];\r\n        setAllReviews(reviews);\r\n\r\n        // Separate reviews by status\r\n        setPendingReviews(reviews.filter(r => r.status === 'pending'));\r\n        setDoneReviews(reviews.filter(r => r.status !== 'pending'));\r\n\r\n        // Extract unique students and procedure types for filtering\r\n        const students = [...new Set(reviews.map(r => r.studentId?.name || r.studentName))].filter(Boolean);\r\n        const procedures = [...new Set(reviews.map(r => r.procedureType))].filter(Boolean);\r\n\r\n        setUniqueStudents(students);\r\n        setUniqueProcedures(procedures);\r\n\r\n        // Fetch supervisor's saved signature if available\r\n        try {\r\n          const signatureRes = await axios.get('http://localhost:5000/api/supervisors/signature', config);\r\n          if (signatureRes.data && signatureRes.data.signature) {\r\n            setSavedSignature(signatureRes.data.signature);\r\n          }\r\n        } catch (signatureErr) {\r\n          console.error('Error fetching signature:', signatureErr);\r\n          // Non-critical error, continue without signature\r\n        }\r\n\r\n        // Fetch analytics data from the server\r\n        try {\r\n          console.log('Fetching analytics data for time range:', analyticsTimeRange);\r\n          const analyticsRes = await axios.get(`http://localhost:5000/api/reviews/analytics?timeRange=${analyticsTimeRange}`, config);\r\n          console.log('Analytics response:', analyticsRes.data);\r\n          \r\n          if (analyticsRes.data) {\r\n            // Ensure all required fields exist with proper defaults\r\n            const analyticsData = {\r\n              statusDistribution: {\r\n                accepted: analyticsRes.data.statusDistribution?.accepted || 0,\r\n                pending: analyticsRes.data.statusDistribution?.pending || 0,\r\n                denied: analyticsRes.data.statusDistribution?.denied || 0\r\n              },\r\n              procedureTypeDistribution: analyticsRes.data.procedureTypeDistribution || {},\r\n              studentPerformance: analyticsRes.data.studentPerformance || {},\r\n              reviewTrends: analyticsRes.data.reviewTrends || [],\r\n              qualityMetrics: {\r\n                avgProcedureQuality: analyticsRes.data.qualityMetrics?.avgProcedureQuality || 0,\r\n                avgPatientInteraction: analyticsRes.data.qualityMetrics?.avgPatientInteraction || 0\r\n              }\r\n            };\r\n            setAnalyticsData(analyticsData);\r\n          }\r\n        } catch (analyticsErr) {\r\n          console.error('Error fetching analytics:', analyticsErr);\r\n          console.log('Falling back to client-side calculation');\r\n          // Fallback to client-side calculation if server analytics fails\r\n          if (reviews.length > 0) {\r\n            calculateAnalytics(reviews);\r\n          } else {\r\n            // If no reviews data, use sample data for testing\r\n            console.log('No reviews data available, using sample data');\r\n            setAnalyticsData(generateSampleData());\r\n          }\r\n        }\r\n\r\n        setLoading(false);\r\n      } catch (err) {\r\n        console.error('Fetch error:', {\r\n          message: err.message,\r\n          status: err.response?.status,\r\n          data: err.response?.data,\r\n          headers: err.response?.headers,\r\n        });\r\n        const errorMessage = err.response?.data?.message ||\r\n                            (err.response?.status === 403 ? 'Access denied: Insufficient permissions' :\r\n                            'Failed to load reviews. Please try again.');\r\n        setError(errorMessage);\r\n        if (err.response?.status === 401) {\r\n          localStorage.removeItem('token');\r\n          localStorage.removeItem('user');\r\n          navigate('/login');\r\n        }\r\n        setPendingReviews([]);\r\n        setDoneReviews([]);\r\n        setAllReviews([]);\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [user, token, navigate, analyticsTimeRange]);\r\n\r\n  // Fetch new analytics data when time range changes\r\n  const handleAnalyticsTimeRangeChange = (newRange) => {\r\n    setAnalyticsTimeRange(newRange);\r\n  };\r\n\r\n  // Generate sample data for testing charts\r\n  const generateSampleData = () => {\r\n    return {\r\n      statusDistribution: { accepted: 15, pending: 8, denied: 3 },\r\n      procedureTypeDistribution: {\r\n        'Operative Dentistry': { total: 10, accepted: 7, denied: 2, pending: 1 },\r\n        'Endodontics': { total: 8, accepted: 5, denied: 1, pending: 2 },\r\n        'Periodontics': { total: 6, accepted: 3, denied: 0, pending: 3 },\r\n        'Fixed Prosthodontics': { total: 2, accepted: 0, denied: 0, pending: 2 }\r\n      },\r\n      studentPerformance: {\r\n        'John Doe': { total: 8, accepted: 6, denied: 1, pending: 1, avgProcedureQuality: 4.2, avgPatientInteraction: 4.5 },\r\n        'Jane Smith': { total: 6, accepted: 4, denied: 1, pending: 1, avgProcedureQuality: 3.8, avgPatientInteraction: 4.0 },\r\n        'Mike Johnson': { total: 4, accepted: 3, denied: 0, pending: 1, avgProcedureQuality: 4.5, avgPatientInteraction: 4.2 }\r\n      },\r\n      reviewTrends: [\r\n        { month: '1/2024', total: 5, accepted: 3, denied: 1, pending: 1 },\r\n        { month: '2/2024', total: 8, accepted: 6, denied: 1, pending: 1 },\r\n        { month: '3/2024', total: 6, accepted: 4, denied: 1, pending: 1 },\r\n        { month: '4/2024', total: 7, accepted: 2, denied: 0, pending: 5 }\r\n      ],\r\n      qualityMetrics: {\r\n        avgProcedureQuality: 4.1,\r\n        avgPatientInteraction: 4.2\r\n      }\r\n    };\r\n  };\r\n\r\n  // Calculate analytics from reviews data\r\n  const calculateAnalytics = (reviews) => {\r\n    if (!Array.isArray(reviews) || reviews.length === 0) {\r\n      console.log('No reviews data available for analytics calculation');\r\n      return;\r\n    }\r\n\r\n    console.log('Calculating analytics for', reviews.length, 'reviews');\r\n\r\n    // Status distribution\r\n    const pending = reviews.filter(r => r.status === 'pending').length;\r\n    const accepted = reviews.filter(r => r.status === 'accepted').length;\r\n    const denied = reviews.filter(r => r.status === 'denied').length;\r\n\r\n    console.log('Status distribution:', { pending, accepted, denied });\r\n\r\n    // Procedure type distribution\r\n    const procedureTypes = {};\r\n    reviews.forEach(review => {\r\n      const type = review.procedureType || 'Unknown';\r\n      if (!procedureTypes[type]) {\r\n        procedureTypes[type] = {\r\n          total: 0,\r\n          accepted: 0,\r\n          denied: 0,\r\n          pending: 0\r\n        };\r\n      }\r\n      procedureTypes[type].total++;\r\n      if (review.status === 'accepted') procedureTypes[type].accepted++;\r\n      else if (review.status === 'denied') procedureTypes[type].denied++;\r\n      else procedureTypes[type].pending++;\r\n    });\r\n\r\n    console.log('Procedure types:', Object.keys(procedureTypes));\r\n\r\n    // Student performance\r\n    const studentPerformance = {};\r\n    reviews.forEach(review => {\r\n      const studentName = review.studentName || review.studentId?.name || 'Unknown';\r\n      if (!studentPerformance[studentName]) {\r\n        studentPerformance[studentName] = {\r\n          total: 0,\r\n          accepted: 0,\r\n          denied: 0,\r\n          pending: 0,\r\n          avgProcedureQuality: 0,\r\n          avgPatientInteraction: 0,\r\n          qualityRatings: [],\r\n          interactionRatings: []\r\n        };\r\n      }\r\n\r\n      studentPerformance[studentName].total++;\r\n      if (review.status === 'accepted') studentPerformance[studentName].accepted++;\r\n      else if (review.status === 'denied') studentPerformance[studentName].denied++;\r\n      else studentPerformance[studentName].pending++;\r\n\r\n      if (review.procedureQuality) {\r\n        studentPerformance[studentName].qualityRatings.push(review.procedureQuality);\r\n      }\r\n\r\n      if (review.patientInteraction) {\r\n        studentPerformance[studentName].interactionRatings.push(review.patientInteraction);\r\n      }\r\n    });\r\n\r\n    // Calculate averages for each student\r\n    Object.keys(studentPerformance).forEach(student => {\r\n      const { qualityRatings, interactionRatings } = studentPerformance[student];\r\n\r\n      if (qualityRatings.length > 0) {\r\n        studentPerformance[student].avgProcedureQuality =\r\n          (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);\r\n      }\r\n\r\n      if (interactionRatings.length > 0) {\r\n        studentPerformance[student].avgPatientInteraction =\r\n          (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);\r\n      }\r\n\r\n      // Remove the arrays from the response\r\n      delete studentPerformance[student].qualityRatings;\r\n      delete studentPerformance[student].interactionRatings;\r\n    });\r\n\r\n    console.log('Student performance calculated for', Object.keys(studentPerformance).length, 'students');\r\n\r\n    // Review trends by month\r\n    const reviewsByMonth = {};\r\n    const now = new Date();\r\n    const sixMonthsAgo = new Date();\r\n    sixMonthsAgo.setMonth(now.getMonth() - 6);\r\n\r\n    reviews.forEach(review => {\r\n      const date = new Date(review.submittedDate);\r\n      if (date >= sixMonthsAgo) {\r\n        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;\r\n        if (!reviewsByMonth[monthYear]) {\r\n          reviewsByMonth[monthYear] = { total: 0, accepted: 0, denied: 0, pending: 0 };\r\n        }\r\n        reviewsByMonth[monthYear].total++;\r\n        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;\r\n        else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;\r\n        else reviewsByMonth[monthYear].pending++;\r\n      }\r\n    });\r\n\r\n    // Convert to array and sort by date\r\n    const reviewTrends = Object.keys(reviewsByMonth).map(month => ({\r\n      month,\r\n      ...reviewsByMonth[month]\r\n    })).sort((a, b) => {\r\n      const [aMonth, aYear] = a.month.split('/').map(Number);\r\n      const [bMonth, bYear] = b.month.split('/').map(Number);\r\n      return aYear === bYear ? aMonth - bMonth : aYear - bYear;\r\n    });\r\n\r\n    console.log('Review trends calculated for', reviewTrends.length, 'months');\r\n\r\n    // Quality metrics\r\n    const doneReviewsArr = reviews.filter(r => r.status !== 'pending');\r\n    const avgProcedureQuality = doneReviewsArr.length > 0\r\n      ? (doneReviewsArr.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviewsArr.length).toFixed(1)\r\n      : 0;\r\n    const avgPatientInteraction = doneReviewsArr.length > 0\r\n      ? (doneReviewsArr.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviewsArr.length).toFixed(1)\r\n      : 0;\r\n\r\n    console.log('Quality metrics:', { avgProcedureQuality, avgPatientInteraction });\r\n\r\n    // Update analytics state\r\n    const newAnalyticsData = {\r\n      statusDistribution: { accepted, pending, denied },\r\n      procedureTypeDistribution: procedureTypes,\r\n      studentPerformance,\r\n      reviewTrends,\r\n      qualityMetrics: {\r\n        avgProcedureQuality,\r\n        avgPatientInteraction\r\n      }\r\n    };\r\n\r\n    console.log('Setting analytics data:', newAnalyticsData);\r\n    setAnalyticsData(newAnalyticsData);\r\n  };\r\n\r\n\r\n\r\n  const handleReviewSubmit = async (reviewId) => {\r\n    if (reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0) {\r\n      setError('Please provide ratings for Procedure Quality and Patient Interaction');\r\n      return;\r\n    }\r\n\r\n    if (!reviewForm.supervisorSignature) {\r\n      setError('Please provide your signature to complete the review');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const config = { headers: { Authorization: `Bearer ${token}` } };\r\n      const updatedReview = {\r\n        status: reviewForm.status,\r\n        procedureQuality: reviewForm.procedureQuality,\r\n        patientInteraction: reviewForm.patientInteraction,\r\n        comment: reviewForm.comment || '',\r\n        supervisorSignature: reviewForm.supervisorSignature\r\n      };\r\n\r\n      console.log('Submitting review update:', { reviewId, ...updatedReview, supervisorSignature: 'signature data present' });\r\n      const response = await axios.put(`http://localhost:5000/api/reviews/${reviewId}`, updatedReview, config);\r\n      console.log('Review updated:', response.data);\r\n\r\n      // Save the signature for future use\r\n      setSavedSignature(reviewForm.supervisorSignature);\r\n\r\n      // Update local state\r\n      const reviewToMove = pendingReviews.find(r => r._id === reviewId);\r\n\r\n      // Make sure supervisor name is set in the local state\r\n      const updatedReviewWithSupervisor = {\r\n        ...updatedReview,\r\n        supervisorName: user?.name || 'Unknown Supervisor',\r\n        reviewedDate: new Date()\r\n      };\r\n\r\n      setPendingReviews(prev => prev.filter(r => r._id !== reviewId));\r\n      setDoneReviews(prev => [...prev, { ...reviewToMove, ...updatedReviewWithSupervisor }]);\r\n\r\n      // Recalculate analytics with the updated review\r\n      const allReviewsUpdated = [\r\n        ...doneReviews,\r\n        { ...reviewToMove, ...updatedReviewWithSupervisor },\r\n        ...pendingReviews.filter(r => r._id !== reviewId)\r\n      ];\r\n      calculateAnalytics(allReviewsUpdated);\r\n\r\n      // Reset form and close modal\r\n      setSelectedReview(null);\r\n      setReviewForm({\r\n        procedureQuality: 0,\r\n        patientInteraction: 0,\r\n        comment: '',\r\n        status: 'accepted',\r\n        supervisorSignature: null\r\n      });\r\n      setError('');\r\n\r\n      // Show success message in a styled popup\r\n      setSuccessMessage(`Review ${reviewForm.status === 'accepted' ? 'approved' : 'declined'} successfully!`);\r\n      setShowSuccessModal(true);\r\n\r\n      // Auto-hide the success message after 3 seconds\r\n      setTimeout(() => {\r\n        setShowSuccessModal(false);\r\n      }, 3000);\r\n    } catch (err) {\r\n      console.error('Review submit error:', {\r\n        message: err.message,\r\n        status: err.response?.status,\r\n        data: err.response?.data,\r\n      });\r\n      setError(err.response?.data?.message || 'Failed to submit review');\r\n    }\r\n  };\r\n\r\n  const filterReviews = (reviews) => {\r\n    let filtered = [...reviews];\r\n\r\n    // Day filter\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n    if (dayFilter === 'today') {\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate.toDateString() === today.toDateString();\r\n      });\r\n    } else if (dayFilter === 'tomorrow') {\r\n      const tomorrow = new Date(today);\r\n      tomorrow.setDate(tomorrow.getDate() + 1);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate.toDateString() === tomorrow.toDateString();\r\n      });\r\n    } else if (dayFilter === 'week') {\r\n      const weekEnd = new Date(today);\r\n      weekEnd.setDate(weekEnd.getDate() + 7);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate >= today && reviewDate <= weekEnd;\r\n      });\r\n    } else if (dayFilter === 'month') {\r\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\r\n      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        return reviewDate >= monthStart && reviewDate < nextMonth;\r\n      });\r\n    }\r\n\r\n    // Hour filter\r\n    if (hourFilter) {\r\n      filtered = filtered.filter(r => {\r\n        const reviewDate = new Date(r.submittedDate);\r\n        const hours = reviewDate.getHours().toString().padStart(2, '0');\r\n        return `${hours}:00` === hourFilter;\r\n      });\r\n    }\r\n\r\n    // Search by patient name or student name\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(r =>\r\n        (r.patientId?.fullName?.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n        (r.studentName?.toLowerCase().includes(searchQuery.toLowerCase()))\r\n      );\r\n    }\r\n\r\n    // Procedure type filter\r\n    if (procedureFilter && procedureFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.procedureType === procedureFilter);\r\n    }\r\n\r\n    // Student filter\r\n    if (studentFilter && studentFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.studentName === studentFilter);\r\n    }\r\n\r\n    // Status filter (only applies to the \"done\" tab)\r\n    if (reviewsTab === 'done' && statusFilter && statusFilter !== 'all') {\r\n      filtered = filtered.filter(r => r.status === statusFilter);\r\n    }\r\n\r\n    // Sort by date (submittedDate for pending, reviewedDate for done)\r\n    return filtered.sort((a, b) => {\r\n      const dateA = new Date(reviewsTab === 'pending' ? a.submittedDate : a.reviewedDate || a.submittedDate);\r\n      const dateB = new Date(reviewsTab === 'pending' ? b.submittedDate : b.reviewedDate || b.submittedDate);\r\n      return dateB - dateA; // Newest first\r\n    });\r\n  };\r\n\r\n  // Analytics calculations\r\n  const totalReviews = pendingReviews.length + doneReviews.length;\r\n  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;\r\n  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;\r\n  const acceptanceRate = totalReviews > 0 ? ((acceptedReviews / totalReviews) * 100).toFixed(1) : 0;\r\n  const denialRate = totalReviews > 0 ? ((deniedReviews / totalReviews) * 100).toFixed(1) : 0;\r\n  const avgProcedureQuality =\r\n    doneReviews.length > 0\r\n      ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1)\r\n      : 0;\r\n  const avgPatientInteraction =\r\n    doneReviews.length > 0\r\n      ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1)\r\n      : 0;\r\n\r\n  const renderStars = (rating, onClick = null) => (\r\n    <div className=\"flex\">\r\n      {[...Array(5)].map((_, i) => (\r\n        <FaStar\r\n          key={i}\r\n          className={`h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} ${\r\n            onClick ? 'cursor-pointer' : ''\r\n          }`}\r\n          onClick={onClick ? () => onClick(i + 1) : null}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: {\r\n      opacity: 1,\r\n      transition: { staggerChildren: 0.1 },\r\n    },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return <Loader />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-gray-50\">\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <Navbar toggleSidebar={() => {}} />\r\n        <main className=\"flex-1 overflow-y-auto p-6\" style={{ background: `linear-gradient(to bottom right, ${colorPalette.primary}10, ${colorPalette.background})` }}>\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            {error && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: -20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                className=\"mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <svg className=\"w-5 h-5 text-red-500 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path\r\n                      fillRule=\"evenodd\"\r\n                      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                      clipRule=\"evenodd\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-red-700 font-medium\">{error}</p>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n\r\n            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>\r\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\r\n                <div>\r\n                  <h1 className=\"text-3xl md:text-4xl font-bold mb-1\" style={{ color: colorPalette.primary }}>Supervisor Dashboard</h1>\r\n                  <p style={{ color: colorPalette.text }}>Welcome back, {user?.name || 'Supervisor'}</p>\r\n                </div>\r\n                <div className=\"flex gap-3\">\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    onClick={() => setShowSignatureModal(true)}\r\n                    className=\"px-4 py-2 text-white rounded-lg flex items-center\"\r\n                    style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}\r\n                  >\r\n                    <FaSignature className=\"mr-2\" /> Signature\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Main Tabs */}\r\n              <div className=\"mb-8\">\r\n                <div className=\"flex border-b border-gray-200\">\r\n                  <button\r\n                    onClick={() => setMainTab('reviews')}\r\n                    className=\"px-6 py-3 font-medium text-base\"\r\n                    style={{\r\n                      borderBottom: mainTab === 'reviews' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                      color: mainTab === 'reviews' ? colorPalette.primary : '#6b7280',\r\n                      backgroundColor: mainTab === 'reviews' ? `${colorPalette.primary}10` : 'transparent'\r\n                    }}\r\n                  >\r\n                    <FaClipboardCheck className=\"h-5 w-5 inline mr-2\" />\r\n                    Reviews\r\n                  </button>\r\n                  <button\r\n                    onClick={() => setMainTab('analytics')}\r\n                    className=\"px-6 py-3 font-medium text-base\"\r\n                    style={{\r\n                      borderBottom: mainTab === 'analytics' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                      color: mainTab === 'analytics' ? colorPalette.primary : '#6b7280',\r\n                      backgroundColor: mainTab === 'analytics' ? `${colorPalette.primary}10` : 'transparent'\r\n                    }}\r\n                  >\r\n                    <FaChartBar className=\"h-5 w-5 inline mr-2\" />\r\n                    Analytics\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Reviews Section */}\r\n              {mainTab === 'reviews' && (\r\n                <>\r\n                  {/* Analytics Summary */}\r\n                  <motion.div\r\n                    variants={container}\r\n                    initial=\"hidden\"\r\n                    whileInView=\"show\"\r\n                    viewport={{ once: true }}\r\n                    className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\"\r\n                  >\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaChartPie className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Total Reviews</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{totalReviews}</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaPercentage className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Acceptance Rate</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{acceptanceRate}%</p>\r\n                          <p className=\"text-xs\" style={{ color: colorPalette.text }}>Decline: {denialRate}%</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaStarHalfAlt className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Avg. Procedure Quality</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{avgProcedureQuality}/5</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                    <motion.div\r\n                      variants={item}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group\"\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"w-14 h-14 rounded-lg flex items-center justify-center mr-4 transition-colors duration-300\"\r\n                          style={{\r\n                            backgroundColor: `${colorPalette.primary}10`,\r\n                            color: colorPalette.primary\r\n                          }}>\r\n                          <FaStarHalfAlt className=\"h-6 w-6\" style={{ color: colorPalette.primary }} />\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>Avg. Patient Interaction</p>\r\n                          <p className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>{avgPatientInteraction}/5</p>\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                  </motion.div>\r\n\r\n                  {/* Reviews Tabs */}\r\n                  <div className=\"mb-6\">\r\n                    <div className=\"flex border-b border-gray-200\">\r\n                      <button\r\n                        onClick={() => setReviewsTab('pending')}\r\n                        className=\"px-4 py-2 font-medium text-sm\"\r\n                        style={{\r\n                          borderBottom: reviewsTab === 'pending' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                          color: reviewsTab === 'pending' ? colorPalette.primary : '#6b7280'\r\n                        }}\r\n                      >\r\n                        Pending Reviews ({pendingReviews.length})\r\n                      </button>\r\n                      <button\r\n                        onClick={() => setReviewsTab('done')}\r\n                        className=\"px-4 py-2 font-medium text-sm\"\r\n                        style={{\r\n                          borderBottom: reviewsTab === 'done' ? `2px solid ${colorPalette.primary}` : 'none',\r\n                          color: reviewsTab === 'done' ? colorPalette.primary : '#6b7280'\r\n                        }}\r\n                      >\r\n                        Done Reviews ({doneReviews.length})\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              {/* Analytics Section */}\r\n              {mainTab === 'analytics' && (\r\n                <div className=\"mb-6\">\r\n                  <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4\">\r\n                    <h2 className=\"text-xl font-bold\" style={{ color: colorPalette.primary }}>Analytics Dashboard</h2>\r\n                    <div className=\"flex gap-3\">\r\n                      <select\r\n                        value={analyticsTimeRange}\r\n                        onChange={(e) => handleAnalyticsTimeRangeChange(e.target.value)}\r\n                        className=\"px-4 py-2 border border-gray-300 rounded-lg\"\r\n                        style={{\r\n                          outline: 'none',\r\n                          boxShadow: 'none',\r\n                          borderColor: '#e5e7eb',\r\n                          ':focus': { borderColor: colorPalette.primary }\r\n                        }}\r\n                      >\r\n                        <option value=\"week\">Last Week</option>\r\n                        <option value=\"month\">Last Month</option>\r\n                        <option value=\"year\">Last Year</option>\r\n                        <option value=\"all\">All Time</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Debug Information - Only show in development */}\r\n                  {process.env.NODE_ENV === 'development' && (\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\"\r\n                    >\r\n                      <h3 className=\"text-sm font-semibold text-yellow-800 mb-2\">Debug Information</h3>\r\n                      <div className=\"text-xs text-yellow-700 space-y-1 mb-3\">\r\n                        <p>Time Range: {analyticsTimeRange}</p>\r\n                        <p>Total Reviews: {allReviews.length}</p>\r\n                        <p>Status Distribution: {JSON.stringify(analyticsData.statusDistribution)}</p>\r\n                        <p>Procedure Types: {Object.keys(analyticsData.procedureTypeDistribution).length}</p>\r\n                        <p>Students: {Object.keys(analyticsData.studentPerformance).length}</p>\r\n                        <p>Trends: {analyticsData.reviewTrends.length} months</p>\r\n                      </div>\r\n                      <div className=\"flex gap-2\">\r\n                        <button\r\n                          onClick={() => setAnalyticsData(generateSampleData())}\r\n                          className=\"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600\"\r\n                        >\r\n                          Load Sample Data\r\n                        </button>\r\n                        <button\r\n                          onClick={() => calculateAnalytics(allReviews)}\r\n                          className=\"px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600\"\r\n                        >\r\n                          Recalculate Analytics\r\n                        </button>\r\n                      </div>\r\n                    </motion.div>\r\n                  )}\r\n\r\n                  {/* Analytics Content */}\r\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\r\n                    {/* Status Distribution Chart */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.1 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Review Status Distribution</h3>\r\n                      <div className=\"h-64 flex items-center justify-center\">\r\n                        {analyticsData.statusDistribution && Object.values(analyticsData.statusDistribution).some(val => val > 0) ? (\r\n                          <Pie\r\n                            data={{\r\n                              labels: ['Accepted', 'Pending', 'Denied'],\r\n                              datasets: [\r\n                                {\r\n                                  data: [\r\n                                    analyticsData.statusDistribution.accepted || 0,\r\n                                    analyticsData.statusDistribution.pending || 0,\r\n                                    analyticsData.statusDistribution.denied || 0\r\n                                  ],\r\n                                  backgroundColor: [\r\n                                    `${colorPalette.accent}B3`,  // accent with opacity\r\n                                    `${colorPalette.primary}B3`, // primary with opacity\r\n                                    'rgba(239, 68, 68, 0.7)'     // red\r\n                                  ],\r\n                                  borderColor: [\r\n                                    colorPalette.accent,\r\n                                    colorPalette.primary,\r\n                                    'rgba(239, 68, 68, 1)'\r\n                                  ],\r\n                                  borderWidth: 1,\r\n                                }\r\n                              ]\r\n                            }}\r\n                            options={{\r\n                              responsive: true,\r\n                              maintainAspectRatio: false,\r\n                              plugins: {\r\n                                legend: {\r\n                                  position: 'bottom',\r\n                                  labels: {\r\n                                    usePointStyle: true,\r\n                                    padding: 20,\r\n                                    font: {\r\n                                      size: 12\r\n                                    }\r\n                                  }\r\n                                },\r\n                                tooltip: {\r\n                                  callbacks: {\r\n                                    label: function(context) {\r\n                                      const label = context.label || '';\r\n                                      const value = context.raw || 0;\r\n                                      const total = context.dataset.data.reduce((a, b) => a + b, 0);\r\n                                      const percentage = total > 0 ? Math.round((value / total) * 100) : 0;\r\n                                      return `${label}: ${value} (${percentage}%)`;\r\n                                    }\r\n                                  }\r\n                                }\r\n                              }\r\n                            }}\r\n                          />\r\n                        ) : (\r\n                          <div className=\"text-center text-gray-500\">\r\n                            <svg className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                            </svg>\r\n                            <p>No review data available</p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Procedure Type Distribution */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.2 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Procedure Type Distribution</h3>\r\n                      <div className=\"h-64 flex items-center justify-center\">\r\n                        {analyticsData.procedureTypeDistribution && Object.keys(analyticsData.procedureTypeDistribution).length > 0 ? (\r\n                          <Bar\r\n                            data={{\r\n                              labels: Object.keys(analyticsData.procedureTypeDistribution),\r\n                              datasets: [\r\n                                {\r\n                                  label: 'Accepted',\r\n                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.accepted || 0),\r\n                                  backgroundColor: `${colorPalette.accent}B3`,\r\n                                  borderColor: colorPalette.accent,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Pending',\r\n                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.pending || 0),\r\n                                  backgroundColor: `${colorPalette.primary}B3`,\r\n                                  borderColor: colorPalette.primary,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Denied',\r\n                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.denied || 0),\r\n                                  backgroundColor: 'rgba(239, 68, 68, 0.7)',\r\n                                  borderColor: 'rgba(239, 68, 68, 1)',\r\n                                  borderWidth: 1,\r\n                                }\r\n                              ]\r\n                            }}\r\n                            options={{\r\n                              responsive: true,\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                x: {\r\n                                  stacked: true,\r\n                                  ticks: {\r\n                                    maxRotation: 45,\r\n                                    minRotation: 0\r\n                                  }\r\n                                },\r\n                                y: {\r\n                                  stacked: true,\r\n                                  beginAtZero: true,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                }\r\n                              },\r\n                              plugins: {\r\n                                legend: {\r\n                                  position: 'bottom',\r\n                                  labels: {\r\n                                    usePointStyle: true,\r\n                                    padding: 20,\r\n                                    font: {\r\n                                      size: 12\r\n                                    }\r\n                                  }\r\n                                },\r\n                                tooltip: {\r\n                                  mode: 'index',\r\n                                  intersect: false\r\n                                }\r\n                              }\r\n                            }}\r\n                          />\r\n                        ) : (\r\n                          <div className=\"text-center text-gray-500\">\r\n                            <svg className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n                            </svg>\r\n                            <p>No procedure data available</p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Student Performance */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.3 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Student Performance</h3>\r\n                      <div className=\"overflow-x-auto\">\r\n                        <table className=\"min-w-full divide-y divide-gray-200\">\r\n                          <thead className=\"bg-gray-50\">\r\n                            <tr>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Student\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Reviews\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Acceptance Rate\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Avg. Quality\r\n                              </th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                Avg. Interaction\r\n                              </th>\r\n                            </tr>\r\n                          </thead>\r\n                          <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                            {analyticsData.studentPerformance && Object.keys(analyticsData.studentPerformance).length > 0 ? (\r\n                              Object.entries(analyticsData.studentPerformance).map(([student, data], index) => (\r\n                                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                    {student}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.total || 0}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.total > 0 ? `${Math.round((data.accepted / data.total) * 100)}%` : 'N/A'}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.avgProcedureQuality > 0 ? (\r\n                                      <div className=\"flex items-center\">\r\n                                        <span className=\"mr-2\">{data.avgProcedureQuality}</span>\r\n                                        {renderStars(parseFloat(data.avgProcedureQuality))}\r\n                                      </div>\r\n                                    ) : 'N/A'}\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                                    {data.avgPatientInteraction > 0 ? (\r\n                                      <div className=\"flex items-center\">\r\n                                        <span className=\"mr-2\">{data.avgPatientInteraction}</span>\r\n                                        {renderStars(parseFloat(data.avgPatientInteraction))}\r\n                                      </div>\r\n                                    ) : 'N/A'}\r\n                                  </td>\r\n                                </tr>\r\n                              ))\r\n                            ) : (\r\n                              <tr>\r\n                                <td colSpan=\"5\" className=\"px-6 py-4 text-center text-sm text-gray-500\">\r\n                                  <div className=\"flex flex-col items-center\">\r\n                                    <svg className=\"h-12 w-12 text-gray-300 mb-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\r\n                                    </svg>\r\n                                    No student performance data available\r\n                                  </div>\r\n                                </td>\r\n                              </tr>\r\n                            )}\r\n                          </tbody>\r\n                        </table>\r\n                      </div>\r\n                    </motion.div>\r\n\r\n                    {/* Review Trends */}\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: 0.4 }}\r\n                      className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\"\r\n                    >\r\n                      <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Review Trends</h3>\r\n                      <div className=\"h-64 flex items-center justify-center\">\r\n                        {analyticsData.reviewTrends && analyticsData.reviewTrends.length > 0 ? (\r\n                          <Bar\r\n                            data={{\r\n                              labels: analyticsData.reviewTrends.map(t => t.month),\r\n                              datasets: [\r\n                                {\r\n                                  label: 'Accepted',\r\n                                  data: analyticsData.reviewTrends.map(t => t.accepted || 0),\r\n                                  backgroundColor: `${colorPalette.accent}B3`,\r\n                                  borderColor: colorPalette.accent,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Pending',\r\n                                  data: analyticsData.reviewTrends.map(t => t.pending || 0),\r\n                                  backgroundColor: `${colorPalette.primary}B3`,\r\n                                  borderColor: colorPalette.primary,\r\n                                  borderWidth: 1,\r\n                                },\r\n                                {\r\n                                  label: 'Denied',\r\n                                  data: analyticsData.reviewTrends.map(t => t.denied || 0),\r\n                                  backgroundColor: 'rgba(239, 68, 68, 0.7)',\r\n                                  borderColor: 'rgba(239, 68, 68, 1)',\r\n                                  borderWidth: 1,\r\n                                }\r\n                              ]\r\n                            }}\r\n                            options={{\r\n                              responsive: true,\r\n                              maintainAspectRatio: false,\r\n                              scales: {\r\n                                x: {\r\n                                  stacked: true,\r\n                                  ticks: {\r\n                                    maxRotation: 45,\r\n                                    minRotation: 0\r\n                                  }\r\n                                },\r\n                                y: {\r\n                                  stacked: true,\r\n                                  beginAtZero: true,\r\n                                  ticks: {\r\n                                    stepSize: 1\r\n                                  }\r\n                                }\r\n                              },\r\n                              plugins: {\r\n                                legend: {\r\n                                  position: 'bottom',\r\n                                  labels: {\r\n                                    usePointStyle: true,\r\n                                    padding: 20,\r\n                                    font: {\r\n                                      size: 12\r\n                                    }\r\n                                  }\r\n                                },\r\n                                tooltip: {\r\n                                  mode: 'index',\r\n                                  intersect: false\r\n                                }\r\n                              }\r\n                            }}\r\n                          />\r\n                        ) : (\r\n                          <div className=\"text-center text-gray-500\">\r\n                            <svg className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\" />\r\n                            </svg>\r\n                            <p>No trend data available</p>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </motion.div>\r\n                  </div>\r\n\r\n                  {/* Quality Metrics Summary */}\r\n                  <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    transition={{ delay: 0.5 }}\r\n                    className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6\"\r\n                  >\r\n                    <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Quality Metrics Summary</h3>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                      <div className=\"p-4 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                        <h4 className=\"text-md font-medium mb-2\" style={{ color: colorPalette.primary }}>Average Procedure Quality</h4>\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"text-3xl font-bold mr-4\" style={{ color: colorPalette.primary }}>\r\n                            {analyticsData.qualityMetrics.avgProcedureQuality}/5\r\n                          </div>\r\n                          <div className=\"flex\">\r\n                            {renderStars(parseFloat(analyticsData.qualityMetrics.avgProcedureQuality))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"p-4 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                        <h4 className=\"text-md font-medium mb-2\" style={{ color: colorPalette.primary }}>Average Patient Interaction</h4>\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"text-3xl font-bold mr-4\" style={{ color: colorPalette.primary }}>\r\n                            {analyticsData.qualityMetrics.avgPatientInteraction}/5\r\n                          </div>\r\n                          <div className=\"flex\">\r\n                            {renderStars(parseFloat(analyticsData.qualityMetrics.avgPatientInteraction))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </motion.div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Reviews Table - Only show when in reviews tab */}\r\n              {mainTab === 'reviews' && (\r\n                <motion.div\r\n                  variants={container}\r\n                  initial=\"hidden\"\r\n                  whileInView=\"show\"\r\n                  viewport={{ once: true }}\r\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden\"\r\n                >\r\n                  <div className=\"p-6\">\r\n                    <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\r\n                      <h2 className=\"text-xl font-bold\" style={{ color: colorPalette.primary }}>\r\n                        {reviewsTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'}\r\n                      </h2>\r\n                      <div className=\"flex flex-col sm:flex-row gap-3 w-full sm:w-auto\">\r\n                        <div className=\"relative\">\r\n                          <input\r\n                            type=\"text\"\r\n                            placeholder=\"Search by patient or student name\"\r\n                            value={searchQuery}\r\n                            onChange={(e) => setSearchQuery(e.target.value)}\r\n                            className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          />\r\n                          <FaSearch className=\"absolute left-3 top-3 text-gray-400\" />\r\n                        </div>\r\n\r\n                        {/* Additional filters */}\r\n                        <div className=\"flex gap-2\">\r\n                          <button\r\n                            onClick={() => document.getElementById('filterDropdown').classList.toggle('hidden')}\r\n                            className=\"px-3 py-2 border border-gray-300 rounded-lg flex items-center\"\r\n                            style={{\r\n                              borderColor: '#e5e7eb',\r\n                              color: colorPalette.text,\r\n                              transition: 'all 0.2s ease'\r\n                            }}\r\n                            onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f9fafb')}\r\n                            onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}\r\n                          >\r\n                            <FaFilter className=\"mr-2 text-gray-500\" /> Filters\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Filter dropdown */}\r\n                    <div id=\"filterDropdown\" className=\"mb-6 p-4 border border-gray-200 rounded-lg hidden\" style={{ backgroundColor: `${colorPalette.primary}05` }}>\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Date Range</label>\r\n                          <select\r\n                            value={dayFilter}\r\n                            onChange={(e) => setDayFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"all\">All Dates</option>\r\n                            <option value=\"today\">Today</option>\r\n                            <option value=\"tomorrow\">Tomorrow</option>\r\n                            <option value=\"week\">This Week</option>\r\n                            <option value=\"month\">This Month</option>\r\n                          </select>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Time</label>\r\n                          <select\r\n                            value={hourFilter}\r\n                            onChange={(e) => setHourFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"\">All Hours</option>\r\n                            <option value=\"09:00\">09:00 AM</option>\r\n                            <option value=\"10:00\">10:00 AM</option>\r\n                            <option value=\"11:00\">11:00 AM</option>\r\n                            <option value=\"12:00\">12:00 PM</option>\r\n                            <option value=\"13:00\">01:00 PM</option>\r\n                            <option value=\"14:00\">02:00 PM</option>\r\n                            <option value=\"15:00\">03:00 PM</option>\r\n                          </select>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Procedure Type</label>\r\n                          <select\r\n                            value={procedureFilter}\r\n                            onChange={(e) => setProcedureFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"all\">All Procedures</option>\r\n                            {uniqueProcedures.map(procedure => (\r\n                              <option key={procedure} value={procedure}>{procedure}</option>\r\n                            ))}\r\n                          </select>\r\n                        </div>\r\n\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Student</label>\r\n                          <select\r\n                            value={studentFilter}\r\n                            onChange={(e) => setStudentFilter(e.target.value)}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                            style={{\r\n                              outline: 'none',\r\n                              boxShadow: 'none',\r\n                              borderColor: '#e5e7eb',\r\n                              ':focus': { borderColor: colorPalette.primary }\r\n                            }}\r\n                          >\r\n                            <option value=\"all\">All Students</option>\r\n                            {uniqueStudents.map(student => (\r\n                              <option key={student} value={student}>{student}</option>\r\n                            ))}\r\n                          </select>\r\n                        </div>\r\n\r\n                        {/* Status filter - only show for done reviews */}\r\n                        {reviewsTab === 'done' && (\r\n                          <div>\r\n                            <label className=\"block text-sm font-medium mb-1\" style={{ color: colorPalette.text }}>Status</label>\r\n                            <select\r\n                              value={statusFilter}\r\n                              onChange={(e) => setStatusFilter(e.target.value)}\r\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\r\n                              style={{\r\n                                outline: 'none',\r\n                                boxShadow: 'none',\r\n                                borderColor: '#e5e7eb',\r\n                                ':focus': { borderColor: colorPalette.primary }\r\n                              }}\r\n                            >\r\n                              <option value=\"all\">All Statuses</option>\r\n                              <option value=\"accepted\">Accepted</option>\r\n                              <option value=\"denied\">Denied</option>\r\n                            </select>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead style={{ backgroundColor: `${colorPalette.primary}05` }}>\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Date</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Patient</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Student</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Procedure</th>\r\n                          {reviewsTab === 'done' && (\r\n                            <>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Status</th>\r\n                              <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Supervisor</th>\r\n                            </>\r\n                          )}\r\n                          {reviewsTab === 'pending' && (\r\n                            <th className=\"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider\" style={{ color: colorPalette.primary }}>Actions</th>\r\n                          )}\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? (\r\n                          <tr>\r\n                            <td colSpan={reviewsTab === 'pending' ? 5 : 6} className=\"px-6 py-8 text-center\">\r\n                              <div className=\"flex flex-col items-center justify-center\">\r\n                                <svg className=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                                </svg>\r\n                                <h3 className=\"text-lg font-medium text-gray-900\">\r\n                                  No {reviewsTab === 'pending' ? 'pending' : 'completed'} reviews\r\n                                </h3>\r\n                                <p className=\"mt-1 text-gray-500\">\r\n                                  {error\r\n                                    ? 'Failed to load reviews due to an error. Check your permissions or try logging in again.'\r\n                                    : reviewsTab === 'pending'\r\n                                      ? 'No pending reviews are available. Check back later or ensure student reviews have been submitted.'\r\n                                      : 'No completed reviews found. Try adjusting the filters or review pending submissions.'}\r\n                                </p>\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ) : (\r\n                          filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).map((review) => (\r\n                            <motion.tr\r\n                              key={review._id}\r\n                              initial={{ opacity: 0 }}\r\n                              animate={{ opacity: 1 }}\r\n                              className=\"hover:bg-gray-50 cursor-pointer\"\r\n                              onClick={() => setSelectedReview(review)}\r\n                            >\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                {new Date(reviewsTab === 'pending' ? review.submittedDate : review.reviewedDate || review.submittedDate).toLocaleDateString()}\r\n                              </td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.patientId?.fullName || 'N/A'}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.studentName || 'N/A'}</td>\r\n                              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.procedureType || 'N/A'}</td>\r\n                              {reviewsTab === 'done' && (\r\n                                <>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                    <span\r\n                                      className=\"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n                                      style={{\r\n                                        backgroundColor: review.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\r\n                                        color: review.status === 'accepted' ? colorPalette.accent : '#b91c1c'\r\n                                      }}\r\n                                    >\r\n                                      {review.status}\r\n                                    </span>\r\n                                  </td>\r\n                                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">{review.supervisorName || 'N/A'}</td>\r\n                                </>\r\n                              )}\r\n                              {reviewsTab === 'pending' && (\r\n                                <td className=\"px-6 py-4 whitespace-nowrap text-sm\">\r\n                                  <button\r\n                                    onClick={() => setSelectedReview(review)}\r\n                                    style={{ color: colorPalette.primary }}\r\n                                    className=\"hover:underline\"\r\n                                  >\r\n                                    Review\r\n                                  </button>\r\n                                </td>\r\n                              )}\r\n                            </motion.tr>\r\n                          ))\r\n                        )}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n              )}\r\n            </motion.div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n\r\n      {selectedReview && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <motion.div\r\n            initial={{ scale: 0.9, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            className=\"bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6\">\r\n              <div className=\"flex justify-between items-center mb-6\">\r\n                <h2 className=\"text-2xl font-bold\" style={{ color: colorPalette.primary }}>Review Details</h2>\r\n                <button onClick={() => setSelectedReview(null)} className=\"text-gray-400 hover:text-gray-500\">\r\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"space-y-6\">\r\n                {/* Patient Information */}\r\n                <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center\" style={{ color: colorPalette.primary }}>\r\n                    <FaUserMd className=\"h-5 w-5 mr-2\" style={{ color: colorPalette.primary }} />\r\n                    Patient Information\r\n                  </h3>\r\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Name</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.patientId?.fullName || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.patientId?.nationalId || 'N/A'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Review Details */}\r\n                <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center\" style={{ color: colorPalette.primary }}>\r\n                    <FaCalendarAlt className=\"h-5 w-5 mr-2\" style={{ color: colorPalette.primary }} />\r\n                    Review Details\r\n                  </h3>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Student</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.studentName || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Student ID</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{typeof selectedReview.studentId === 'object' ? selectedReview.studentId.studentId : selectedReview.studentId || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Procedure</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.procedureType || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Submission Date</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">\r\n                        {selectedReview.submittedDate ? new Date(selectedReview.submittedDate).toLocaleString() : 'N/A'}\r\n                      </p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Student Comment</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.comment || 'No comment'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Review Steps */}\r\n                <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                  <h3 className=\"text-lg font-semibold mb-4 flex items-center\" style={{ color: colorPalette.primary }}>\r\n                    <FaListAlt className=\"h-5 w-5 mr-2\" style={{ color: colorPalette.primary }} />\r\n                    Procedure Steps\r\n                  </h3>\r\n                  <ReviewStepsDisplay\r\n                    reviewSteps={selectedReview.reviewSteps || []}\r\n                    procedureType={selectedReview.procedureType}\r\n                  />\r\n                </div>\r\n\r\n                {/* Supervisor Review Form (for pending reviews) */}\r\n                {selectedReview.status === 'pending' && (\r\n                  <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                    <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Submit Review</h3>\r\n                    <div className=\"space-y-4\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Procedure Quality</h4>\r\n                        {renderStars(reviewForm.procedureQuality, (rating) =>\r\n                          setReviewForm({ ...reviewForm, procedureQuality: rating })\r\n                        )}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Patient Interaction</h4>\r\n                        {renderStars(reviewForm.patientInteraction, (rating) =>\r\n                          setReviewForm({ ...reviewForm, patientInteraction: rating })\r\n                        )}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Comment</h4>\r\n                        <textarea\r\n                          value={reviewForm.comment}\r\n                          onChange={(e) => setReviewForm({ ...reviewForm, comment: e.target.value })}\r\n                          className=\"w-full px-4 py-2 border border-gray-300 rounded-lg\"\r\n                          style={{\r\n                            outline: 'none',\r\n                            boxShadow: 'none',\r\n                            borderColor: '#e5e7eb',\r\n                            ':focus': { borderColor: colorPalette.primary }\r\n                          }}\r\n                          rows=\"4\"\r\n                          placeholder=\"Add any comments or feedback\"\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Review Decision</h4>\r\n                        <div className=\"flex gap-4 mt-2\">\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => setReviewForm({ ...reviewForm, status: 'accepted' })}\r\n                            className=\"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\"\r\n                            style={{\r\n                              backgroundColor: reviewForm.status === 'accepted'\r\n                                ? colorPalette.accent\r\n                                : '#f3f4f6',\r\n                              color: reviewForm.status === 'accepted'\r\n                                ? '#ffffff'\r\n                                : colorPalette.text,\r\n                              fontWeight: reviewForm.status === 'accepted' ? '500' : 'normal',\r\n                              border: reviewForm.status === 'accepted'\r\n                                ? `2px solid ${colorPalette.accent}`\r\n                                : 'none'\r\n                            }}\r\n                          >\r\n                            <FaCheck className=\"mr-2\" style={{\r\n                              color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.accent\r\n                            }} />\r\n                            Accept\r\n                          </button>\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => setReviewForm({ ...reviewForm, status: 'denied' })}\r\n                            className=\"flex-1 py-3 px-4 rounded-lg flex items-center justify-center\"\r\n                            style={{\r\n                              backgroundColor: reviewForm.status === 'denied'\r\n                                ? '#ef4444'\r\n                                : '#f3f4f6',\r\n                              color: reviewForm.status === 'denied'\r\n                                ? '#ffffff'\r\n                                : colorPalette.text,\r\n                              fontWeight: reviewForm.status === 'denied' ? '500' : 'normal',\r\n                              border: reviewForm.status === 'denied'\r\n                                ? '2px solid #dc2626'\r\n                                : 'none'\r\n                            }}\r\n                          >\r\n                            <FaTimes className=\"mr-2\" style={{\r\n                              color: reviewForm.status === 'denied' ? '#ffffff' : '#ef4444'\r\n                            }} />\r\n                            Decline\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Signature Section */}\r\n                      <div className=\"mt-6 border-t pt-4 border-gray-200\">\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Signature</h4>\r\n\r\n                        {savedSignature ? (\r\n                          <div className=\"mb-4\">\r\n                            <div className=\"flex justify-between items-center mb-3\">\r\n                              <p className=\"text-sm text-gray-600\">You have a saved signature</p>\r\n                              <motion.button\r\n                                whileHover={{ scale: 1.05 }}\r\n                                whileTap={{ scale: 0.95 }}\r\n                                onClick={() => setReviewForm(prev => ({ ...prev, supervisorSignature: savedSignature }))}\r\n                                className=\"px-4 py-2 text-white rounded-lg flex items-center\"\r\n                                style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}\r\n                              >\r\n                                <FaSignature className=\"mr-2\" /> Sign\r\n                              </motion.button>\r\n                            </div>\r\n\r\n                            {reviewForm.supervisorSignature && (\r\n                              <div className=\"border rounded-lg p-4\"\r\n                                style={{\r\n                                  borderColor: '#e5e7eb',\r\n                                  backgroundColor: `${colorPalette.primary}05`\r\n                                }}>\r\n                                <h4 className=\"text-sm font-medium mb-2\" style={{ color: colorPalette.primary }}>Signature Preview</h4>\r\n                                {reviewForm.supervisorSignature.startsWith('data:image') ? (\r\n                                  <img src={reviewForm.supervisorSignature} alt=\"Signature\" className=\"max-h-16 mx-auto\" />\r\n                                ) : (\r\n                                  <p className=\"font-signature text-lg text-center\">{reviewForm.supervisorSignature}</p>\r\n                                )}\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"p-4 rounded-lg mb-4\"\r\n                            style={{\r\n                              backgroundColor: `${colorPalette.secondary}10`,\r\n                              border: `1px solid ${colorPalette.secondary}30`\r\n                            }}>\r\n                            <p className=\"text-sm mb-2\" style={{ color: colorPalette.secondary }}>\r\n                              You don't have a saved signature. Please create one first.\r\n                            </p>\r\n                            <motion.button\r\n                              whileHover={{ scale: 1.05 }}\r\n                              whileTap={{ scale: 0.95 }}\r\n                              onClick={() => setShowSignatureModal(true)}\r\n                              className=\"px-4 py-2 text-white rounded-lg flex items-center text-sm\"\r\n                              style={{ backgroundColor: colorPalette.secondary }}\r\n                            >\r\n                              <FaSignature className=\"mr-2\" /> Create Signature\r\n                            </motion.button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"flex justify-end gap-4 mt-6\">\r\n                        <motion.button\r\n                          whileHover={{ scale: 1.05 }}\r\n                          whileTap={{ scale: 0.95 }}\r\n                          onClick={() => handleReviewSubmit(selectedReview._id)}\r\n                          disabled={reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature}\r\n                          className=\"px-6 py-3 rounded-lg flex items-center shadow-md\"\r\n                          style={{\r\n                            backgroundColor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? '#d1d5db'\r\n                              : 'transparent',\r\n                            background: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? '#d1d5db'\r\n                              : reviewForm.status === 'accepted'\r\n                                ? `linear-gradient(to right, ${colorPalette.accent}, ${colorPalette.accent})`\r\n                                : 'linear-gradient(to right, #ef4444, #b91c1c)',\r\n                            color: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? '#6b7280'\r\n                              : '#ffffff',\r\n                            cursor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature\r\n                              ? 'not-allowed'\r\n                              : 'pointer'\r\n                          }}\r\n                        >\r\n                          {reviewForm.status === 'accepted' ? (\r\n                            <FaCheck className=\"h-5 w-5 mr-2\" />\r\n                          ) : (\r\n                            <FaTimes className=\"h-5 w-5 mr-2\" />\r\n                          )}\r\n                          Submit {reviewForm.status === 'accepted' ? 'Approval' : 'Decline'}\r\n                        </motion.button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Display Supervisor Review (for done reviews) */}\r\n                {selectedReview.status !== 'pending' && (\r\n                  <div className=\"p-6 rounded-lg\" style={{ backgroundColor: `${colorPalette.primary}10` }}>\r\n                    <h3 className=\"text-lg font-semibold mb-4\" style={{ color: colorPalette.primary }}>Supervisor Review</h3>\r\n                    <div className=\"space-y-4\">\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Supervisor</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.supervisorName || 'N/A'}</p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Reviewed Date</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">\r\n                          {selectedReview.reviewedDate ? new Date(selectedReview.reviewedDate).toLocaleString() : 'N/A'}\r\n                        </p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Procedure Quality</h4>\r\n                        {renderStars(selectedReview.procedureQuality || 0)}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Patient Interaction</h4>\r\n                        {renderStars(selectedReview.patientInteraction || 0)}\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Comment</h4>\r\n                        <p className=\"text-sm text-gray-900 mt-1\">{selectedReview.comment || 'No comment'}</p>\r\n                      </div>\r\n                      <div>\r\n                        <h4 className=\"text-sm font-medium text-gray-500\">Status</h4>\r\n                        <span\r\n                          className=\"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full\"\r\n                          style={{\r\n                            backgroundColor: selectedReview.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',\r\n                            color: selectedReview.status === 'accepted' ? colorPalette.accent : '#b91c1c'\r\n                          }}\r\n                        >\r\n                          {selectedReview.status}\r\n                        </span>\r\n                      </div>\r\n\r\n                      {/* Signature Display */}\r\n                      <div className=\"mt-4 pt-4 border-t border-gray-200\">\r\n                        <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Supervisor Signature</h4>\r\n                        {selectedReview.supervisorSignature ? (\r\n                          <div className=\"border rounded-lg p-4\" style={{\r\n                            borderColor: '#e5e7eb',\r\n                            backgroundColor: colorPalette.background\r\n                          }}>\r\n                            {selectedReview.supervisorSignature.startsWith('data:image') ? (\r\n                              <img src={selectedReview.supervisorSignature} alt=\"Signature\" className=\"max-h-20\" />\r\n                            ) : (\r\n                              <p className=\"font-signature text-lg\">{selectedReview.supervisorSignature}</p>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <p className=\"text-sm text-gray-500\">No signature provided</p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Signature Modal */}\r\n      {showSignatureModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n            className=\"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\"\r\n          >\r\n            <div className=\"p-6 rounded-t-xl\" style={{\r\n              background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`\r\n            }}>\r\n              <div className=\"flex justify-between items-center\">\r\n                <h2 className=\"text-xl font-bold text-white flex items-center\">\r\n                  <FaSignature className=\"mr-3 h-5 w-5\" />\r\n                  Manage Your Signature\r\n                </h2>\r\n                <button\r\n                  onClick={() => setShowSignatureModal(false)}\r\n                  className=\"text-white hover:text-blue-200 transition-colors\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-6\">\r\n              <div className=\"p-4 rounded-lg mb-6 border-l-4\"\r\n                style={{\r\n                  backgroundColor: `${colorPalette.primary}10`,\r\n                  borderColor: colorPalette.primary\r\n                }}>\r\n                <p style={{ color: colorPalette.primary }}>\r\n                  Create or update your signature below. This signature will be used for all your review approvals.\r\n                </p>\r\n              </div>\r\n\r\n              <SignatureManager\r\n                initialSignature={savedSignature}\r\n                onSignatureSelect={(signature) => {\r\n                  setSavedSignature(signature);\r\n                  // Close modal after a short delay to show success\r\n                  setTimeout(() => setShowSignatureModal(false), 1500);\r\n                }}\r\n              />\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Success Modal */}\r\n      {showSuccessModal && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -50 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: 50 }}\r\n          className=\"fixed top-0 inset-x-0 flex justify-center items-start z-50 pt-6 px-4\"\r\n        >\r\n          <div className=\"bg-white rounded-lg shadow-xl border-l-4 p-4 flex items-center max-w-md w-full\"\r\n            style={{ borderColor: colorPalette.accent }}>\r\n            <div className=\"p-2 rounded-full mr-4\" style={{ backgroundColor: `${colorPalette.accent}20` }}>\r\n              <FaCheck className=\"text-xl\" style={{ color: colorPalette.accent }} />\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <h3 className=\"font-medium\" style={{ color: colorPalette.text }}>Success!</h3>\r\n              <p style={{ color: '#6b7280' }}>{successMessage}</p>\r\n            </div>\r\n            <button\r\n              onClick={() => setShowSuccessModal(false)}\r\n              className=\"text-gray-400 hover:text-gray-500\"\r\n            >\r\n              <FaTimes />\r\n            </button>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SupervisorDashboard;"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EACjDC,UAAU,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAC3DC,UAAU,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,WAAW,EACzDC,eAAe,EAAEC,SAAS,EAAEC,aAAa,QACpC,gBAAgB;AACvB,SAASC,KAAK,IAAIC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,QAAQ,UAAU;AACvH,SAASC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;;AAE1C;AACA,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,kBAAkB,MAAM,sBAAsB;;AAErD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;;AAED;AACArB,OAAO,CAACsB,QAAQ,CAACrB,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,KAAK,CAAC;;AAE5F;AACAP,OAAO,CAACuB,QAAQ,CAACC,IAAI,CAACC,MAAM,GAAG,8BAA8B;AAC7DzB,OAAO,CAACuB,QAAQ,CAACC,IAAI,CAACE,IAAI,GAAG,EAAE;AAC/B1B,OAAO,CAACuB,QAAQ,CAACI,KAAK,GAAG,SAAS;AAClC3B,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACC,OAAO,CAACC,eAAe,GAAG,oBAAoB;AACvE9B,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACC,OAAO,CAACE,UAAU,GAAG,SAAS;AACvD/B,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACC,OAAO,CAACG,SAAS,GAAG,SAAS;AACtDhC,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACK,MAAM,CAACC,MAAM,CAACC,aAAa,GAAG,IAAI;AAC3DnC,OAAO,CAACuB,QAAQ,CAACK,OAAO,CAACK,MAAM,CAACC,MAAM,CAACE,OAAO,GAAG,EAAE;AAEnD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAChC,MAAMC,QAAQ,GAAGjE,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkE,IAAI;IAAEC;EAAM,CAAC,GAAGjE,OAAO,CAAC,CAAC;EACjC,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwE,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,aAAa,EAAEC,gBAAgB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC,SAAS,CAAC;EACjD,MAAM,CAACgG,UAAU,EAAEC,aAAa,CAAC,GAAGjG,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACkG,cAAc,EAAEC,iBAAiB,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwG,cAAc,EAAEC,iBAAiB,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3G,QAAQ,CAAC,OAAO,CAAC;EACrE,MAAM,CAAC4G,aAAa,EAAEC,gBAAgB,CAAC,GAAG7G,QAAQ,CAAC;IACjD8G,kBAAkB,EAAE;MAAEC,QAAQ,EAAE,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IAC1DC,yBAAyB,EAAE,CAAC,CAAC;IAC7BC,kBAAkB,EAAE,CAAC,CAAC;IACtBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE;MACdC,mBAAmB,EAAE,CAAC;MACtBC,qBAAqB,EAAE;IACzB;EACF,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzH,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC4H,UAAU,EAAEC,aAAa,CAAC,GAAG7H,QAAQ,CAAC;IAC3C8H,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,UAAU;IAClBC,mBAAmB,EAAE;EACvB,CAAC,CAAC;;EAIF;EACAjI,SAAS,CAAC,MAAM;IACd,MAAMkI,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QAAEjE,IAAI;QAAEC;MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACD,IAAI,IAAI,CAACC,KAAK,IAAID,IAAI,CAACkE,IAAI,KAAK,YAAY,EAAE;QACjD7D,QAAQ,CAAC,uDAAuD,CAAC;QACjEN,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEA,IAAI;QACFI,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMgE,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUpE,KAAK;UAAG;QAAE,CAAC;;QAEhE;QACA+D,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,MAAMK,aAAa,GAAG,MAAMvI,KAAK,CAACwI,GAAG,CAAC,8CAA8C,EAAEJ,MAAM,CAAC;QAC7FH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,aAAa,CAACE,IAAI,CAAC;QAExD,MAAMC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACL,aAAa,CAACE,IAAI,CAAC,GAAGF,aAAa,CAACE,IAAI,GAAG,EAAE;QAC3E7D,aAAa,CAAC8D,OAAO,CAAC;;QAEtB;QACAlE,iBAAiB,CAACkE,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC,CAAC;QAC9DpD,cAAc,CAACgE,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC,CAAC;;QAE3D;QACA,MAAMiB,QAAQ,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACN,OAAO,CAACO,GAAG,CAACH,CAAC;UAAA,IAAAI,YAAA;UAAA,OAAI,EAAAA,YAAA,GAAAJ,CAAC,CAACK,SAAS,cAAAD,YAAA,uBAAXA,YAAA,CAAaE,IAAI,KAAIN,CAAC,CAACO,WAAW;QAAA,EAAC,CAAC,CAAC,CAACR,MAAM,CAACS,OAAO,CAAC;QACnG,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAIP,GAAG,CAACN,OAAO,CAACO,GAAG,CAACH,CAAC,IAAIA,CAAC,CAACU,aAAa,CAAC,CAAC,CAAC,CAACX,MAAM,CAACS,OAAO,CAAC;QAElFhC,iBAAiB,CAACyB,QAAQ,CAAC;QAC3BvB,mBAAmB,CAAC+B,UAAU,CAAC;;QAE/B;QACA,IAAI;UACF,MAAME,YAAY,GAAG,MAAMzJ,KAAK,CAACwI,GAAG,CAAC,iDAAiD,EAAEJ,MAAM,CAAC;UAC/F,IAAIqB,YAAY,CAAChB,IAAI,IAAIgB,YAAY,CAAChB,IAAI,CAACiB,SAAS,EAAE;YACpD1D,iBAAiB,CAACyD,YAAY,CAAChB,IAAI,CAACiB,SAAS,CAAC;UAChD;QACF,CAAC,CAAC,OAAOC,YAAY,EAAE;UACrB1B,OAAO,CAAC5D,KAAK,CAAC,2BAA2B,EAAEsF,YAAY,CAAC;UACxD;QACF;;QAEA;QACA,IAAI;UACF1B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE3B,kBAAkB,CAAC;UAC1E,MAAMqD,YAAY,GAAG,MAAM5J,KAAK,CAACwI,GAAG,CAAC,yDAAyDjC,kBAAkB,EAAE,EAAE6B,MAAM,CAAC;UAC3HH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0B,YAAY,CAACnB,IAAI,CAAC;UAErD,IAAImB,YAAY,CAACnB,IAAI,EAAE;YAAA,IAAAoB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YACrB;YACA,MAAMxD,aAAa,GAAG;cACpBE,kBAAkB,EAAE;gBAClBC,QAAQ,EAAE,EAAAiD,qBAAA,GAAAD,YAAY,CAACnB,IAAI,CAAC9B,kBAAkB,cAAAkD,qBAAA,uBAApCA,qBAAA,CAAsCjD,QAAQ,KAAI,CAAC;gBAC7DC,OAAO,EAAE,EAAAiD,sBAAA,GAAAF,YAAY,CAACnB,IAAI,CAAC9B,kBAAkB,cAAAmD,sBAAA,uBAApCA,sBAAA,CAAsCjD,OAAO,KAAI,CAAC;gBAC3DC,MAAM,EAAE,EAAAiD,sBAAA,GAAAH,YAAY,CAACnB,IAAI,CAAC9B,kBAAkB,cAAAoD,sBAAA,uBAApCA,sBAAA,CAAsCjD,MAAM,KAAI;cAC1D,CAAC;cACDC,yBAAyB,EAAE6C,YAAY,CAACnB,IAAI,CAAC1B,yBAAyB,IAAI,CAAC,CAAC;cAC5EC,kBAAkB,EAAE4C,YAAY,CAACnB,IAAI,CAACzB,kBAAkB,IAAI,CAAC,CAAC;cAC9DC,YAAY,EAAE2C,YAAY,CAACnB,IAAI,CAACxB,YAAY,IAAI,EAAE;cAClDC,cAAc,EAAE;gBACdC,mBAAmB,EAAE,EAAA6C,qBAAA,GAAAJ,YAAY,CAACnB,IAAI,CAACvB,cAAc,cAAA8C,qBAAA,uBAAhCA,qBAAA,CAAkC7C,mBAAmB,KAAI,CAAC;gBAC/EC,qBAAqB,EAAE,EAAA6C,sBAAA,GAAAL,YAAY,CAACnB,IAAI,CAACvB,cAAc,cAAA+C,sBAAA,uBAAhCA,sBAAA,CAAkC7C,qBAAqB,KAAI;cACpF;YACF,CAAC;YACDV,gBAAgB,CAACD,aAAa,CAAC;UACjC;QACF,CAAC,CAAC,OAAOyD,YAAY,EAAE;UACrBjC,OAAO,CAAC5D,KAAK,CAAC,2BAA2B,EAAE6F,YAAY,CAAC;UACxDjC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;UACtD;UACA,IAAIQ,OAAO,CAACyB,MAAM,GAAG,CAAC,EAAE;YACtBC,kBAAkB,CAAC1B,OAAO,CAAC;UAC7B,CAAC,MAAM;YACL;YACAT,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;YAC3DxB,gBAAgB,CAAC2D,kBAAkB,CAAC,CAAC,CAAC;UACxC;QACF;QAEAjG,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOkG,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,cAAA;QACZ5C,OAAO,CAAC5D,KAAK,CAAC,cAAc,EAAE;UAC5ByG,OAAO,EAAER,GAAG,CAACQ,OAAO;UACpBhD,MAAM,GAAAyC,aAAA,GAAED,GAAG,CAACS,QAAQ,cAAAR,aAAA,uBAAZA,aAAA,CAAczC,MAAM;UAC5BW,IAAI,GAAA+B,cAAA,GAAEF,GAAG,CAACS,QAAQ,cAAAP,cAAA,uBAAZA,cAAA,CAAc/B,IAAI;UACxBJ,OAAO,GAAAoC,cAAA,GAAEH,GAAG,CAACS,QAAQ,cAAAN,cAAA,uBAAZA,cAAA,CAAcpC;QACzB,CAAC,CAAC;QACF,MAAM2C,YAAY,GAAG,EAAAN,cAAA,GAAAJ,GAAG,CAACS,QAAQ,cAAAL,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoBG,OAAO,MAC3B,EAAAF,cAAA,GAAAN,GAAG,CAACS,QAAQ,cAAAH,cAAA,uBAAZA,cAAA,CAAc9C,MAAM,MAAK,GAAG,GAAG,yCAAyC,GACzE,2CAA2C,CAAC;QAChExD,QAAQ,CAAC0G,YAAY,CAAC;QACtB,IAAI,EAAAH,cAAA,GAAAP,GAAG,CAACS,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAc/C,MAAM,MAAK,GAAG,EAAE;UAChCmD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;UAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;UAC/BlH,QAAQ,CAAC,QAAQ,CAAC;QACpB;QACAQ,iBAAiB,CAAC,EAAE,CAAC;QACrBE,cAAc,CAAC,EAAE,CAAC;QAClBE,aAAa,CAAC,EAAE,CAAC;QACjBR,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACD4D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC/D,IAAI,EAAEC,KAAK,EAAEF,QAAQ,EAAEuC,kBAAkB,CAAC,CAAC;;EAE/C;EACA,MAAM4E,8BAA8B,GAAIC,QAAQ,IAAK;IACnD5E,qBAAqB,CAAC4E,QAAQ,CAAC;EACjC,CAAC;;EAED;EACA,MAAMf,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,OAAO;MACL1D,kBAAkB,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;MAC3DC,yBAAyB,EAAE;QACzB,qBAAqB,EAAE;UAAEsE,KAAK,EAAE,EAAE;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;QACxE,aAAa,EAAE;UAAEwE,KAAK,EAAE,CAAC;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;QAC/D,cAAc,EAAE;UAAEwE,KAAK,EAAE,CAAC;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;QAChE,sBAAsB,EAAE;UAAEwE,KAAK,EAAE,CAAC;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE;MACzE,CAAC;MACDG,kBAAkB,EAAE;QAClB,UAAU,EAAE;UAAEqE,KAAK,EAAE,CAAC;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE,CAAC;UAAEM,mBAAmB,EAAE,GAAG;UAAEC,qBAAqB,EAAE;QAAI,CAAC;QAClH,YAAY,EAAE;UAAEiE,KAAK,EAAE,CAAC;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE,CAAC;UAAEM,mBAAmB,EAAE,GAAG;UAAEC,qBAAqB,EAAE;QAAI,CAAC;QACpH,cAAc,EAAE;UAAEiE,KAAK,EAAE,CAAC;UAAEzE,QAAQ,EAAE,CAAC;UAAEE,MAAM,EAAE,CAAC;UAAED,OAAO,EAAE,CAAC;UAAEM,mBAAmB,EAAE,GAAG;UAAEC,qBAAqB,EAAE;QAAI;MACvH,CAAC;MACDH,YAAY,EAAE,CACZ;QAAEqE,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAEzE,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,EACjE;QAAEyE,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAEzE,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,EACjE;QAAEyE,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAEzE,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,EACjE;QAAEyE,KAAK,EAAE,QAAQ;QAAED,KAAK,EAAE,CAAC;QAAEzE,QAAQ,EAAE,CAAC;QAAEE,MAAM,EAAE,CAAC;QAAED,OAAO,EAAE;MAAE,CAAC,CAClE;MACDK,cAAc,EAAE;QACdC,mBAAmB,EAAE,GAAG;QACxBC,qBAAqB,EAAE;MACzB;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMgD,kBAAkB,GAAI1B,OAAO,IAAK;IACtC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAIA,OAAO,CAACyB,MAAM,KAAK,CAAC,EAAE;MACnDlC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,OAAO,CAACyB,MAAM,EAAE,SAAS,CAAC;;IAEnE;IACA,MAAMtD,OAAO,GAAG6B,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC,CAACqC,MAAM;IAClE,MAAMvD,QAAQ,GAAG8B,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,UAAU,CAAC,CAACqC,MAAM;IACpE,MAAMrD,MAAM,GAAG4B,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,QAAQ,CAAC,CAACqC,MAAM;IAEhElC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAErB,OAAO;MAAED,QAAQ;MAAEE;IAAO,CAAC,CAAC;;IAElE;IACA,MAAMyE,cAAc,GAAG,CAAC,CAAC;IACzB7C,OAAO,CAAC8C,OAAO,CAACC,MAAM,IAAI;MACxB,MAAMC,IAAI,GAAGD,MAAM,CAACjC,aAAa,IAAI,SAAS;MAC9C,IAAI,CAAC+B,cAAc,CAACG,IAAI,CAAC,EAAE;QACzBH,cAAc,CAACG,IAAI,CAAC,GAAG;UACrBL,KAAK,EAAE,CAAC;UACRzE,QAAQ,EAAE,CAAC;UACXE,MAAM,EAAE,CAAC;UACTD,OAAO,EAAE;QACX,CAAC;MACH;MACA0E,cAAc,CAACG,IAAI,CAAC,CAACL,KAAK,EAAE;MAC5B,IAAII,MAAM,CAAC3D,MAAM,KAAK,UAAU,EAAEyD,cAAc,CAACG,IAAI,CAAC,CAAC9E,QAAQ,EAAE,CAAC,KAC7D,IAAI6E,MAAM,CAAC3D,MAAM,KAAK,QAAQ,EAAEyD,cAAc,CAACG,IAAI,CAAC,CAAC5E,MAAM,EAAE,CAAC,KAC9DyE,cAAc,CAACG,IAAI,CAAC,CAAC7E,OAAO,EAAE;IACrC,CAAC,CAAC;IAEFoB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyD,MAAM,CAACC,IAAI,CAACL,cAAc,CAAC,CAAC;;IAE5D;IACA,MAAMvE,kBAAkB,GAAG,CAAC,CAAC;IAC7B0B,OAAO,CAAC8C,OAAO,CAACC,MAAM,IAAI;MAAA,IAAAI,iBAAA;MACxB,MAAMxC,WAAW,GAAGoC,MAAM,CAACpC,WAAW,MAAAwC,iBAAA,GAAIJ,MAAM,CAACtC,SAAS,cAAA0C,iBAAA,uBAAhBA,iBAAA,CAAkBzC,IAAI,KAAI,SAAS;MAC7E,IAAI,CAACpC,kBAAkB,CAACqC,WAAW,CAAC,EAAE;QACpCrC,kBAAkB,CAACqC,WAAW,CAAC,GAAG;UAChCgC,KAAK,EAAE,CAAC;UACRzE,QAAQ,EAAE,CAAC;UACXE,MAAM,EAAE,CAAC;UACTD,OAAO,EAAE,CAAC;UACVM,mBAAmB,EAAE,CAAC;UACtBC,qBAAqB,EAAE,CAAC;UACxB0E,cAAc,EAAE,EAAE;UAClBC,kBAAkB,EAAE;QACtB,CAAC;MACH;MAEA/E,kBAAkB,CAACqC,WAAW,CAAC,CAACgC,KAAK,EAAE;MACvC,IAAII,MAAM,CAAC3D,MAAM,KAAK,UAAU,EAAEd,kBAAkB,CAACqC,WAAW,CAAC,CAACzC,QAAQ,EAAE,CAAC,KACxE,IAAI6E,MAAM,CAAC3D,MAAM,KAAK,QAAQ,EAAEd,kBAAkB,CAACqC,WAAW,CAAC,CAACvC,MAAM,EAAE,CAAC,KACzEE,kBAAkB,CAACqC,WAAW,CAAC,CAACxC,OAAO,EAAE;MAE9C,IAAI4E,MAAM,CAAC9D,gBAAgB,EAAE;QAC3BX,kBAAkB,CAACqC,WAAW,CAAC,CAACyC,cAAc,CAACE,IAAI,CAACP,MAAM,CAAC9D,gBAAgB,CAAC;MAC9E;MAEA,IAAI8D,MAAM,CAAC7D,kBAAkB,EAAE;QAC7BZ,kBAAkB,CAACqC,WAAW,CAAC,CAAC0C,kBAAkB,CAACC,IAAI,CAACP,MAAM,CAAC7D,kBAAkB,CAAC;MACpF;IACF,CAAC,CAAC;;IAEF;IACA+D,MAAM,CAACC,IAAI,CAAC5E,kBAAkB,CAAC,CAACwE,OAAO,CAACS,OAAO,IAAI;MACjD,MAAM;QAAEH,cAAc;QAAEC;MAAmB,CAAC,GAAG/E,kBAAkB,CAACiF,OAAO,CAAC;MAE1E,IAAIH,cAAc,CAAC3B,MAAM,GAAG,CAAC,EAAE;QAC7BnD,kBAAkB,CAACiF,OAAO,CAAC,CAAC9E,mBAAmB,GAC7C,CAAC2E,cAAc,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,EAAE,CAAC,CAAC,GAAGN,cAAc,CAAC3B,MAAM,EAAEkC,OAAO,CAAC,CAAC,CAAC;MAChG;MAEA,IAAIN,kBAAkB,CAAC5B,MAAM,GAAG,CAAC,EAAE;QACjCnD,kBAAkB,CAACiF,OAAO,CAAC,CAAC7E,qBAAqB,GAC/C,CAAC2E,kBAAkB,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,EAAE,CAAC,CAAC,GAAGL,kBAAkB,CAAC5B,MAAM,EAAEkC,OAAO,CAAC,CAAC,CAAC;MACxG;;MAEA;MACA,OAAOrF,kBAAkB,CAACiF,OAAO,CAAC,CAACH,cAAc;MACjD,OAAO9E,kBAAkB,CAACiF,OAAO,CAAC,CAACF,kBAAkB;IACvD,CAAC,CAAC;IAEF9D,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyD,MAAM,CAACC,IAAI,CAAC5E,kBAAkB,CAAC,CAACmD,MAAM,EAAE,UAAU,CAAC;;IAErG;IACA,MAAMmC,cAAc,GAAG,CAAC,CAAC;IACzB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,YAAY,GAAG,IAAID,IAAI,CAAC,CAAC;IAC/BC,YAAY,CAACC,QAAQ,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IAEzCjE,OAAO,CAAC8C,OAAO,CAACC,MAAM,IAAI;MACxB,MAAMmB,IAAI,GAAG,IAAIJ,IAAI,CAACf,MAAM,CAACoB,aAAa,CAAC;MAC3C,IAAID,IAAI,IAAIH,YAAY,EAAE;QACxB,MAAMK,SAAS,GAAG,GAAGF,IAAI,CAACD,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAIC,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE;QAChE,IAAI,CAACT,cAAc,CAACQ,SAAS,CAAC,EAAE;UAC9BR,cAAc,CAACQ,SAAS,CAAC,GAAG;YAAEzB,KAAK,EAAE,CAAC;YAAEzE,QAAQ,EAAE,CAAC;YAAEE,MAAM,EAAE,CAAC;YAAED,OAAO,EAAE;UAAE,CAAC;QAC9E;QACAyF,cAAc,CAACQ,SAAS,CAAC,CAACzB,KAAK,EAAE;QACjC,IAAII,MAAM,CAAC3D,MAAM,KAAK,UAAU,EAAEwE,cAAc,CAACQ,SAAS,CAAC,CAAClG,QAAQ,EAAE,CAAC,KAClE,IAAI6E,MAAM,CAAC3D,MAAM,KAAK,QAAQ,EAAEwE,cAAc,CAACQ,SAAS,CAAC,CAAChG,MAAM,EAAE,CAAC,KACnEwF,cAAc,CAACQ,SAAS,CAAC,CAACjG,OAAO,EAAE;MAC1C;IACF,CAAC,CAAC;;IAEF;IACA,MAAMI,YAAY,GAAG0E,MAAM,CAACC,IAAI,CAACU,cAAc,CAAC,CAACrD,GAAG,CAACqC,KAAK,KAAK;MAC7DA,KAAK;MACL,GAAGgB,cAAc,CAAChB,KAAK;IACzB,CAAC,CAAC,CAAC,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACjB,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGH,CAAC,CAAC3B,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAACpE,GAAG,CAACqE,MAAM,CAAC;MACtD,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC,GAAGN,CAAC,CAAC5B,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAACpE,GAAG,CAACqE,MAAM,CAAC;MACtD,OAAOF,KAAK,KAAKI,KAAK,GAAGL,MAAM,GAAGI,MAAM,GAAGH,KAAK,GAAGI,KAAK;IAC1D,CAAC,CAAC;IAEFvF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEjB,YAAY,CAACkD,MAAM,EAAE,QAAQ,CAAC;;IAE1E;IACA,MAAMsD,cAAc,GAAG/E,OAAO,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,SAAS,CAAC;IAClE,MAAMX,mBAAmB,GAAGsG,cAAc,CAACtD,MAAM,GAAG,CAAC,GACjD,CAACsD,cAAc,CAACvB,MAAM,CAAC,CAACC,GAAG,EAAErD,CAAC,KAAKqD,GAAG,IAAIrD,CAAC,CAACnB,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG8F,cAAc,CAACtD,MAAM,EAAEkC,OAAO,CAAC,CAAC,CAAC,GAC1G,CAAC;IACL,MAAMjF,qBAAqB,GAAGqG,cAAc,CAACtD,MAAM,GAAG,CAAC,GACnD,CAACsD,cAAc,CAACvB,MAAM,CAAC,CAACC,GAAG,EAAErD,CAAC,KAAKqD,GAAG,IAAIrD,CAAC,CAAClB,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG6F,cAAc,CAACtD,MAAM,EAAEkC,OAAO,CAAC,CAAC,CAAC,GAC5G,CAAC;IAELpE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAAEf,mBAAmB;MAAEC;IAAsB,CAAC,CAAC;;IAE/E;IACA,MAAMsG,gBAAgB,GAAG;MACvB/G,kBAAkB,EAAE;QAAEC,QAAQ;QAAEC,OAAO;QAAEC;MAAO,CAAC;MACjDC,yBAAyB,EAAEwE,cAAc;MACzCvE,kBAAkB;MAClBC,YAAY;MACZC,cAAc,EAAE;QACdC,mBAAmB;QACnBC;MACF;IACF,CAAC;IAEDa,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEwF,gBAAgB,CAAC;IACxDhH,gBAAgB,CAACgH,gBAAgB,CAAC;EACpC,CAAC;EAID,MAAMC,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAInG,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,EAAE;MAC5EtD,QAAQ,CAAC,sEAAsE,CAAC;MAChF;IACF;IAEA,IAAI,CAACmD,UAAU,CAACM,mBAAmB,EAAE;MACnCzD,QAAQ,CAAC,sDAAsD,CAAC;MAChE;IACF;IAEA,IAAI;MACF,MAAM8D,MAAM,GAAG;QAAEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUpE,KAAK;QAAG;MAAE,CAAC;MAChE,MAAM2J,aAAa,GAAG;QACpB/F,MAAM,EAAEL,UAAU,CAACK,MAAM;QACzBH,gBAAgB,EAAEF,UAAU,CAACE,gBAAgB;QAC7CC,kBAAkB,EAAEH,UAAU,CAACG,kBAAkB;QACjDC,OAAO,EAAEJ,UAAU,CAACI,OAAO,IAAI,EAAE;QACjCE,mBAAmB,EAAEN,UAAU,CAACM;MAClC,CAAC;MAEDE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QAAE0F,QAAQ;QAAE,GAAGC,aAAa;QAAE9F,mBAAmB,EAAE;MAAyB,CAAC,CAAC;MACvH,MAAMgD,QAAQ,GAAG,MAAM/K,KAAK,CAAC8N,GAAG,CAAC,qCAAqCF,QAAQ,EAAE,EAAEC,aAAa,EAAEzF,MAAM,CAAC;MACxGH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6C,QAAQ,CAACtC,IAAI,CAAC;;MAE7C;MACAzC,iBAAiB,CAACyB,UAAU,CAACM,mBAAmB,CAAC;;MAEjD;MACA,MAAMgG,YAAY,GAAGxJ,cAAc,CAACyJ,IAAI,CAAClF,CAAC,IAAIA,CAAC,CAACmF,GAAG,KAAKL,QAAQ,CAAC;;MAEjE;MACA,MAAMM,2BAA2B,GAAG;QAClC,GAAGL,aAAa;QAChBM,cAAc,EAAE,CAAAlK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,IAAI,KAAI,oBAAoB;QAClDgF,YAAY,EAAE,IAAI5B,IAAI,CAAC;MACzB,CAAC;MAEDhI,iBAAiB,CAAC6J,IAAI,IAAIA,IAAI,CAACxF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACmF,GAAG,KAAKL,QAAQ,CAAC,CAAC;MAC/DlJ,cAAc,CAAC2J,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAGN,YAAY;QAAE,GAAGG;MAA4B,CAAC,CAAC,CAAC;;MAEtF;MACA,MAAMI,iBAAiB,GAAG,CACxB,GAAG7J,WAAW,EACd;QAAE,GAAGsJ,YAAY;QAAE,GAAGG;MAA4B,CAAC,EACnD,GAAG3J,cAAc,CAACsE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACmF,GAAG,KAAKL,QAAQ,CAAC,CAClD;MACDxD,kBAAkB,CAACkE,iBAAiB,CAAC;;MAErC;MACAxJ,iBAAiB,CAAC,IAAI,CAAC;MACvB4C,aAAa,CAAC;QACZC,gBAAgB,EAAE,CAAC;QACnBC,kBAAkB,EAAE,CAAC;QACrBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE,UAAU;QAClBC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACFzD,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACAgC,iBAAiB,CAAC,UAAUmB,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,UAAU,GAAG,UAAU,gBAAgB,CAAC;MACvG1B,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACAmI,UAAU,CAAC,MAAM;QACfnI,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOkE,GAAG,EAAE;MAAA,IAAAkE,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;MACZ1G,OAAO,CAAC5D,KAAK,CAAC,sBAAsB,EAAE;QACpCyG,OAAO,EAAER,GAAG,CAACQ,OAAO;QACpBhD,MAAM,GAAA0G,cAAA,GAAElE,GAAG,CAACS,QAAQ,cAAAyD,cAAA,uBAAZA,cAAA,CAAc1G,MAAM;QAC5BW,IAAI,GAAAgG,cAAA,GAAEnE,GAAG,CAACS,QAAQ,cAAA0D,cAAA,uBAAZA,cAAA,CAAchG;MACtB,CAAC,CAAC;MACFnE,QAAQ,CAAC,EAAAoK,cAAA,GAAApE,GAAG,CAACS,QAAQ,cAAA2D,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjG,IAAI,cAAAkG,mBAAA,uBAAlBA,mBAAA,CAAoB7D,OAAO,KAAI,yBAAyB,CAAC;IACpE;EACF,CAAC;EAED,MAAM8D,aAAa,GAAIlG,OAAO,IAAK;IACjC,IAAImG,QAAQ,GAAG,CAAC,GAAGnG,OAAO,CAAC;;IAE3B;IACA,MAAMoG,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC;IACxBsC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,IAAIhK,SAAS,KAAK,OAAO,EAAE;MACzB8J,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMkG,UAAU,GAAG,IAAIxC,IAAI,CAAC1D,CAAC,CAAC+D,aAAa,CAAC;QAC5C,OAAOmC,UAAU,CAACC,YAAY,CAAC,CAAC,KAAKH,KAAK,CAACG,YAAY,CAAC,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlK,SAAS,KAAK,UAAU,EAAE;MACnC,MAAMmK,QAAQ,GAAG,IAAI1C,IAAI,CAACsC,KAAK,CAAC;MAChCI,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACxCP,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMkG,UAAU,GAAG,IAAIxC,IAAI,CAAC1D,CAAC,CAAC+D,aAAa,CAAC;QAC5C,OAAOmC,UAAU,CAACC,YAAY,CAAC,CAAC,KAAKC,QAAQ,CAACD,YAAY,CAAC,CAAC;MAC9D,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlK,SAAS,KAAK,MAAM,EAAE;MAC/B,MAAMsK,OAAO,GAAG,IAAI7C,IAAI,CAACsC,KAAK,CAAC;MAC/BO,OAAO,CAACF,OAAO,CAACE,OAAO,CAACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACtCP,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMkG,UAAU,GAAG,IAAIxC,IAAI,CAAC1D,CAAC,CAAC+D,aAAa,CAAC;QAC5C,OAAOmC,UAAU,IAAIF,KAAK,IAAIE,UAAU,IAAIK,OAAO;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAItK,SAAS,KAAK,OAAO,EAAE;MAChC,MAAMuK,UAAU,GAAG,IAAI9C,IAAI,CAACsC,KAAK,CAAC/B,WAAW,CAAC,CAAC,EAAE+B,KAAK,CAACnC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,MAAM4C,SAAS,GAAG,IAAI/C,IAAI,CAACsC,KAAK,CAAC/B,WAAW,CAAC,CAAC,EAAE+B,KAAK,CAACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACxEkC,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMkG,UAAU,GAAG,IAAIxC,IAAI,CAAC1D,CAAC,CAAC+D,aAAa,CAAC;QAC5C,OAAOmC,UAAU,IAAIM,UAAU,IAAIN,UAAU,GAAGO,SAAS;MAC3D,CAAC,CAAC;IACJ;;IAEA;IACA,IAAItK,UAAU,EAAE;MACd4J,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAI;QAC9B,MAAMkG,UAAU,GAAG,IAAIxC,IAAI,CAAC1D,CAAC,CAAC+D,aAAa,CAAC;QAC5C,MAAM2C,KAAK,GAAGR,UAAU,CAACS,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC/D,OAAO,GAAGH,KAAK,KAAK,KAAKvK,UAAU;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIE,WAAW,EAAE;MACf0J,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC;QAAA,IAAA8G,YAAA,EAAAC,qBAAA,EAAAC,cAAA;QAAA,OAC1B,EAAAF,YAAA,GAAC9G,CAAC,CAACiH,SAAS,cAAAH,YAAA,wBAAAC,qBAAA,GAAXD,YAAA,CAAaI,QAAQ,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/K,WAAW,CAAC8K,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GACxEhH,CAAC,CAACO,WAAW,cAAAyG,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/K,WAAW,CAAC8K,WAAW,CAAC,CAAC,CAAC,CAAC;MAAA,CACpE,CAAC;IACH;;IAEA;IACA,IAAI5K,eAAe,IAAIA,eAAe,KAAK,KAAK,EAAE;MAChDwJ,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACU,aAAa,KAAKnE,eAAe,CAAC;IACtE;;IAEA;IACA,IAAIE,aAAa,IAAIA,aAAa,KAAK,KAAK,EAAE;MAC5CsJ,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACO,WAAW,KAAK9D,aAAa,CAAC;IAClE;;IAEA;IACA,IAAIM,UAAU,KAAK,MAAM,IAAIJ,YAAY,IAAIA,YAAY,KAAK,KAAK,EAAE;MACnEoJ,QAAQ,GAAGA,QAAQ,CAAChG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAKrC,YAAY,CAAC;IAC5D;;IAEA;IACA,OAAOoJ,QAAQ,CAAC7B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC7B,MAAMiD,KAAK,GAAG,IAAI3D,IAAI,CAAC3G,UAAU,KAAK,SAAS,GAAGoH,CAAC,CAACJ,aAAa,GAAGI,CAAC,CAACmB,YAAY,IAAInB,CAAC,CAACJ,aAAa,CAAC;MACtG,MAAMuD,KAAK,GAAG,IAAI5D,IAAI,CAAC3G,UAAU,KAAK,SAAS,GAAGqH,CAAC,CAACL,aAAa,GAAGK,CAAC,CAACkB,YAAY,IAAIlB,CAAC,CAACL,aAAa,CAAC;MACtG,OAAOuD,KAAK,GAAGD,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,YAAY,GAAG9L,cAAc,CAAC4F,MAAM,GAAG1F,WAAW,CAAC0F,MAAM;EAC/D,MAAMmG,eAAe,GAAG7L,WAAW,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,UAAU,CAAC,CAACqC,MAAM;EAC/E,MAAMoG,aAAa,GAAG9L,WAAW,CAACoE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChB,MAAM,KAAK,QAAQ,CAAC,CAACqC,MAAM;EAC3E,MAAMqG,cAAc,GAAGH,YAAY,GAAG,CAAC,GAAG,CAAEC,eAAe,GAAGD,YAAY,GAAI,GAAG,EAAEhE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACjG,MAAMoE,UAAU,GAAGJ,YAAY,GAAG,CAAC,GAAG,CAAEE,aAAa,GAAGF,YAAY,GAAI,GAAG,EAAEhE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAC3F,MAAMlF,mBAAmB,GACvB1C,WAAW,CAAC0F,MAAM,GAAG,CAAC,GAClB,CAAC1F,WAAW,CAACyH,MAAM,CAAC,CAACC,GAAG,EAAErD,CAAC,KAAKqD,GAAG,IAAIrD,CAAC,CAACnB,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGlD,WAAW,CAAC0F,MAAM,EAAEkC,OAAO,CAAC,CAAC,CAAC,GACpG,CAAC;EACP,MAAMjF,qBAAqB,GACzB3C,WAAW,CAAC0F,MAAM,GAAG,CAAC,GAClB,CAAC1F,WAAW,CAACyH,MAAM,CAAC,CAACC,GAAG,EAAErD,CAAC,KAAKqD,GAAG,IAAIrD,CAAC,CAAClB,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGnD,WAAW,CAAC0F,MAAM,EAAEkC,OAAO,CAAC,CAAC,CAAC,GACtG,CAAC;EAEP,MAAMqE,WAAW,GAAGA,CAACtE,MAAM,EAAEuE,OAAO,GAAG,IAAI,kBACzCvO,OAAA;IAAKwO,SAAS,EAAC,MAAM;IAAAC,QAAA,EAClB,CAAC,GAAGlI,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,GAAG,CAAC,CAAC6H,CAAC,EAAEC,CAAC,kBACtB3O,OAAA,CAAC7B,MAAM;MAELqQ,SAAS,EAAE,WAAWG,CAAC,GAAG3E,MAAM,GAAG,iBAAiB,GAAG,eAAe,IACpEuE,OAAO,GAAG,gBAAgB,GAAG,EAAE,EAC9B;MACHA,OAAO,EAAEA,OAAO,GAAG,MAAMA,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,GAAG;IAAK,GAJ1CA,CAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKP,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMC,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MACJD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IACrC;EACF,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAIxN,OAAO,EAAE;IACX,oBAAO/B,OAAA,CAACjC,MAAM;MAAA6Q,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACnB;EAEA,oBACE/O,OAAA;IAAKwO,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvCzO,OAAA;MAAKwO,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDzO,OAAA,CAAClC,MAAM;QAAC0R,aAAa,EAAEA,CAAA,KAAM,CAAC;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnC/O,OAAA;QAAMwO,SAAS,EAAC,4BAA4B;QAACiB,KAAK,EAAE;UAAEnP,UAAU,EAAE,oCAAoCH,YAAY,CAACC,OAAO,OAAOD,YAAY,CAACG,UAAU;QAAI,CAAE;QAAAmO,QAAA,eAC5JzO,OAAA;UAAKwO,SAAS,EAAC,mBAAmB;UAAAC,QAAA,GAC/BxM,KAAK,iBACJjC,OAAA,CAAChC,MAAM,CAAC0R,GAAG;YACTC,OAAO,EAAE;cAAET,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCK,OAAO,EAAE;cAAEV,OAAO,EAAE,CAAC;cAAEK,CAAC,EAAE;YAAE,CAAE;YAC9Bf,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAE7EzO,OAAA;cAAKwO,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCzO,OAAA;gBAAKwO,SAAS,EAAC,2BAA2B;gBAACqB,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAArB,QAAA,eAChFzO,OAAA;kBACE+P,QAAQ,EAAC,SAAS;kBAClBC,CAAC,EAAC,yNAAyN;kBAC3NC,QAAQ,EAAC;gBAAS;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/O,OAAA;gBAAGwO,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAExM;cAAK;gBAAA2M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CACb,eAED/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;YAACC,OAAO,EAAE;cAAET,OAAO,EAAE;YAAE,CAAE;YAACU,OAAO,EAAE;cAAEV,OAAO,EAAE;YAAE,CAAE;YAACE,UAAU,EAAE;cAAEc,QAAQ,EAAE;YAAI,CAAE;YAAAzB,QAAA,gBAC1FzO,OAAA;cAAKwO,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FzO,OAAA;gBAAAyO,QAAA,gBACEzO,OAAA;kBAAIwO,SAAS,EAAC,qCAAqC;kBAACiB,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACC;kBAAQ,CAAE;kBAAAqO,QAAA,EAAC;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrH/O,OAAA;kBAAGyP,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACI;kBAAK,CAAE;kBAAAkO,QAAA,GAAC,gBAAc,EAAC,CAAA5M,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,IAAI,KAAI,YAAY;gBAAA;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACN/O,OAAA;gBAAKwO,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBzO,OAAA,CAAChC,MAAM,CAACmS,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1B9B,OAAO,EAAEA,CAAA,KAAMzK,qBAAqB,CAAC,IAAI,CAAE;kBAC3C0K,SAAS,EAAC,mDAAmD;kBAC7DiB,KAAK,EAAE;oBAAEnP,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;kBAAI,CAAE;kBAAAoO,QAAA,gBAEvGzO,OAAA,CAAClB,WAAW;oBAAC0P,SAAS,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,cAClC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/O,OAAA;cAAKwO,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBzO,OAAA;gBAAKwO,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,gBAC5CzO,OAAA;kBACEuO,OAAO,EAAEA,CAAA,KAAM/K,UAAU,CAAC,SAAS,CAAE;kBACrCgL,SAAS,EAAC,iCAAiC;kBAC3CiB,KAAK,EAAE;oBACLc,YAAY,EAAEhN,OAAO,KAAK,SAAS,GAAG,aAAapD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;oBAClFU,KAAK,EAAEyC,OAAO,KAAK,SAAS,GAAGpD,YAAY,CAACC,OAAO,GAAG,SAAS;oBAC/Da,eAAe,EAAEsC,OAAO,KAAK,SAAS,GAAG,GAAGpD,YAAY,CAACC,OAAO,IAAI,GAAG;kBACzE,CAAE;kBAAAqO,QAAA,gBAEFzO,OAAA,CAACpB,gBAAgB;oBAAC4P,SAAS,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEtD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/O,OAAA;kBACEuO,OAAO,EAAEA,CAAA,KAAM/K,UAAU,CAAC,WAAW,CAAE;kBACvCgL,SAAS,EAAC,iCAAiC;kBAC3CiB,KAAK,EAAE;oBACLc,YAAY,EAAEhN,OAAO,KAAK,WAAW,GAAG,aAAapD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;oBACpFU,KAAK,EAAEyC,OAAO,KAAK,WAAW,GAAGpD,YAAY,CAACC,OAAO,GAAG,SAAS;oBACjEa,eAAe,EAAEsC,OAAO,KAAK,WAAW,GAAG,GAAGpD,YAAY,CAACC,OAAO,IAAI,GAAG;kBAC3E,CAAE;kBAAAqO,QAAA,gBAEFzO,OAAA,CAACrB,UAAU;oBAAC6P,SAAS,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLxL,OAAO,KAAK,SAAS,iBACpBvD,OAAA,CAAAE,SAAA;cAAAuO,QAAA,gBAEEzO,OAAA,CAAChC,MAAM,CAAC0R,GAAG;gBACTc,QAAQ,EAAExB,SAAU;gBACpBW,OAAO,EAAC,QAAQ;gBAChBc,WAAW,EAAC,MAAM;gBAClBC,QAAQ,EAAE;kBAAEC,IAAI,EAAE;gBAAK,CAAE;gBACzBnC,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBAErEzO,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5IzO,OAAA;oBAAKwO,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCzO,OAAA;sBAAKwO,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACLxO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAqO,QAAA,eACFzO,OAAA,CAAC1B,UAAU;wBAACkQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC,eACN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAGwO,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACzF/O,OAAA;wBAAGwO,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,EAAER;sBAAY;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACb/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5IzO,OAAA;oBAAKwO,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCzO,OAAA;sBAAKwO,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACLxO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAqO,QAAA,eACFzO,OAAA,CAACzB,YAAY;wBAACiQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC,eACN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAGwO,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC3F/O,OAAA;wBAAGwO,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,GAAEL,cAAc,EAAC,GAAC;sBAAA;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC/F/O,OAAA;wBAAGwO,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,GAAC,WAAS,EAACJ,UAAU,EAAC,GAAC;sBAAA;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACb/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5IzO,OAAA;oBAAKwO,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCzO,OAAA;sBAAKwO,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACLxO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAqO,QAAA,eACFzO,OAAA,CAACxB,aAAa;wBAACgQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E,CAAC,eACN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAGwO,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAsB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAClG/O,OAAA;wBAAGwO,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,GAAE1J,mBAAmB,EAAC,IAAE;sBAAA;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACb/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTc,QAAQ,EAAElB,IAAK;kBACfd,SAAS,EAAC,kIAAkI;kBAAAC,QAAA,eAE5IzO,OAAA;oBAAKwO,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChCzO,OAAA;sBAAKwO,SAAS,EAAC,2FAA2F;sBACxGiB,KAAK,EAAE;wBACLxO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;wBAC5CU,KAAK,EAAEX,YAAY,CAACC;sBACtB,CAAE;sBAAAqO,QAAA,eACFzO,OAAA,CAACxB,aAAa;wBAACgQ,SAAS,EAAC,SAAS;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ;sBAAE;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E,CAAC,eACN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAGwO,SAAS,EAAC,qBAAqB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACpG/O,OAAA;wBAAGwO,SAAS,EAAC,oBAAoB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,GAAEzJ,qBAAqB,EAAC,IAAE;sBAAA;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGb/O,OAAA;gBAAKwO,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBzO,OAAA;kBAAKwO,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5CzO,OAAA;oBACEuO,OAAO,EAAEA,CAAA,KAAM7K,aAAa,CAAC,SAAS,CAAE;oBACxC8K,SAAS,EAAC,+BAA+B;oBACzCiB,KAAK,EAAE;sBACLc,YAAY,EAAE9M,UAAU,KAAK,SAAS,GAAG,aAAatD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;sBACrFU,KAAK,EAAE2C,UAAU,KAAK,SAAS,GAAGtD,YAAY,CAACC,OAAO,GAAG;oBAC3D,CAAE;oBAAAqO,QAAA,GACH,mBACkB,EAACtM,cAAc,CAAC4F,MAAM,EAAC,GAC1C;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/O,OAAA;oBACEuO,OAAO,EAAEA,CAAA,KAAM7K,aAAa,CAAC,MAAM,CAAE;oBACrC8K,SAAS,EAAC,+BAA+B;oBACzCiB,KAAK,EAAE;sBACLc,YAAY,EAAE9M,UAAU,KAAK,MAAM,GAAG,aAAatD,YAAY,CAACC,OAAO,EAAE,GAAG,MAAM;sBAClFU,KAAK,EAAE2C,UAAU,KAAK,MAAM,GAAGtD,YAAY,CAACC,OAAO,GAAG;oBACxD,CAAE;oBAAAqO,QAAA,GACH,gBACe,EAACpM,WAAW,CAAC0F,MAAM,EAAC,GACpC;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,eACN,CACH,EAGAxL,OAAO,KAAK,WAAW,iBACtBvD,OAAA;cAAKwO,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzO,OAAA;gBAAKwO,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,gBAC/FzO,OAAA;kBAAIwO,SAAS,EAAC,mBAAmB;kBAACiB,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACC;kBAAQ,CAAE;kBAAAqO,QAAA,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClG/O,OAAA;kBAAKwO,SAAS,EAAC,YAAY;kBAAAC,QAAA,eACzBzO,OAAA;oBACE4Q,KAAK,EAAEzM,kBAAmB;oBAC1B0M,QAAQ,EAAGC,CAAC,IAAK/H,8BAA8B,CAAC+H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAChEpC,SAAS,EAAC,6CAA6C;oBACvDiB,KAAK,EAAE;sBACLuB,OAAO,EAAE,MAAM;sBACfC,SAAS,EAAE,MAAM;sBACjBC,WAAW,EAAE,SAAS;sBACtB,QAAQ,EAAE;wBAAEA,WAAW,EAAE/Q,YAAY,CAACC;sBAAQ;oBAChD,CAAE;oBAAAqO,QAAA,gBAEFzO,OAAA;sBAAQ4Q,KAAK,EAAC,MAAM;sBAAAnC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC/O,OAAA;sBAAQ4Q,KAAK,EAAC,OAAO;sBAAAnC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACzC/O,OAAA;sBAAQ4Q,KAAK,EAAC,MAAM;sBAAAnC,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvC/O,OAAA;sBAAQ4Q,KAAK,EAAC,KAAK;sBAAAnC,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCrR,OAAA,CAAChC,MAAM,CAAC0R,GAAG;gBACTC,OAAO,EAAE;kBAAET,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAG,CAAE;gBAC/BK,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAE,CAAE;gBAC9Bf,SAAS,EAAC,2DAA2D;gBAAAC,QAAA,gBAErEzO,OAAA;kBAAIwO,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjF/O,OAAA;kBAAKwO,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrDzO,OAAA;oBAAAyO,QAAA,GAAG,cAAY,EAACtK,kBAAkB;kBAAA;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvC/O,OAAA;oBAAAyO,QAAA,GAAG,iBAAe,EAAClM,UAAU,CAACwF,MAAM;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzC/O,OAAA;oBAAAyO,QAAA,GAAG,uBAAqB,EAAC6C,IAAI,CAACC,SAAS,CAAClN,aAAa,CAACE,kBAAkB,CAAC;kBAAA;oBAAAqK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9E/O,OAAA;oBAAAyO,QAAA,GAAG,mBAAiB,EAAClF,MAAM,CAACC,IAAI,CAACnF,aAAa,CAACM,yBAAyB,CAAC,CAACoD,MAAM;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrF/O,OAAA;oBAAAyO,QAAA,GAAG,YAAU,EAAClF,MAAM,CAACC,IAAI,CAACnF,aAAa,CAACO,kBAAkB,CAAC,CAACmD,MAAM;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvE/O,OAAA;oBAAAyO,QAAA,GAAG,UAAQ,EAACpK,aAAa,CAACQ,YAAY,CAACkD,MAAM,EAAC,SAAO;kBAAA;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN/O,OAAA;kBAAKwO,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBzO,OAAA;oBACEuO,OAAO,EAAEA,CAAA,KAAMjK,gBAAgB,CAAC2D,kBAAkB,CAAC,CAAC,CAAE;oBACtDuG,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,EAC/E;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/O,OAAA;oBACEuO,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAACzF,UAAU,CAAE;oBAC9CiM,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,EACjF;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,eAGD/O,OAAA;gBAAKwO,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAEzDzO,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpEzO,OAAA;oBAAIwO,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE3O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAqO,QAAA,EAAC;kBAA0B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClH/O,OAAA;oBAAKwO,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDpK,aAAa,CAACE,kBAAkB,IAAIgF,MAAM,CAACkI,MAAM,CAACpN,aAAa,CAACE,kBAAkB,CAAC,CAACmN,IAAI,CAACC,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,gBACvG3R,OAAA,CAACL,GAAG;sBACF0G,IAAI,EAAE;wBACJhF,MAAM,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;wBACzCuQ,QAAQ,EAAE,CACR;0BACEvL,IAAI,EAAE,CACJhC,aAAa,CAACE,kBAAkB,CAACC,QAAQ,IAAI,CAAC,EAC9CH,aAAa,CAACE,kBAAkB,CAACE,OAAO,IAAI,CAAC,EAC7CJ,aAAa,CAACE,kBAAkB,CAACG,MAAM,IAAI,CAAC,CAC7C;0BACDzD,eAAe,EAAE,CACf,GAAGd,YAAY,CAACK,MAAM,IAAI;0BAAG;0BAC7B,GAAGL,YAAY,CAACC,OAAO,IAAI;0BAAE;0BAC7B,wBAAwB,CAAK;0BAAA,CAC9B;0BACD8Q,WAAW,EAAE,CACX/Q,YAAY,CAACK,MAAM,EACnBL,YAAY,CAACC,OAAO,EACpB,sBAAsB,CACvB;0BACDyR,WAAW,EAAE;wBACf,CAAC;sBAEL,CAAE;sBACFC,OAAO,EAAE;wBACPC,UAAU,EAAE,IAAI;wBAChBC,mBAAmB,EAAE,KAAK;wBAC1BjR,OAAO,EAAE;0BACPK,MAAM,EAAE;4BACN6Q,QAAQ,EAAE,QAAQ;4BAClB5Q,MAAM,EAAE;8BACNC,aAAa,EAAE,IAAI;8BACnBC,OAAO,EAAE,EAAE;8BACXZ,IAAI,EAAE;gCACJE,IAAI,EAAE;8BACR;4BACF;0BACF,CAAC;0BACDG,OAAO,EAAE;4BACPkR,SAAS,EAAE;8BACTC,KAAK,EAAE,SAAAA,CAASC,OAAO,EAAE;gCACvB,MAAMD,KAAK,GAAGC,OAAO,CAACD,KAAK,IAAI,EAAE;gCACjC,MAAMvB,KAAK,GAAGwB,OAAO,CAACC,GAAG,IAAI,CAAC;gCAC9B,MAAMpJ,KAAK,GAAGmJ,OAAO,CAACE,OAAO,CAACjM,IAAI,CAACyD,MAAM,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;gCAC7D,MAAMyH,UAAU,GAAGtJ,KAAK,GAAG,CAAC,GAAGuJ,IAAI,CAACC,KAAK,CAAE7B,KAAK,GAAG3H,KAAK,GAAI,GAAG,CAAC,GAAG,CAAC;gCACpE,OAAO,GAAGkJ,KAAK,KAAKvB,KAAK,KAAK2B,UAAU,IAAI;8BAC9C;4BACF;0BACF;wBACF;sBACF;oBAAE;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAEF/O,OAAA;sBAAKwO,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCzO,OAAA;wBAAKwO,SAAS,EAAC,sCAAsC;wBAACqB,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAAC4C,MAAM,EAAC,cAAc;wBAAAjE,QAAA,eACzGzO,OAAA;0BAAM2S,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC7C,CAAC,EAAC;wBAAsM;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3Q,CAAC,eACN/O,OAAA;wBAAAyO,QAAA,EAAG;sBAAwB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGb/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpEzO,OAAA;oBAAIwO,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE3O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAqO,QAAA,EAAC;kBAA2B;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnH/O,OAAA;oBAAKwO,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDpK,aAAa,CAACM,yBAAyB,IAAI4E,MAAM,CAACC,IAAI,CAACnF,aAAa,CAACM,yBAAyB,CAAC,CAACoD,MAAM,GAAG,CAAC,gBACzG/H,OAAA,CAACJ,GAAG;sBACFyG,IAAI,EAAE;wBACJhF,MAAM,EAAEkI,MAAM,CAACC,IAAI,CAACnF,aAAa,CAACM,yBAAyB,CAAC;wBAC5DiN,QAAQ,EAAE,CACR;0BACEO,KAAK,EAAE,UAAU;0BACjB9L,IAAI,EAAEkD,MAAM,CAACkI,MAAM,CAACpN,aAAa,CAACM,yBAAyB,CAAC,CAACkC,GAAG,CAACiM,CAAC,IAAIA,CAAC,CAACtO,QAAQ,IAAI,CAAC,CAAC;0BACtFvD,eAAe,EAAE,GAAGd,YAAY,CAACK,MAAM,IAAI;0BAC3C0Q,WAAW,EAAE/Q,YAAY,CAACK,MAAM;0BAChCqR,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,SAAS;0BAChB9L,IAAI,EAAEkD,MAAM,CAACkI,MAAM,CAACpN,aAAa,CAACM,yBAAyB,CAAC,CAACkC,GAAG,CAACiM,CAAC,IAAIA,CAAC,CAACrO,OAAO,IAAI,CAAC,CAAC;0BACrFxD,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;0BAC5C8Q,WAAW,EAAE/Q,YAAY,CAACC,OAAO;0BACjCyR,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,QAAQ;0BACf9L,IAAI,EAAEkD,MAAM,CAACkI,MAAM,CAACpN,aAAa,CAACM,yBAAyB,CAAC,CAACkC,GAAG,CAACiM,CAAC,IAAIA,CAAC,CAACpO,MAAM,IAAI,CAAC,CAAC;0BACpFzD,eAAe,EAAE,wBAAwB;0BACzCiQ,WAAW,EAAE,sBAAsB;0BACnCW,WAAW,EAAE;wBACf,CAAC;sBAEL,CAAE;sBACFC,OAAO,EAAE;wBACPC,UAAU,EAAE,IAAI;wBAChBC,mBAAmB,EAAE,KAAK;wBAC1Be,MAAM,EAAE;0BACNC,CAAC,EAAE;4BACDC,OAAO,EAAE,IAAI;4BACbC,KAAK,EAAE;8BACLC,WAAW,EAAE,EAAE;8BACfC,WAAW,EAAE;4BACf;0BACF,CAAC;0BACD7D,CAAC,EAAE;4BACD0D,OAAO,EAAE,IAAI;4BACbI,WAAW,EAAE,IAAI;4BACjBH,KAAK,EAAE;8BACLI,QAAQ,EAAE;4BACZ;0BACF;wBACF,CAAC;wBACDvS,OAAO,EAAE;0BACPK,MAAM,EAAE;4BACN6Q,QAAQ,EAAE,QAAQ;4BAClB5Q,MAAM,EAAE;8BACNC,aAAa,EAAE,IAAI;8BACnBC,OAAO,EAAE,EAAE;8BACXZ,IAAI,EAAE;gCACJE,IAAI,EAAE;8BACR;4BACF;0BACF,CAAC;0BACDG,OAAO,EAAE;4BACPuS,IAAI,EAAE,OAAO;4BACbC,SAAS,EAAE;0BACb;wBACF;sBACF;oBAAE;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAEF/O,OAAA;sBAAKwO,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCzO,OAAA;wBAAKwO,SAAS,EAAC,sCAAsC;wBAACqB,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAAC4C,MAAM,EAAC,cAAc;wBAAAjE,QAAA,eACzGzO,OAAA;0BAAM2S,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC7C,CAAC,EAAC;wBAAsM;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3Q,CAAC,eACN/O,OAAA;wBAAAyO,QAAA,EAAG;sBAA2B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGb/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpEzO,OAAA;oBAAIwO,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE3O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAqO,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3G/O,OAAA;oBAAKwO,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9BzO,OAAA;sBAAOwO,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,gBACpDzO,OAAA;wBAAOwO,SAAS,EAAC,YAAY;wBAAAC,QAAA,eAC3BzO,OAAA;0BAAAyO,QAAA,gBACEzO,OAAA;4BAAIwO,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,gFAAgF;4BAAAC,QAAA,EAAC;0BAE/F;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACR/O,OAAA;wBAAOwO,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EACjDpK,aAAa,CAACO,kBAAkB,IAAI2E,MAAM,CAACC,IAAI,CAACnF,aAAa,CAACO,kBAAkB,CAAC,CAACmD,MAAM,GAAG,CAAC,GAC3FwB,MAAM,CAACkK,OAAO,CAACpP,aAAa,CAACO,kBAAkB,CAAC,CAACiC,GAAG,CAAC,CAAC,CAACgD,OAAO,EAAExD,IAAI,CAAC,EAAEqN,KAAK,kBAC1E1T,OAAA;0BAAgBwO,SAAS,EAAEkF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,YAAa;0BAAAjF,QAAA,gBACrEzO,OAAA;4BAAIwO,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAC1E5E;0BAAO;4BAAA+E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DpI,IAAI,CAAC4C,KAAK,IAAI;0BAAC;4BAAA2F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACd,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DpI,IAAI,CAAC4C,KAAK,GAAG,CAAC,GAAG,GAAGuJ,IAAI,CAACC,KAAK,CAAEpM,IAAI,CAAC7B,QAAQ,GAAG6B,IAAI,CAAC4C,KAAK,GAAI,GAAG,CAAC,GAAG,GAAG;0BAAK;4BAAA2F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5E,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DpI,IAAI,CAACtB,mBAAmB,GAAG,CAAC,gBAC3B/E,OAAA;8BAAKwO,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChCzO,OAAA;gCAAMwO,SAAS,EAAC,MAAM;gCAAAC,QAAA,EAAEpI,IAAI,CAACtB;8BAAmB;gCAAA6J,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EACvDT,WAAW,CAACqF,UAAU,CAACtN,IAAI,CAACtB,mBAAmB,CAAC,CAAC;4BAAA;8BAAA6J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC/C,CAAC,GACJ;0BAAK;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAC9DpI,IAAI,CAACrB,qBAAqB,GAAG,CAAC,gBAC7BhF,OAAA;8BAAKwO,SAAS,EAAC,mBAAmB;8BAAAC,QAAA,gBAChCzO,OAAA;gCAAMwO,SAAS,EAAC,MAAM;gCAAAC,QAAA,EAAEpI,IAAI,CAACrB;8BAAqB;gCAAA4J,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,EACzDT,WAAW,CAACqF,UAAU,CAACtN,IAAI,CAACrB,qBAAqB,CAAC,CAAC;4BAAA;8BAAA4J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjD,CAAC,GACJ;0BAAK;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CAAC;wBAAA,GAzBE2E,KAAK;0BAAA9E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA0BV,CACL,CAAC,gBAEF/O,OAAA;0BAAAyO,QAAA,eACEzO,OAAA;4BAAI4T,OAAO,EAAC,GAAG;4BAACpF,SAAS,EAAC,6CAA6C;4BAAAC,QAAA,eACrEzO,OAAA;8BAAKwO,SAAS,EAAC,4BAA4B;8BAAAC,QAAA,gBACzCzO,OAAA;gCAAKwO,SAAS,EAAC,8BAA8B;gCAACqB,IAAI,EAAC,MAAM;gCAACC,OAAO,EAAC,WAAW;gCAAC4C,MAAM,EAAC,cAAc;gCAAAjE,QAAA,eACjGzO,OAAA;kCAAM2S,aAAa,EAAC,OAAO;kCAACC,cAAc,EAAC,OAAO;kCAACC,WAAW,EAAE,CAAE;kCAAC7C,CAAC,EAAC;gCAAyH;kCAAApB,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC9L,CAAC,yCAER;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBACL;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGb/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;kBACTC,OAAO,EAAE;oBAAET,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAG,CAAE;kBAC/BK,OAAO,EAAE;oBAAEV,OAAO,EAAE,CAAC;oBAAEK,CAAC,EAAE;kBAAE,CAAE;kBAC9BH,UAAU,EAAE;oBAAEoC,KAAK,EAAE;kBAAI,CAAE;kBAC3BhD,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBAEpEzO,OAAA;oBAAIwO,SAAS,EAAC,4BAA4B;oBAACiB,KAAK,EAAE;sBAAE3O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAqO,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrG/O,OAAA;oBAAKwO,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnDpK,aAAa,CAACQ,YAAY,IAAIR,aAAa,CAACQ,YAAY,CAACkD,MAAM,GAAG,CAAC,gBAClE/H,OAAA,CAACJ,GAAG;sBACFyG,IAAI,EAAE;wBACJhF,MAAM,EAAEgD,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACgN,CAAC,IAAIA,CAAC,CAAC3K,KAAK,CAAC;wBACpD0I,QAAQ,EAAE,CACR;0BACEO,KAAK,EAAE,UAAU;0BACjB9L,IAAI,EAAEhC,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACgN,CAAC,IAAIA,CAAC,CAACrP,QAAQ,IAAI,CAAC,CAAC;0BAC1DvD,eAAe,EAAE,GAAGd,YAAY,CAACK,MAAM,IAAI;0BAC3C0Q,WAAW,EAAE/Q,YAAY,CAACK,MAAM;0BAChCqR,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,SAAS;0BAChB9L,IAAI,EAAEhC,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACgN,CAAC,IAAIA,CAAC,CAACpP,OAAO,IAAI,CAAC,CAAC;0BACzDxD,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;0BAC5C8Q,WAAW,EAAE/Q,YAAY,CAACC,OAAO;0BACjCyR,WAAW,EAAE;wBACf,CAAC,EACD;0BACEM,KAAK,EAAE,QAAQ;0BACf9L,IAAI,EAAEhC,aAAa,CAACQ,YAAY,CAACgC,GAAG,CAACgN,CAAC,IAAIA,CAAC,CAACnP,MAAM,IAAI,CAAC,CAAC;0BACxDzD,eAAe,EAAE,wBAAwB;0BACzCiQ,WAAW,EAAE,sBAAsB;0BACnCW,WAAW,EAAE;wBACf,CAAC;sBAEL,CAAE;sBACFC,OAAO,EAAE;wBACPC,UAAU,EAAE,IAAI;wBAChBC,mBAAmB,EAAE,KAAK;wBAC1Be,MAAM,EAAE;0BACNC,CAAC,EAAE;4BACDC,OAAO,EAAE,IAAI;4BACbC,KAAK,EAAE;8BACLC,WAAW,EAAE,EAAE;8BACfC,WAAW,EAAE;4BACf;0BACF,CAAC;0BACD7D,CAAC,EAAE;4BACD0D,OAAO,EAAE,IAAI;4BACbI,WAAW,EAAE,IAAI;4BACjBH,KAAK,EAAE;8BACLI,QAAQ,EAAE;4BACZ;0BACF;wBACF,CAAC;wBACDvS,OAAO,EAAE;0BACPK,MAAM,EAAE;4BACN6Q,QAAQ,EAAE,QAAQ;4BAClB5Q,MAAM,EAAE;8BACNC,aAAa,EAAE,IAAI;8BACnBC,OAAO,EAAE,EAAE;8BACXZ,IAAI,EAAE;gCACJE,IAAI,EAAE;8BACR;4BACF;0BACF,CAAC;0BACDG,OAAO,EAAE;4BACPuS,IAAI,EAAE,OAAO;4BACbC,SAAS,EAAE;0BACb;wBACF;sBACF;oBAAE;sBAAA5E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,gBAEF/O,OAAA;sBAAKwO,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,gBACxCzO,OAAA;wBAAKwO,SAAS,EAAC,sCAAsC;wBAACqB,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAAC4C,MAAM,EAAC,cAAc;wBAAAjE,QAAA,eACzGzO,OAAA;0BAAM2S,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,WAAW,EAAE,CAAE;0BAAC7C,CAAC,EAAC;wBAAgF;0BAAApB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrJ,CAAC,eACN/O,OAAA;wBAAAyO,QAAA,EAAG;sBAAuB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGN/O,OAAA,CAAChC,MAAM,CAAC0R,GAAG;gBACTC,OAAO,EAAE;kBAAET,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAG,CAAE;gBAC/BK,OAAO,EAAE;kBAAEV,OAAO,EAAE,CAAC;kBAAEK,CAAC,EAAE;gBAAE,CAAE;gBAC9BH,UAAU,EAAE;kBAAEoC,KAAK,EAAE;gBAAI,CAAE;gBAC3BhD,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,gBAEzEzO,OAAA;kBAAIwO,SAAS,EAAC,4BAA4B;kBAACiB,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACC;kBAAQ,CAAE;kBAAAqO,QAAA,EAAC;gBAAuB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/G/O,OAAA;kBAAKwO,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDzO,OAAA;oBAAKwO,SAAS,EAAC,gBAAgB;oBAACiB,KAAK,EAAE;sBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;oBAAK,CAAE;oBAAAqO,QAAA,gBACtFzO,OAAA;sBAAIwO,SAAS,EAAC,0BAA0B;sBAACiB,KAAK,EAAE;wBAAE3O,KAAK,EAAEX,YAAY,CAACC;sBAAQ,CAAE;sBAAAqO,QAAA,EAAC;oBAAyB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/G/O,OAAA;sBAAKwO,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCzO,OAAA;wBAAKwO,SAAS,EAAC,yBAAyB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,GAC7EpK,aAAa,CAACS,cAAc,CAACC,mBAAmB,EAAC,IACpD;sBAAA;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN/O,OAAA;wBAAKwO,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAClBH,WAAW,CAACqF,UAAU,CAACtP,aAAa,CAACS,cAAc,CAACC,mBAAmB,CAAC;sBAAC;wBAAA6J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN/O,OAAA;oBAAKwO,SAAS,EAAC,gBAAgB;oBAACiB,KAAK,EAAE;sBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;oBAAK,CAAE;oBAAAqO,QAAA,gBACtFzO,OAAA;sBAAIwO,SAAS,EAAC,0BAA0B;sBAACiB,KAAK,EAAE;wBAAE3O,KAAK,EAAEX,YAAY,CAACC;sBAAQ,CAAE;sBAAAqO,QAAA,EAAC;oBAA2B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjH/O,OAAA;sBAAKwO,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,gBAChCzO,OAAA;wBAAKwO,SAAS,EAAC,yBAAyB;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,GAC7EpK,aAAa,CAACS,cAAc,CAACE,qBAAqB,EAAC,IACtD;sBAAA;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACN/O,OAAA;wBAAKwO,SAAS,EAAC,MAAM;wBAAAC,QAAA,EAClBH,WAAW,CAACqF,UAAU,CAACtP,aAAa,CAACS,cAAc,CAACE,qBAAqB,CAAC;sBAAC;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,EAGAxL,OAAO,KAAK,SAAS,iBACpBvD,OAAA,CAAChC,MAAM,CAAC0R,GAAG;cACTc,QAAQ,EAAExB,SAAU;cACpBW,OAAO,EAAC,QAAQ;cAChBc,WAAW,EAAC,MAAM;cAClBC,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBnC,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAE5HzO,OAAA;gBAAKwO,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBAClBzO,OAAA;kBAAKwO,SAAS,EAAC,kFAAkF;kBAAAC,QAAA,gBAC/FzO,OAAA;oBAAIwO,SAAS,EAAC,mBAAmB;oBAACiB,KAAK,EAAE;sBAAE3O,KAAK,EAAEX,YAAY,CAACC;oBAAQ,CAAE;oBAAAqO,QAAA,EACtEhL,UAAU,KAAK,SAAS,GAAG,iBAAiB,GAAG;kBAAc;oBAAAmL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eACL/O,OAAA;oBAAKwO,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBAC/DzO,OAAA;sBAAKwO,SAAS,EAAC,UAAU;sBAAAC,QAAA,gBACvBzO,OAAA;wBACEsJ,IAAI,EAAC,MAAM;wBACXwK,WAAW,EAAC,mCAAmC;wBAC/ClD,KAAK,EAAE7N,WAAY;wBACnB8N,QAAQ,EAAGC,CAAC,IAAK9N,cAAc,CAAC8N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAChDpC,SAAS,EAAC,0DAA0D;wBACpEiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAE/Q,YAAY,CAACC;0BAAQ;wBAChD;sBAAE;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACF/O,OAAA,CAACtB,QAAQ;wBAAC8P,SAAS,EAAC;sBAAqC;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,eAGN/O,OAAA;sBAAKwO,SAAS,EAAC,YAAY;sBAAAC,QAAA,eACzBzO,OAAA;wBACEuO,OAAO,EAAEA,CAAA,KAAMwF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAE;wBACpF1F,SAAS,EAAC,+DAA+D;wBACzEiB,KAAK,EAAE;0BACLyB,WAAW,EAAE,SAAS;0BACtBpQ,KAAK,EAAEX,YAAY,CAACI,IAAI;0BACxB6O,UAAU,EAAE;wBACd,CAAE;wBACF+E,WAAW,EAAGrD,CAAC,IAAMA,CAAC,CAACsD,aAAa,CAAC3E,KAAK,CAACxO,eAAe,GAAG,SAAW;wBACxEoT,UAAU,EAAGvD,CAAC,IAAMA,CAAC,CAACsD,aAAa,CAAC3E,KAAK,CAACxO,eAAe,GAAG,aAAe;wBAAAwN,QAAA,gBAE3EzO,OAAA,CAACvB,QAAQ;0BAAC+P,SAAS,EAAC;wBAAoB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,YAC7C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/O,OAAA;kBAAKsU,EAAE,EAAC,gBAAgB;kBAAC9F,SAAS,EAAC,mDAAmD;kBAACiB,KAAK,EAAE;oBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;kBAAK,CAAE;kBAAAqO,QAAA,eAC7IzO,OAAA;oBAAKwO,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDzO,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAOwO,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACzG/O,OAAA;wBACE4Q,KAAK,EAAEjO,SAAU;wBACjBkO,QAAQ,EAAGC,CAAC,IAAKlO,YAAY,CAACkO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAC9CpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAE/Q,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAqO,QAAA,gBAEFzO,OAAA;0BAAQ4Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACtC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACpC/O,OAAA;0BAAQ4Q,KAAK,EAAC,UAAU;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1C/O,OAAA;0BAAQ4Q,KAAK,EAAC,MAAM;0BAAAnC,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAEN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAOwO,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACnG/O,OAAA;wBACE4Q,KAAK,EAAE/N,UAAW;wBAClBgO,QAAQ,EAAGC,CAAC,IAAKhO,aAAa,CAACgO,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAC/CpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAE/Q,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAqO,QAAA,gBAEFzO,OAAA;0BAAQ4Q,KAAK,EAAC,EAAE;0BAAAnC,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACnC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvC/O,OAAA;0BAAQ4Q,KAAK,EAAC,OAAO;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAEN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAOwO,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC7G/O,OAAA;wBACE4Q,KAAK,EAAE3N,eAAgB;wBACvB4N,QAAQ,EAAGC,CAAC,IAAK5N,kBAAkB,CAAC4N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBACpDpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAE/Q,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAqO,QAAA,gBAEFzO,OAAA;0BAAQ4Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAc;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EAC1C5J,gBAAgB,CAAC0B,GAAG,CAAC0N,SAAS,iBAC7BvU,OAAA;0BAAwB4Q,KAAK,EAAE2D,SAAU;0BAAA9F,QAAA,EAAE8F;wBAAS,GAAvCA,SAAS;0BAAA3F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAuC,CAC9D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAEN/O,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAOwO,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACtG/O,OAAA;wBACE4Q,KAAK,EAAEzN,aAAc;wBACrB0N,QAAQ,EAAGC,CAAC,IAAK1N,gBAAgB,CAAC0N,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBAClDpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAE/Q,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAqO,QAAA,gBAEFzO,OAAA;0BAAQ4Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EACxC9J,cAAc,CAAC4B,GAAG,CAACgD,OAAO,iBACzB7J,OAAA;0BAAsB4Q,KAAK,EAAE/G,OAAQ;0BAAA4E,QAAA,EAAE5E;wBAAO,GAAjCA,OAAO;0BAAA+E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAmC,CACxD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,EAGLtL,UAAU,KAAK,MAAM,iBACpBzD,OAAA;sBAAAyO,QAAA,gBACEzO,OAAA;wBAAOwO,SAAS,EAAC,gCAAgC;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACI;wBAAK,CAAE;wBAAAkO,QAAA,EAAC;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACrG/O,OAAA;wBACE4Q,KAAK,EAAEvN,YAAa;wBACpBwN,QAAQ,EAAGC,CAAC,IAAKxN,eAAe,CAACwN,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;wBACjDpC,SAAS,EAAC,oDAAoD;wBAC9DiB,KAAK,EAAE;0BACLuB,OAAO,EAAE,MAAM;0BACfC,SAAS,EAAE,MAAM;0BACjBC,WAAW,EAAE,SAAS;0BACtB,QAAQ,EAAE;4BAAEA,WAAW,EAAE/Q,YAAY,CAACC;0BAAQ;wBAChD,CAAE;wBAAAqO,QAAA,gBAEFzO,OAAA;0BAAQ4Q,KAAK,EAAC,KAAK;0BAAAnC,QAAA,EAAC;wBAAY;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzC/O,OAAA;0BAAQ4Q,KAAK,EAAC,UAAU;0BAAAnC,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1C/O,OAAA;0BAAQ4Q,KAAK,EAAC,QAAQ;0BAAAnC,QAAA,EAAC;wBAAM;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAER/O,OAAA;kBAAKwO,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,eAC9BzO,OAAA;oBAAOwO,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,gBACpDzO,OAAA;sBAAOyP,KAAK,EAAE;wBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;sBAAK,CAAE;sBAAAqO,QAAA,eAC7DzO,OAAA;wBAAAyO,QAAA,gBACEzO,OAAA;0BAAIwO,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE3O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAqO,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAClI/O,OAAA;0BAAIwO,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE3O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAqO,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrI/O,OAAA;0BAAIwO,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE3O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAqO,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACrI/O,OAAA;0BAAIwO,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE3O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAqO,QAAA,EAAC;wBAAS;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACtItL,UAAU,KAAK,MAAM,iBACpBzD,OAAA,CAAAE,SAAA;0BAAAuO,QAAA,gBACEzO,OAAA;4BAAIwO,SAAS,EAAC,kEAAkE;4BAACiB,KAAK,EAAE;8BAAE3O,KAAK,EAAEX,YAAY,CAACC;4BAAQ,CAAE;4BAAAqO,QAAA,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACpI/O,OAAA;4BAAIwO,SAAS,EAAC,kEAAkE;4BAACiB,KAAK,EAAE;8BAAE3O,KAAK,EAAEX,YAAY,CAACC;4BAAQ,CAAE;4BAAAqO,QAAA,EAAC;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA,eACxI,CACH,EACAtL,UAAU,KAAK,SAAS,iBACvBzD,OAAA;0BAAIwO,SAAS,EAAC,kEAAkE;0BAACiB,KAAK,EAAE;4BAAE3O,KAAK,EAAEX,YAAY,CAACC;0BAAQ,CAAE;0BAAAqO,QAAA,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CACrI;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACR/O,OAAA;sBAAOwO,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EACjDjC,aAAa,CAAC/I,UAAU,KAAK,SAAS,GAAGtB,cAAc,GAAGE,WAAW,CAAC,CAAC0F,MAAM,KAAK,CAAC,gBAClF/H,OAAA;wBAAAyO,QAAA,eACEzO,OAAA;0BAAI4T,OAAO,EAAEnQ,UAAU,KAAK,SAAS,GAAG,CAAC,GAAG,CAAE;0BAAC+K,SAAS,EAAC,uBAAuB;0BAAAC,QAAA,eAC9EzO,OAAA;4BAAKwO,SAAS,EAAC,2CAA2C;4BAAAC,QAAA,gBACxDzO,OAAA;8BAAKwO,SAAS,EAAC,8BAA8B;8BAACqB,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAAC4C,MAAM,EAAC,cAAc;8BAAAjE,QAAA,eACjGzO,OAAA;gCAAM2S,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,WAAW,EAAE,CAAE;gCAAC7C,CAAC,EAAC;8BAAiI;gCAAApB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtM,CAAC,eACN/O,OAAA;8BAAIwO,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAAC,KAC7C,EAAChL,UAAU,KAAK,SAAS,GAAG,SAAS,GAAG,WAAW,EAAC,UACzD;4BAAA;8BAAAmL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eACL/O,OAAA;8BAAGwO,SAAS,EAAC,oBAAoB;8BAAAC,QAAA,EAC9BxM,KAAK,GACF,yFAAyF,GACzFwB,UAAU,KAAK,SAAS,GACtB,mGAAmG,GACnG;4BAAsF;8BAAAmL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC3F,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GAELvC,aAAa,CAAC/I,UAAU,KAAK,SAAS,GAAGtB,cAAc,GAAGE,WAAW,CAAC,CAACwE,GAAG,CAAEwC,MAAM;wBAAA,IAAAmL,iBAAA;wBAAA,oBAChFxU,OAAA,CAAChC,MAAM,CAACyW,EAAE;0BAER9E,OAAO,EAAE;4BAAET,OAAO,EAAE;0BAAE,CAAE;0BACxBU,OAAO,EAAE;4BAAEV,OAAO,EAAE;0BAAE,CAAE;0BACxBV,SAAS,EAAC,iCAAiC;0BAC3CD,OAAO,EAAEA,CAAA,KAAM7L,iBAAiB,CAAC2G,MAAM,CAAE;0BAAAoF,QAAA,gBAEzCzO,OAAA;4BAAIwO,SAAS,EAAC,+DAA+D;4BAAAC,QAAA,EAC1E,IAAIrE,IAAI,CAAC3G,UAAU,KAAK,SAAS,GAAG4F,MAAM,CAACoB,aAAa,GAAGpB,MAAM,CAAC2C,YAAY,IAAI3C,MAAM,CAACoB,aAAa,CAAC,CAACiK,kBAAkB,CAAC;0BAAC;4BAAA9F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3H,CAAC,eACL/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAE,EAAA+F,iBAAA,GAAAnL,MAAM,CAACsE,SAAS,cAAA6G,iBAAA,uBAAhBA,iBAAA,CAAkB5G,QAAQ,KAAI;0BAAK;4BAAAgB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC5G/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEpF,MAAM,CAACpC,WAAW,IAAI;0BAAK;4BAAA2H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpG/O,OAAA;4BAAIwO,SAAS,EAAC,mDAAmD;4BAAAC,QAAA,EAAEpF,MAAM,CAACjC,aAAa,IAAI;0BAAK;4BAAAwH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,EACrGtL,UAAU,KAAK,MAAM,iBACpBzD,OAAA,CAAAE,SAAA;4BAAAuO,QAAA,gBACEzO,OAAA;8BAAIwO,SAAS,EAAC,6BAA6B;8BAAAC,QAAA,eACzCzO,OAAA;gCACEwO,SAAS,EAAC,oEAAoE;gCAC9EiB,KAAK,EAAE;kCACLxO,eAAe,EAAEoI,MAAM,CAAC3D,MAAM,KAAK,UAAU,GAAG,GAAGvF,YAAY,CAACK,MAAM,IAAI,GAAG,SAAS;kCACtFM,KAAK,EAAEuI,MAAM,CAAC3D,MAAM,KAAK,UAAU,GAAGvF,YAAY,CAACK,MAAM,GAAG;gCAC9D,CAAE;gCAAAiO,QAAA,EAEDpF,MAAM,CAAC3D;8BAAM;gCAAAkJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACL,CAAC,eACL/O,OAAA;8BAAIwO,SAAS,EAAC,mDAAmD;8BAAAC,QAAA,EAAEpF,MAAM,CAAC0C,cAAc,IAAI;4BAAK;8BAAA6C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAK,CAAC;0BAAA,eACvG,CACH,EACAtL,UAAU,KAAK,SAAS,iBACvBzD,OAAA;4BAAIwO,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,eACjDzO,OAAA;8BACEuO,OAAO,EAAEA,CAAA,KAAM7L,iBAAiB,CAAC2G,MAAM,CAAE;8BACzCoG,KAAK,EAAE;gCAAE3O,KAAK,EAAEX,YAAY,CAACC;8BAAQ,CAAE;8BACvCoO,SAAS,EAAC,iBAAiB;8BAAAC,QAAA,EAC5B;4BAED;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP,CACL;wBAAA,GAtCI1F,MAAM,CAACwC,GAAG;0BAAA+C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAuCN,CAAC;sBAAA,CACb;oBACF;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELtM,cAAc,iBACbzC,OAAA;MAAKwO,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FzO,OAAA,CAAChC,MAAM,CAAC0R,GAAG;QACTC,OAAO,EAAE;UAAEU,KAAK,EAAE,GAAG;UAAEnB,OAAO,EAAE;QAAE,CAAE;QACpCU,OAAO,EAAE;UAAES,KAAK,EAAE,CAAC;UAAEnB,OAAO,EAAE;QAAE,CAAE;QAClCV,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAEzFzO,OAAA;UAAKwO,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBzO,OAAA;YAAKwO,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzO,OAAA;cAAIwO,SAAS,EAAC,oBAAoB;cAACiB,KAAK,EAAE;gBAAE3O,KAAK,EAAEX,YAAY,CAACC;cAAQ,CAAE;cAAAqO,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9F/O,OAAA;cAAQuO,OAAO,EAAEA,CAAA,KAAM7L,iBAAiB,CAAC,IAAI,CAAE;cAAC8L,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAC3FzO,OAAA;gBAAKwO,SAAS,EAAC,SAAS;gBAACqB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAC4C,MAAM,EAAC,cAAc;gBAAAjE,QAAA,eAC5EzO,OAAA;kBAAM2S,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAAC7C,CAAC,EAAC;gBAAsB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN/O,OAAA;YAAKwO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBzO,OAAA;cAAKwO,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAqO,QAAA,gBACtFzO,OAAA;gBAAIwO,SAAS,EAAC,8CAA8C;gBAACiB,KAAK,EAAE;kBAAE3O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAqO,QAAA,gBAClGzO,OAAA,CAAC/B,QAAQ;kBAACuQ,SAAS,EAAC,cAAc;kBAACiB,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACC;kBAAQ;gBAAE;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAE/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/O,OAAA;gBAAKwO,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDzO,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3D/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,EAAA/M,qBAAA,GAAAe,cAAc,CAACkL,SAAS,cAAAjM,qBAAA,uBAAxBA,qBAAA,CAA0BkM,QAAQ,KAAI;kBAAK;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,EAAA9M,sBAAA,GAAAc,cAAc,CAACkL,SAAS,cAAAhM,sBAAA,uBAAxBA,sBAAA,CAA0BgT,UAAU,KAAI;kBAAK;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/O,OAAA;cAAKwO,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAqO,QAAA,gBACtFzO,OAAA;gBAAIwO,SAAS,EAAC,8CAA8C;gBAACiB,KAAK,EAAE;kBAAE3O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAqO,QAAA,gBAClGzO,OAAA,CAAC9B,aAAa;kBAACsQ,SAAS,EAAC,cAAc;kBAACiB,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACC;kBAAQ;gBAAE;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAEpF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/O,OAAA;gBAAKwO,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzO,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9D/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEhM,cAAc,CAACwE,WAAW,IAAI;kBAAK;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAE,OAAOhM,cAAc,CAACsE,SAAS,KAAK,QAAQ,GAAGtE,cAAc,CAACsE,SAAS,CAACA,SAAS,GAAGtE,cAAc,CAACsE,SAAS,IAAI;kBAAK;oBAAA6H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEhM,cAAc,CAAC2E,aAAa,IAAI;kBAAK;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtChM,cAAc,CAACgI,aAAa,GAAG,IAAIL,IAAI,CAAC3H,cAAc,CAACgI,aAAa,CAAC,CAACmK,cAAc,CAAC,CAAC,GAAG;kBAAK;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEhM,cAAc,CAACgD,OAAO,IAAI;kBAAY;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN/O,OAAA;cAAKwO,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAqO,QAAA,gBACtFzO,OAAA;gBAAIwO,SAAS,EAAC,8CAA8C;gBAACiB,KAAK,EAAE;kBAAE3O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAqO,QAAA,gBAClGzO,OAAA,CAAChB,SAAS;kBAACwP,SAAS,EAAC,cAAc;kBAACiB,KAAK,EAAE;oBAAE3O,KAAK,EAAEX,YAAY,CAACC;kBAAQ;gBAAE;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,mBAEhF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/O,OAAA,CAACF,kBAAkB;gBACjB+U,WAAW,EAAEpS,cAAc,CAACoS,WAAW,IAAI,EAAG;gBAC9CzN,aAAa,EAAE3E,cAAc,CAAC2E;cAAc;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLtM,cAAc,CAACiD,MAAM,KAAK,SAAS,iBAClC1F,OAAA;cAAKwO,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAqO,QAAA,gBACtFzO,OAAA;gBAAIwO,SAAS,EAAC,4BAA4B;gBAACiB,KAAK,EAAE;kBAAE3O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAqO,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrG/O,OAAA;gBAAKwO,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzO,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC5ET,WAAW,CAACjJ,UAAU,CAACE,gBAAgB,EAAGyE,MAAM,IAC/C1E,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEE,gBAAgB,EAAEyE;kBAAO,CAAC,CAC3D,CAAC;gBAAA;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC9ET,WAAW,CAACjJ,UAAU,CAACG,kBAAkB,EAAGwE,MAAM,IACjD1E,aAAa,CAAC;oBAAE,GAAGD,UAAU;oBAAEG,kBAAkB,EAAEwE;kBAAO,CAAC,CAC7D,CAAC;gBAAA;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9E/O,OAAA;oBACE4Q,KAAK,EAAEvL,UAAU,CAACI,OAAQ;oBAC1BoL,QAAQ,EAAGC,CAAC,IAAKxL,aAAa,CAAC;sBAAE,GAAGD,UAAU;sBAAEI,OAAO,EAAEqL,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAE;oBAC3EpC,SAAS,EAAC,oDAAoD;oBAC9DiB,KAAK,EAAE;sBACLuB,OAAO,EAAE,MAAM;sBACfC,SAAS,EAAE,MAAM;sBACjBC,WAAW,EAAE,SAAS;sBACtB,QAAQ,EAAE;wBAAEA,WAAW,EAAE/Q,YAAY,CAACC;sBAAQ;oBAChD,CAAE;oBACF0U,IAAI,EAAC,GAAG;oBACRhB,WAAW,EAAC;kBAA8B;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3E/O,OAAA;oBAAKwO,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BzO,OAAA;sBACEsJ,IAAI,EAAC,QAAQ;sBACbiF,OAAO,EAAEA,CAAA,KAAMjJ,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEK,MAAM,EAAE;sBAAW,CAAC,CAAE;sBACpE8I,SAAS,EAAC,8DAA8D;sBACxEiB,KAAK,EAAE;wBACLxO,eAAe,EAAEoE,UAAU,CAACK,MAAM,KAAK,UAAU,GAC7CvF,YAAY,CAACK,MAAM,GACnB,SAAS;wBACbM,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,UAAU,GACnC,SAAS,GACTvF,YAAY,CAACI,IAAI;wBACrBwU,UAAU,EAAE1P,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG,QAAQ;wBAC/DsP,MAAM,EAAE3P,UAAU,CAACK,MAAM,KAAK,UAAU,GACpC,aAAavF,YAAY,CAACK,MAAM,EAAE,GAClC;sBACN,CAAE;sBAAAiO,QAAA,gBAEFzO,OAAA,CAAC5B,OAAO;wBAACoQ,SAAS,EAAC,MAAM;wBAACiB,KAAK,EAAE;0BAC/B3O,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,SAAS,GAAGvF,YAAY,CAACK;wBACrE;sBAAE;wBAAAoO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/O,OAAA;sBACEsJ,IAAI,EAAC,QAAQ;sBACbiF,OAAO,EAAEA,CAAA,KAAMjJ,aAAa,CAAC;wBAAE,GAAGD,UAAU;wBAAEK,MAAM,EAAE;sBAAS,CAAC,CAAE;sBAClE8I,SAAS,EAAC,8DAA8D;sBACxEiB,KAAK,EAAE;wBACLxO,eAAe,EAAEoE,UAAU,CAACK,MAAM,KAAK,QAAQ,GAC3C,SAAS,GACT,SAAS;wBACb5E,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,QAAQ,GACjC,SAAS,GACTvF,YAAY,CAACI,IAAI;wBACrBwU,UAAU,EAAE1P,UAAU,CAACK,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ;wBAC7DsP,MAAM,EAAE3P,UAAU,CAACK,MAAM,KAAK,QAAQ,GAClC,mBAAmB,GACnB;sBACN,CAAE;sBAAA+I,QAAA,gBAEFzO,OAAA,CAAC3B,OAAO;wBAACmQ,SAAS,EAAC,MAAM;wBAACiB,KAAK,EAAE;0BAC/B3O,KAAK,EAAEuE,UAAU,CAACK,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG;wBACtD;sBAAE;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAEP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN/O,OAAA;kBAAKwO,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBACjDzO,OAAA;oBAAIwO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAE/EpL,cAAc,gBACb3D,OAAA;oBAAKwO,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBzO,OAAA;sBAAKwO,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,gBACrDzO,OAAA;wBAAGwO,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAC;sBAA0B;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACnE/O,OAAA,CAAChC,MAAM,CAACmS,MAAM;wBACZC,UAAU,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAE;wBAC5BC,QAAQ,EAAE;0BAAED,KAAK,EAAE;wBAAK,CAAE;wBAC1B9B,OAAO,EAAEA,CAAA,KAAMjJ,aAAa,CAAC2G,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEtG,mBAAmB,EAAEhC;wBAAe,CAAC,CAAC,CAAE;wBACzF6K,SAAS,EAAC,mDAAmD;wBAC7DiB,KAAK,EAAE;0BAAEnP,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;wBAAI,CAAE;wBAAAoO,QAAA,gBAEvGzO,OAAA,CAAClB,WAAW;0BAAC0P,SAAS,EAAC;wBAAM;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,SAClC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAe,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,EAEL1J,UAAU,CAACM,mBAAmB,iBAC7B3F,OAAA;sBAAKwO,SAAS,EAAC,uBAAuB;sBACpCiB,KAAK,EAAE;wBACLyB,WAAW,EAAE,SAAS;wBACtBjQ,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;sBAC1C,CAAE;sBAAAqO,QAAA,gBACFzO,OAAA;wBAAIwO,SAAS,EAAC,0BAA0B;wBAACiB,KAAK,EAAE;0BAAE3O,KAAK,EAAEX,YAAY,CAACC;wBAAQ,CAAE;wBAAAqO,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EACtG1J,UAAU,CAACM,mBAAmB,CAACsP,UAAU,CAAC,YAAY,CAAC,gBACtDjV,OAAA;wBAAKkV,GAAG,EAAE7P,UAAU,CAACM,mBAAoB;wBAACwP,GAAG,EAAC,WAAW;wBAAC3G,SAAS,EAAC;sBAAkB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEzF/O,OAAA;wBAAGwO,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAAEpJ,UAAU,CAACM;sBAAmB;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CACtF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAEN/O,OAAA;oBAAKwO,SAAS,EAAC,qBAAqB;oBAClCiB,KAAK,EAAE;sBACLxO,eAAe,EAAE,GAAGd,YAAY,CAACE,SAAS,IAAI;sBAC9C2U,MAAM,EAAE,aAAa7U,YAAY,CAACE,SAAS;oBAC7C,CAAE;oBAAAoO,QAAA,gBACFzO,OAAA;sBAAGwO,SAAS,EAAC,cAAc;sBAACiB,KAAK,EAAE;wBAAE3O,KAAK,EAAEX,YAAY,CAACE;sBAAU,CAAE;sBAAAoO,QAAA,EAAC;oBAEtE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ/O,OAAA,CAAChC,MAAM,CAACmS,MAAM;sBACZC,UAAU,EAAE;wBAAEC,KAAK,EAAE;sBAAK,CAAE;sBAC5BC,QAAQ,EAAE;wBAAED,KAAK,EAAE;sBAAK,CAAE;sBAC1B9B,OAAO,EAAEA,CAAA,KAAMzK,qBAAqB,CAAC,IAAI,CAAE;sBAC3C0K,SAAS,EAAC,2DAA2D;sBACrEiB,KAAK,EAAE;wBAAExO,eAAe,EAAEd,YAAY,CAACE;sBAAU,CAAE;sBAAAoO,QAAA,gBAEnDzO,OAAA,CAAClB,WAAW;wBAAC0P,SAAS,EAAC;sBAAM;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,qBAClC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAe,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN/O,OAAA;kBAAKwO,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1CzO,OAAA,CAAChC,MAAM,CAACmS,MAAM;oBACZC,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAC1B9B,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAAC9I,cAAc,CAACoJ,GAAG,CAAE;oBACtDuJ,QAAQ,EAAE/P,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAoB;oBACtH6I,SAAS,EAAC,kDAAkD;oBAC5DiB,KAAK,EAAE;sBACLxO,eAAe,EAAEoE,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GACxH,SAAS,GACT,aAAa;sBACjBrF,UAAU,EAAE+E,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GACnH,SAAS,GACTN,UAAU,CAACK,MAAM,KAAK,UAAU,GAC9B,6BAA6BvF,YAAY,CAACK,MAAM,KAAKL,YAAY,CAACK,MAAM,GAAG,GAC3E,6CAA6C;sBACnDM,KAAK,EAAEuE,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GAC9G,SAAS,GACT,SAAS;sBACb0P,MAAM,EAAEhQ,UAAU,CAACE,gBAAgB,KAAK,CAAC,IAAIF,UAAU,CAACG,kBAAkB,KAAK,CAAC,IAAI,CAACH,UAAU,CAACM,mBAAmB,GAC/G,aAAa,GACb;oBACN,CAAE;oBAAA8I,QAAA,GAEDpJ,UAAU,CAACK,MAAM,KAAK,UAAU,gBAC/B1F,OAAA,CAAC5B,OAAO;sBAACoQ,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAEpC/O,OAAA,CAAC3B,OAAO;sBAACmQ,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CACpC,EAAC,SACK,EAAC1J,UAAU,CAACK,MAAM,KAAK,UAAU,GAAG,UAAU,GAAG,SAAS;kBAAA;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGAtM,cAAc,CAACiD,MAAM,KAAK,SAAS,iBAClC1F,OAAA;cAAKwO,SAAS,EAAC,gBAAgB;cAACiB,KAAK,EAAE;gBAAExO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO;cAAK,CAAE;cAAAqO,QAAA,gBACtFzO,OAAA;gBAAIwO,SAAS,EAAC,4BAA4B;gBAACiB,KAAK,EAAE;kBAAE3O,KAAK,EAAEX,YAAY,CAACC;gBAAQ,CAAE;gBAAAqO,QAAA,EAAC;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzG/O,OAAA;gBAAKwO,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzO,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEhM,cAAc,CAACsJ,cAAc,IAAI;kBAAK;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAa;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpE/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EACtChM,cAAc,CAACuJ,YAAY,GAAG,IAAI5B,IAAI,CAAC3H,cAAc,CAACuJ,YAAY,CAAC,CAAC4I,cAAc,CAAC,CAAC,GAAG;kBAAK;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACvET,WAAW,CAAC7L,cAAc,CAAC8C,gBAAgB,IAAI,CAAC,CAAC;gBAAA;kBAAAqJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EACzET,WAAW,CAAC7L,cAAc,CAAC+C,kBAAkB,IAAI,CAAC,CAAC;gBAAA;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9D/O,OAAA;oBAAGwO,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEhM,cAAc,CAACgD,OAAO,IAAI;kBAAY;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACN/O,OAAA;kBAAAyO,QAAA,gBACEzO,OAAA;oBAAIwO,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7D/O,OAAA;oBACEwO,SAAS,EAAC,oEAAoE;oBAC9EiB,KAAK,EAAE;sBACLxO,eAAe,EAAEwB,cAAc,CAACiD,MAAM,KAAK,UAAU,GAAG,GAAGvF,YAAY,CAACK,MAAM,IAAI,GAAG,SAAS;sBAC9FM,KAAK,EAAE2B,cAAc,CAACiD,MAAM,KAAK,UAAU,GAAGvF,YAAY,CAACK,MAAM,GAAG;oBACtE,CAAE;oBAAAiO,QAAA,EAEDhM,cAAc,CAACiD;kBAAM;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGN/O,OAAA;kBAAKwO,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,gBACjDzO,OAAA;oBAAIwO,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC/EtM,cAAc,CAACkD,mBAAmB,gBACjC3F,OAAA;oBAAKwO,SAAS,EAAC,uBAAuB;oBAACiB,KAAK,EAAE;sBAC5CyB,WAAW,EAAE,SAAS;sBACtBjQ,eAAe,EAAEd,YAAY,CAACG;oBAChC,CAAE;oBAAAmO,QAAA,EACChM,cAAc,CAACkD,mBAAmB,CAACsP,UAAU,CAAC,YAAY,CAAC,gBAC1DjV,OAAA;sBAAKkV,GAAG,EAAEzS,cAAc,CAACkD,mBAAoB;sBAACwP,GAAG,EAAC,WAAW;sBAAC3G,SAAS,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAErF/O,OAAA;sBAAGwO,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,EAAEhM,cAAc,CAACkD;oBAAmB;sBAAAiJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAC9E;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAEN/O,OAAA;oBAAGwO,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAC9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAGAlL,kBAAkB,iBACjB7D,OAAA;MAAKwO,SAAS,EAAC,iGAAiG;MAAAC,QAAA,eAC9GzO,OAAA,CAAChC,MAAM,CAAC0R,GAAG;QACTC,OAAO,EAAE;UAAET,OAAO,EAAE,CAAC;UAAEmB,KAAK,EAAE;QAAI,CAAE;QACpCT,OAAO,EAAE;UAAEV,OAAO,EAAE,CAAC;UAAEmB,KAAK,EAAE;QAAE,CAAE;QAClCjB,UAAU,EAAE;UAAE9F,IAAI,EAAE,QAAQ;UAAEgM,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAE;QAC5D/G,SAAS,EAAC,8EAA8E;QAAAC,QAAA,gBAExFzO,OAAA;UAAKwO,SAAS,EAAC,kBAAkB;UAACiB,KAAK,EAAE;YACvCnP,UAAU,EAAE,6BAA6BH,YAAY,CAACC,OAAO,KAAKD,YAAY,CAACE,SAAS;UAC1F,CAAE;UAAAoO,QAAA,eACAzO,OAAA;YAAKwO,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzO,OAAA;cAAIwO,SAAS,EAAC,gDAAgD;cAAAC,QAAA,gBAC5DzO,OAAA,CAAClB,WAAW;gBAAC0P,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/O,OAAA;cACEuO,OAAO,EAAEA,CAAA,KAAMzK,qBAAqB,CAAC,KAAK,CAAE;cAC5C0K,SAAS,EAAC,kDAAkD;cAAAC,QAAA,eAE5DzO,OAAA;gBAAKwO,SAAS,EAAC,SAAS;gBAACqB,IAAI,EAAC,MAAM;gBAAC6C,MAAM,EAAC,cAAc;gBAAC5C,OAAO,EAAC,WAAW;gBAAArB,QAAA,eAC5EzO,OAAA;kBAAM2S,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAAC7C,CAAC,EAAC;gBAAsB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/O,OAAA;UAAKwO,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBzO,OAAA;YAAKwO,SAAS,EAAC,gCAAgC;YAC7CiB,KAAK,EAAE;cACLxO,eAAe,EAAE,GAAGd,YAAY,CAACC,OAAO,IAAI;cAC5C8Q,WAAW,EAAE/Q,YAAY,CAACC;YAC5B,CAAE;YAAAqO,QAAA,eACFzO,OAAA;cAAGyP,KAAK,EAAE;gBAAE3O,KAAK,EAAEX,YAAY,CAACC;cAAQ,CAAE;cAAAqO,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN/O,OAAA,CAACH,gBAAgB;YACf2V,gBAAgB,EAAE7R,cAAe;YACjC8R,iBAAiB,EAAGnO,SAAS,IAAK;cAChC1D,iBAAiB,CAAC0D,SAAS,CAAC;cAC5B;cACA6E,UAAU,CAAC,MAAMrI,qBAAqB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;YACtD;UAAE;YAAA8K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EAGAhL,gBAAgB,iBACf/D,OAAA,CAAChC,MAAM,CAAC0R,GAAG;MACTC,OAAO,EAAE;QAAET,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCK,OAAO,EAAE;QAAEV,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE;MAAE,CAAE;MAC9BmG,IAAI,EAAE;QAAExG,OAAO,EAAE,CAAC;QAAEK,CAAC,EAAE;MAAG,CAAE;MAC5Bf,SAAS,EAAC,sEAAsE;MAAAC,QAAA,eAEhFzO,OAAA;QAAKwO,SAAS,EAAC,gFAAgF;QAC7FiB,KAAK,EAAE;UAAEyB,WAAW,EAAE/Q,YAAY,CAACK;QAAO,CAAE;QAAAiO,QAAA,gBAC5CzO,OAAA;UAAKwO,SAAS,EAAC,uBAAuB;UAACiB,KAAK,EAAE;YAAExO,eAAe,EAAE,GAAGd,YAAY,CAACK,MAAM;UAAK,CAAE;UAAAiO,QAAA,eAC5FzO,OAAA,CAAC5B,OAAO;YAACoQ,SAAS,EAAC,SAAS;YAACiB,KAAK,EAAE;cAAE3O,KAAK,EAAEX,YAAY,CAACK;YAAO;UAAE;YAAAoO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACN/O,OAAA;UAAKwO,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBzO,OAAA;YAAIwO,SAAS,EAAC,aAAa;YAACiB,KAAK,EAAE;cAAE3O,KAAK,EAAEX,YAAY,CAACI;YAAK,CAAE;YAAAkO,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E/O,OAAA;YAAGyP,KAAK,EAAE;cAAE3O,KAAK,EAAE;YAAU,CAAE;YAAA2N,QAAA,EAAExK;UAAc;YAAA2K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACN/O,OAAA;UACEuO,OAAO,EAAEA,CAAA,KAAMvK,mBAAmB,CAAC,KAAK,CAAE;UAC1CwK,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAE7CzO,OAAA,CAAC3B,OAAO;YAAAuQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtN,EAAA,CAtwDID,mBAAmB;EAAA,QACN7D,WAAW,EACJE,OAAO;AAAA;AAAA8X,EAAA,GAF3BnU,mBAAmB;AAwwDzB,eAAeA,mBAAmB;AAAC,IAAAmU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}