import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCheckCircle, FaTimesCircle, FaCircle, FaCheck, FaTimes } from 'react-icons/fa';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745',
  danger: '#dc2626'
};

const ReviewStepsDisplay = ({ reviewSteps, procedureType, onStepStatusChange, isSupervisorMode = false, stepStatuses = {} }) => {
  const [localStepStatuses, setLocalStepStatuses] = useState(stepStatuses);

  // Update local state when prop changes
  React.useEffect(() => {
    setLocalStepStatuses(stepStatuses);
  }, [stepStatuses]);

  if (!reviewSteps || reviewSteps.length === 0) {
    return (
      <div className="p-4 rounded-lg text-center" style={{ backgroundColor: '#f8f9fa' }}>
        <p style={{ color: colorPalette.text }}>No review steps available for this procedure.</p>
      </div>
    );
  }

  // Count steps by status
  const completedSteps = reviewSteps.filter(step => step.completed).length;
  const totalSteps = reviewSteps.length;
  const acceptedSteps = Object.values(localStepStatuses).filter(status => status === 'accepted').length;
  const declinedSteps = Object.values(localStepStatuses).filter(status => status === 'declined').length;
  const pendingSteps = completedSteps - acceptedSteps - declinedSteps;

  const handleStepAction = (index, action) => {
    const newStatuses = { ...localStepStatuses };
    newStatuses[index] = action;
    setLocalStepStatuses(newStatuses);
    
    if (onStepStatusChange) {
      onStepStatusChange(index, action);
    }
  };

  const getStepStatus = (index) => {
    return localStepStatuses[index] || 'pending';
  };

  const getStepBackgroundColor = (step, index) => {
    if (!step.completed) return colorPalette.background;
    
    const status = getStepStatus(index);
    switch (status) {
      case 'accepted':
        return `${colorPalette.accent}10`;
      case 'declined':
        return `${colorPalette.danger}10`;
      default:
        return `${colorPalette.primary}10`;
    }
  };

  const getStepIcon = (step, index) => {
    if (!step.completed) {
      return <FaCircle className="h-5 w-5 text-gray-300" />;
    }
    
    const status = getStepStatus(index);
    switch (status) {
      case 'accepted':
        return <FaCheckCircle className="h-5 w-5" style={{ color: colorPalette.accent }} />;
      case 'declined':
        return <FaTimesCircle className="h-5 w-5" style={{ color: colorPalette.danger }} />;
      default:
        return <FaCircle className="h-5 w-5" style={{ color: colorPalette.primary }} />;
    }
  };

  const getStepTextColor = (step, index) => {
    if (!step.completed) return '#666666';
    
    const status = getStepStatus(index);
    switch (status) {
      case 'accepted':
        return colorPalette.accent;
      case 'declined':
        return colorPalette.danger;
      default:
        return colorPalette.primary;
    }
  };

  return (
    <div className="rounded-lg overflow-hidden" style={{ backgroundColor: colorPalette.background, border: `1px solid #e5e7eb` }}>
      <div className="p-4 border-b" style={{ backgroundColor: `${colorPalette.primary}10`, borderColor: '#e5e7eb' }}>
        <div className="flex justify-between items-center">
          <h3 className="text-md font-semibold" style={{ color: colorPalette.primary }}>
            {procedureType} Review Steps
          </h3>
          <div className="flex items-center space-x-4">
            {isSupervisorMode && (
              <div className="flex items-center space-x-2 text-xs">
                <span className="flex items-center">
                  <FaCheckCircle className="h-3 w-3 mr-1" style={{ color: colorPalette.accent }} />
                  {acceptedSteps} Accepted
                </span>
                <span className="flex items-center">
                  <FaTimesCircle className="h-3 w-3 mr-1" style={{ color: colorPalette.danger }} />
                  {declinedSteps} Declined
                </span>
                <span className="flex items-center">
                  <FaCircle className="h-3 w-3 mr-1" style={{ color: colorPalette.primary }} />
                  {pendingSteps} Pending
                </span>
              </div>
            )}
            <div className="flex items-center">
              <div className="w-32 rounded-full h-2.5 mr-2" style={{ backgroundColor: '#e5e7eb' }}>
                <div
                  className="h-2.5 rounded-full"
                  style={{
                    width: `${(completedSteps / totalSteps) * 100}%`,
                    backgroundColor: colorPalette.accent
                  }}
                ></div>
              </div>
              <span className="text-sm font-medium" style={{ color: colorPalette.text }}>
                {completedSteps}/{totalSteps}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="divide-y" style={{ borderColor: '#e5e7eb' }}>
        {reviewSteps.map((step, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className="p-3 flex items-center justify-between"
            style={{
              backgroundColor: getStepBackgroundColor(step, index)
            }}
          >
            <div className="flex items-center flex-1">
              <div className="flex-shrink-0 mt-0.5 mr-3">
                {getStepIcon(step, index)}
              </div>
              <div className="flex-1">
                <p className="text-sm" style={{
                  fontWeight: step.completed ? '500' : 'normal',
                  color: getStepTextColor(step, index)
                }}>
                  {step.description}
                </p>
              </div>
            </div>
            
            {isSupervisorMode && step.completed && (
              <div className="flex items-center space-x-2 ml-4">
                {getStepStatus(index) === 'pending' && (
                  <>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleStepAction(index, 'accepted')}
                      className="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                      style={{
                        backgroundColor: colorPalette.accent,
                        color: 'white'
                      }}
                    >
                      <FaCheck className="h-3 w-3 mr-1" />
                      Accept
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleStepAction(index, 'declined')}
                      className="px-3 py-1 rounded-full text-xs font-medium flex items-center"
                      style={{
                        backgroundColor: colorPalette.danger,
                        color: 'white'
                      }}
                    >
                      <FaTimes className="h-3 w-3 mr-1" />
                      Decline
                    </motion.button>
                  </>
                )}
                {getStepStatus(index) === 'accepted' && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: `${colorPalette.accent}20`,
                      color: colorPalette.accent
                    }}>
                    <FaCheckCircle className="h-3 w-3 mr-1" />
                    Accepted
                  </span>
                )}
                {getStepStatus(index) === 'declined' && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: `${colorPalette.danger}20`,
                      color: colorPalette.danger
                    }}>
                    <FaTimesCircle className="h-3 w-3 mr-1" />
                    Declined
                  </span>
                )}
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default ReviewStepsDisplay;
