{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\supervisor\\\\ReviewStepsDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaClock, FaCheck, FaTimes, FaComment } from 'react-icons/fa';\nimport axios from 'axios';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\nconst ReviewStepsDisplay = ({\n  reviewSteps,\n  procedureType,\n  reviewId,\n  onStepUpdate\n}) => {\n  _s();\n  const [loading, setLoading] = useState({});\n  const [showCommentModal, setShowCommentModal] = useState(false);\n  const [selectedStep, setSelectedStep] = useState(null);\n  const [comment, setComment] = useState('');\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 rounded-lg text-center\",\n      style: {\n        backgroundColor: '#f8f9fa'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: colorPalette.text\n        },\n        children: \"No review steps available for this procedure.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Helper function to get step status\n  const getStepStatus = step => {\n    if (!step.completed) return 'not_started';\n    if (step.supervisorStatus === 'approved') return 'approved';\n    if (step.supervisorStatus === 'declined') return 'declined';\n    return 'pending'; // Student completed but supervisor hasn't reviewed\n  };\n\n  // Helper function to get step color\n  const getStepColor = status => {\n    switch (status) {\n      case 'approved':\n        return colorPalette.accent;\n      // Green\n      case 'pending':\n        return '#FF8C00';\n      // Orange\n      case 'declined':\n        return '#DC3545';\n      // Red\n      default:\n        return '#6C757D';\n      // Gray\n    }\n  };\n\n  // Helper function to get step icon\n  const getStepIcon = status => {\n    switch (status) {\n      case 'approved':\n        return /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 31\n        }, this);\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(FaClock, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 30\n        }, this);\n      case 'declined':\n        return /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 31\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaCircle, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 23\n        }, this);\n    }\n  };\n\n  // Count steps by status\n  const approvedSteps = reviewSteps.filter(step => getStepStatus(step) === 'approved').length;\n  const pendingSteps = reviewSteps.filter(step => getStepStatus(step) === 'pending').length;\n  const totalSteps = reviewSteps.length;\n  const completionPercentage = Math.round(approvedSteps / totalSteps * 100);\n\n  // Handle step status update\n  const handleStepUpdate = async (stepIndex, supervisorStatus, supervisorComment = '') => {\n    if (!reviewId) return;\n    setLoading(prev => ({\n      ...prev,\n      [stepIndex]: true\n    }));\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.put(`http://localhost:5000/api/reviews/${reviewId}/steps/${stepIndex}/status`, {\n        supervisorStatus,\n        supervisorComment\n      }, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (onStepUpdate) {\n        onStepUpdate(response.data);\n      }\n    } catch (error) {\n      console.error('Error updating step status:', error);\n      alert('Failed to update step status. Please try again.');\n    } finally {\n      setLoading(prev => ({\n        ...prev,\n        [stepIndex]: false\n      }));\n    }\n  };\n\n  // Handle comment modal\n  const openCommentModal = (stepIndex, action) => {\n    var _reviewSteps$stepInde;\n    setSelectedStep({\n      index: stepIndex,\n      action\n    });\n    setComment(((_reviewSteps$stepInde = reviewSteps[stepIndex]) === null || _reviewSteps$stepInde === void 0 ? void 0 : _reviewSteps$stepInde.supervisorComment) || '');\n    setShowCommentModal(true);\n  };\n  const submitWithComment = async () => {\n    if (selectedStep) {\n      await handleStepUpdate(selectedStep.index, selectedStep.action, comment);\n      setShowCommentModal(false);\n      setSelectedStep(null);\n      setComment('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-lg overflow-hidden\",\n    style: {\n      backgroundColor: colorPalette.background,\n      border: `1px solid #e5e7eb`\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b\",\n      style: {\n        backgroundColor: `${colorPalette.primary}10`,\n        borderColor: '#e5e7eb'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-md font-semibold\",\n          style: {\n            color: colorPalette.primary\n          },\n          children: [procedureType, \" Review Steps\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-32 rounded-full h-2.5 mr-2\",\n              style: {\n                backgroundColor: '#e5e7eb'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-2.5 rounded-full\",\n                style: {\n                  width: `${completionPercentage}%`,\n                  backgroundColor: colorPalette.accent\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              style: {\n                color: colorPalette.text\n              },\n              children: [approvedSteps, \"/\", totalSteps, \" Approved\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), pendingSteps > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium px-2 py-1 rounded-full\",\n            style: {\n              backgroundColor: '#FF8C0020',\n              color: '#FF8C00'\n            },\n            children: [pendingSteps, \" Pending\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y\",\n      style: {\n        borderColor: '#e5e7eb'\n      },\n      children: reviewSteps.map((step, index) => {\n        const status = getStepStatus(step);\n        const stepColor = getStepColor(status);\n        return /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 5\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.05\n          },\n          className: \"p-4 flex items-start justify-between\",\n          style: {\n            backgroundColor: status !== 'not_started' ? `${stepColor}10` : colorPalette.background\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0 mt-0.5 mr-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: stepColor\n                },\n                children: getStepIcon(status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm\",\n                style: {\n                  fontWeight: status !== 'not_started' ? '500' : 'normal',\n                  color: status !== 'not_started' ? colorPalette.text : '#666666'\n                },\n                children: step.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this), step.supervisorComment && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs mt-1 italic\",\n                style: {\n                  color: '#6C757D'\n                },\n                children: [\"Supervisor note: \", step.supervisorComment]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mt-1\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs px-2 py-1 rounded-full\",\n                  style: {\n                    backgroundColor: `${stepColor}20`,\n                    color: stepColor\n                  },\n                  children: status === 'not_started' ? 'Not Started' : status === 'pending' ? 'Pending Review' : status === 'approved' ? 'Approved' : 'Declined'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), status === 'pending' && reviewId && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleStepUpdate(index, 'approved'),\n              disabled: loading[index],\n              className: \"flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors\",\n              style: {\n                backgroundColor: colorPalette.accent,\n                color: 'white'\n              },\n              title: \"Approve step\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), loading[index] ? 'Approving...' : 'Approve']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => openCommentModal(index, 'declined'),\n              disabled: loading[index],\n              className: \"flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors\",\n              style: {\n                backgroundColor: '#DC3545',\n                color: 'white'\n              },\n              title: \"Decline step\",\n              children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this), \"Decline\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => openCommentModal(index, 'approved'),\n              disabled: loading[index],\n              className: \"flex items-center px-2 py-1 rounded-md text-xs font-medium transition-colors\",\n              style: {\n                backgroundColor: '#6C757D',\n                color: 'white'\n              },\n              title: \"Add comment\",\n              children: /*#__PURE__*/_jsxDEV(FaComment, {\n                className: \"h-3 w-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewStepsDisplay, \"1sUnRvsPkVUb4CGjB7+HZpIq2bw=\");\n_c = ReviewStepsDisplay;\nexport default ReviewStepsDisplay;\nvar _c;\n$RefreshReg$(_c, \"ReviewStepsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaCheckCircle", "FaTimesCircle", "FaCircle", "FaClock", "FaCheck", "FaTimes", "FaComment", "axios", "jsxDEV", "_jsxDEV", "colorPalette", "primary", "secondary", "background", "text", "accent", "ReviewStepsDisplay", "reviewSteps", "procedureType", "reviewId", "onStepUpdate", "_s", "loading", "setLoading", "showCommentModal", "setShowCommentModal", "selectedStep", "setSelectedStep", "comment", "setComment", "length", "className", "style", "backgroundColor", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStepStatus", "step", "completed", "supervisorS<PERSON>us", "getStepColor", "status", "getStepIcon", "approvedSteps", "filter", "pendingSteps", "totalSteps", "completionPercentage", "Math", "round", "handleStepUpdate", "stepIndex", "supervisorComment", "prev", "token", "localStorage", "getItem", "response", "put", "headers", "Authorization", "data", "error", "console", "alert", "openCommentModal", "action", "_reviewSteps$stepInde", "index", "submitWithComment", "border", "borderColor", "width", "map", "stepColor", "div", "initial", "opacity", "y", "animate", "transition", "delay", "fontWeight", "description", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Co<PERSON>/dentlyzer-frontend/src/supervisor/ReviewStepsDisplay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaClock, FaCheck, FaTimes, FaComment } from 'react-icons/fa';\nimport axios from 'axios';\n\n// Website color palette\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\nconst ReviewStepsDisplay = ({ reviewSteps, procedureType, reviewId, onStepUpdate }) => {\n  const [loading, setLoading] = useState({});\n  const [showCommentModal, setShowCommentModal] = useState(false);\n  const [selectedStep, setSelectedStep] = useState(null);\n  const [comment, setComment] = useState('');\n\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return (\n      <div className=\"p-4 rounded-lg text-center\" style={{ backgroundColor: '#f8f9fa' }}>\n        <p style={{ color: colorPalette.text }}>No review steps available for this procedure.</p>\n      </div>\n    );\n  }\n\n  // Helper function to get step status\n  const getStepStatus = (step) => {\n    if (!step.completed) return 'not_started';\n    if (step.supervisorStatus === 'approved') return 'approved';\n    if (step.supervisorStatus === 'declined') return 'declined';\n    return 'pending'; // Student completed but supervisor hasn't reviewed\n  };\n\n  // Helper function to get step color\n  const getStepColor = (status) => {\n    switch (status) {\n      case 'approved': return colorPalette.accent; // Green\n      case 'pending': return '#FF8C00'; // Orange\n      case 'declined': return '#DC3545'; // Red\n      default: return '#6C757D'; // Gray\n    }\n  };\n\n  // Helper function to get step icon\n  const getStepIcon = (status) => {\n    switch (status) {\n      case 'approved': return <FaCheckCircle className=\"h-5 w-5\" />;\n      case 'pending': return <FaClock className=\"h-5 w-5\" />;\n      case 'declined': return <FaTimesCircle className=\"h-5 w-5\" />;\n      default: return <FaCircle className=\"h-5 w-5\" />;\n    }\n  };\n\n  // Count steps by status\n  const approvedSteps = reviewSteps.filter(step => getStepStatus(step) === 'approved').length;\n  const pendingSteps = reviewSteps.filter(step => getStepStatus(step) === 'pending').length;\n  const totalSteps = reviewSteps.length;\n  const completionPercentage = Math.round((approvedSteps / totalSteps) * 100);\n\n  // Handle step status update\n  const handleStepUpdate = async (stepIndex, supervisorStatus, supervisorComment = '') => {\n    if (!reviewId) return;\n\n    setLoading(prev => ({ ...prev, [stepIndex]: true }));\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.put(\n        `http://localhost:5000/api/reviews/${reviewId}/steps/${stepIndex}/status`,\n        { supervisorStatus, supervisorComment },\n        { headers: { Authorization: `Bearer ${token}` } }\n      );\n\n      if (onStepUpdate) {\n        onStepUpdate(response.data);\n      }\n    } catch (error) {\n      console.error('Error updating step status:', error);\n      alert('Failed to update step status. Please try again.');\n    } finally {\n      setLoading(prev => ({ ...prev, [stepIndex]: false }));\n    }\n  };\n\n  // Handle comment modal\n  const openCommentModal = (stepIndex, action) => {\n    setSelectedStep({ index: stepIndex, action });\n    setComment(reviewSteps[stepIndex]?.supervisorComment || '');\n    setShowCommentModal(true);\n  };\n\n  const submitWithComment = async () => {\n    if (selectedStep) {\n      await handleStepUpdate(selectedStep.index, selectedStep.action, comment);\n      setShowCommentModal(false);\n      setSelectedStep(null);\n      setComment('');\n    }\n  };\n\n  return (\n    <div className=\"rounded-lg overflow-hidden\" style={{ backgroundColor: colorPalette.background, border: `1px solid #e5e7eb` }}>\n      <div className=\"p-4 border-b\" style={{ backgroundColor: `${colorPalette.primary}10`, borderColor: '#e5e7eb' }}>\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-md font-semibold\" style={{ color: colorPalette.primary }}>\n            {procedureType} Review Steps\n          </h3>\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center\">\n              <div className=\"w-32 rounded-full h-2.5 mr-2\" style={{ backgroundColor: '#e5e7eb' }}>\n                <div\n                  className=\"h-2.5 rounded-full\"\n                  style={{\n                    width: `${completionPercentage}%`,\n                    backgroundColor: colorPalette.accent\n                  }}\n                ></div>\n              </div>\n              <span className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>\n                {approvedSteps}/{totalSteps} Approved\n              </span>\n            </div>\n            {pendingSteps > 0 && (\n              <span className=\"text-sm font-medium px-2 py-1 rounded-full\" style={{\n                backgroundColor: '#FF8C0020',\n                color: '#FF8C00'\n              }}>\n                {pendingSteps} Pending\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"divide-y\" style={{ borderColor: '#e5e7eb' }}>\n        {reviewSteps.map((step, index) => {\n          const status = getStepStatus(step);\n          const stepColor = getStepColor(status);\n\n          return (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 5 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n              className=\"p-4 flex items-start justify-between\"\n              style={{\n                backgroundColor: status !== 'not_started' ? `${stepColor}10` : colorPalette.background\n              }}\n            >\n              <div className=\"flex items-start flex-1\">\n                <div className=\"flex-shrink-0 mt-0.5 mr-3\">\n                  <div style={{ color: stepColor }}>\n                    {getStepIcon(status)}\n                  </div>\n                </div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm\" style={{\n                    fontWeight: status !== 'not_started' ? '500' : 'normal',\n                    color: status !== 'not_started' ? colorPalette.text : '#666666'\n                  }}>\n                    {step.description}\n                  </p>\n                  {step.supervisorComment && (\n                    <p className=\"text-xs mt-1 italic\" style={{ color: '#6C757D' }}>\n                      Supervisor note: {step.supervisorComment}\n                    </p>\n                  )}\n                  <div className=\"flex items-center mt-1\">\n                    <span className=\"text-xs px-2 py-1 rounded-full\" style={{\n                      backgroundColor: `${stepColor}20`,\n                      color: stepColor\n                    }}>\n                      {status === 'not_started' ? 'Not Started' :\n                       status === 'pending' ? 'Pending Review' :\n                       status === 'approved' ? 'Approved' : 'Declined'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Supervisor Action Buttons - Only show for pending steps */}\n              {status === 'pending' && reviewId && (\n                <div className=\"flex items-center space-x-2 ml-4\">\n                  <button\n                    onClick={() => handleStepUpdate(index, 'approved')}\n                    disabled={loading[index]}\n                    className=\"flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors\"\n                    style={{\n                      backgroundColor: colorPalette.accent,\n                      color: 'white'\n                    }}\n                    title=\"Approve step\"\n                  >\n                    <FaCheck className=\"h-3 w-3 mr-1\" />\n                    {loading[index] ? 'Approving...' : 'Approve'}\n                  </button>\n                  <button\n                    onClick={() => openCommentModal(index, 'declined')}\n                    disabled={loading[index]}\n                    className=\"flex items-center px-3 py-1 rounded-md text-xs font-medium transition-colors\"\n                    style={{\n                      backgroundColor: '#DC3545',\n                      color: 'white'\n                    }}\n                    title=\"Decline step\"\n                  >\n                    <FaTimes className=\"h-3 w-3 mr-1\" />\n                    Decline\n                  </button>\n                  <button\n                    onClick={() => openCommentModal(index, 'approved')}\n                    disabled={loading[index]}\n                    className=\"flex items-center px-2 py-1 rounded-md text-xs font-medium transition-colors\"\n                    style={{\n                      backgroundColor: '#6C757D',\n                      color: 'white'\n                    }}\n                    title=\"Add comment\"\n                  >\n                    <FaComment className=\"h-3 w-3\" />\n                  </button>\n                </div>\n              )}\n            </motion.div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewStepsDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,QAAQ,gBAAgB;AAC7G,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,aAAa;EAAEC,QAAQ;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAE1C,IAAI,CAACmB,WAAW,IAAIA,WAAW,CAACa,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACErB,OAAA;MAAKsB,SAAS,EAAC,4BAA4B;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE;MAAU,CAAE;MAAAC,QAAA,eAChFzB,OAAA;QAAGuB,KAAK,EAAE;UAAEG,KAAK,EAAEzB,YAAY,CAACI;QAAK,CAAE;QAAAoB,QAAA,EAAC;MAA6C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;;EAEA;EACA,MAAMC,aAAa,GAAIC,IAAI,IAAK;IAC9B,IAAI,CAACA,IAAI,CAACC,SAAS,EAAE,OAAO,aAAa;IACzC,IAAID,IAAI,CAACE,gBAAgB,KAAK,UAAU,EAAE,OAAO,UAAU;IAC3D,IAAIF,IAAI,CAACE,gBAAgB,KAAK,UAAU,EAAE,OAAO,UAAU;IAC3D,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,OAAOnC,YAAY,CAACK,MAAM;MAAE;MAC7C,KAAK,SAAS;QAAE,OAAO,SAAS;MAAE;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MAAE;MACnC;QAAS,OAAO,SAAS;MAAE;IAC7B;EACF,CAAC;;EAED;EACA,MAAM+B,WAAW,GAAID,MAAM,IAAK;IAC9B,QAAQA,MAAM;MACZ,KAAK,UAAU;QAAE,oBAAOpC,OAAA,CAACT,aAAa;UAAC+B,SAAS,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,SAAS;QAAE,oBAAO9B,OAAA,CAACN,OAAO;UAAC4B,SAAS,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,UAAU;QAAE,oBAAO9B,OAAA,CAACR,aAAa;UAAC8B,SAAS,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D;QAAS,oBAAO9B,OAAA,CAACP,QAAQ;UAAC6B,SAAS,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAG9B,WAAW,CAAC+B,MAAM,CAACP,IAAI,IAAID,aAAa,CAACC,IAAI,CAAC,KAAK,UAAU,CAAC,CAACX,MAAM;EAC3F,MAAMmB,YAAY,GAAGhC,WAAW,CAAC+B,MAAM,CAACP,IAAI,IAAID,aAAa,CAACC,IAAI,CAAC,KAAK,SAAS,CAAC,CAACX,MAAM;EACzF,MAAMoB,UAAU,GAAGjC,WAAW,CAACa,MAAM;EACrC,MAAMqB,oBAAoB,GAAGC,IAAI,CAACC,KAAK,CAAEN,aAAa,GAAGG,UAAU,GAAI,GAAG,CAAC;;EAE3E;EACA,MAAMI,gBAAgB,GAAG,MAAAA,CAAOC,SAAS,EAAEZ,gBAAgB,EAAEa,iBAAiB,GAAG,EAAE,KAAK;IACtF,IAAI,CAACrC,QAAQ,EAAE;IAEfI,UAAU,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,SAAS,GAAG;IAAK,CAAC,CAAC,CAAC;IACpD,IAAI;MACF,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAC9B,qCAAqC3C,QAAQ,UAAUoC,SAAS,SAAS,EACzE;QAAEZ,gBAAgB;QAAEa;MAAkB,CAAC,EACvC;QAAEO,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAAE,CAClD,CAAC;MAED,IAAItC,YAAY,EAAE;QAChBA,YAAY,CAACyC,QAAQ,CAACI,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDE,KAAK,CAAC,iDAAiD,CAAC;IAC1D,CAAC,SAAS;MACR7C,UAAU,CAACkC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,SAAS,GAAG;MAAM,CAAC,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMc,gBAAgB,GAAGA,CAACd,SAAS,EAAEe,MAAM,KAAK;IAAA,IAAAC,qBAAA;IAC9C5C,eAAe,CAAC;MAAE6C,KAAK,EAAEjB,SAAS;MAAEe;IAAO,CAAC,CAAC;IAC7CzC,UAAU,CAAC,EAAA0C,qBAAA,GAAAtD,WAAW,CAACsC,SAAS,CAAC,cAAAgB,qBAAA,uBAAtBA,qBAAA,CAAwBf,iBAAiB,KAAI,EAAE,CAAC;IAC3D/B,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMgD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI/C,YAAY,EAAE;MAChB,MAAM4B,gBAAgB,CAAC5B,YAAY,CAAC8C,KAAK,EAAE9C,YAAY,CAAC4C,MAAM,EAAE1C,OAAO,CAAC;MACxEH,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,eAAe,CAAC,IAAI,CAAC;MACrBE,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEpB,OAAA;IAAKsB,SAAS,EAAC,4BAA4B;IAACC,KAAK,EAAE;MAAEC,eAAe,EAAEvB,YAAY,CAACG,UAAU;MAAE6D,MAAM,EAAE;IAAoB,CAAE;IAAAxC,QAAA,gBAC3HzB,OAAA;MAAKsB,SAAS,EAAC,cAAc;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE,GAAGvB,YAAY,CAACC,OAAO,IAAI;QAAEgE,WAAW,EAAE;MAAU,CAAE;MAAAzC,QAAA,eAC5GzB,OAAA;QAAKsB,SAAS,EAAC,mCAAmC;QAAAG,QAAA,gBAChDzB,OAAA;UAAIsB,SAAS,EAAC,uBAAuB;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEzB,YAAY,CAACC;UAAQ,CAAE;UAAAuB,QAAA,GAC1EhB,aAAa,EAAC,eACjB;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9B,OAAA;UAAKsB,SAAS,EAAC,6BAA6B;UAAAG,QAAA,gBAC1CzB,OAAA;YAAKsB,SAAS,EAAC,mBAAmB;YAAAG,QAAA,gBAChCzB,OAAA;cAAKsB,SAAS,EAAC,8BAA8B;cAACC,KAAK,EAAE;gBAAEC,eAAe,EAAE;cAAU,CAAE;cAAAC,QAAA,eAClFzB,OAAA;gBACEsB,SAAS,EAAC,oBAAoB;gBAC9BC,KAAK,EAAE;kBACL4C,KAAK,EAAE,GAAGzB,oBAAoB,GAAG;kBACjClB,eAAe,EAAEvB,YAAY,CAACK;gBAChC;cAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN9B,OAAA;cAAMsB,SAAS,EAAC,qBAAqB;cAACC,KAAK,EAAE;gBAAEG,KAAK,EAAEzB,YAAY,CAACI;cAAK,CAAE;cAAAoB,QAAA,GACvEa,aAAa,EAAC,GAAC,EAACG,UAAU,EAAC,WAC9B;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACLU,YAAY,GAAG,CAAC,iBACfxC,OAAA;YAAMsB,SAAS,EAAC,4CAA4C;YAACC,KAAK,EAAE;cAClEC,eAAe,EAAE,WAAW;cAC5BE,KAAK,EAAE;YACT,CAAE;YAAAD,QAAA,GACCe,YAAY,EAAC,UAChB;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9B,OAAA;MAAKsB,SAAS,EAAC,UAAU;MAACC,KAAK,EAAE;QAAE2C,WAAW,EAAE;MAAU,CAAE;MAAAzC,QAAA,EACzDjB,WAAW,CAAC4D,GAAG,CAAC,CAACpC,IAAI,EAAE+B,KAAK,KAAK;QAChC,MAAM3B,MAAM,GAAGL,aAAa,CAACC,IAAI,CAAC;QAClC,MAAMqC,SAAS,GAAGlC,YAAY,CAACC,MAAM,CAAC;QAEtC,oBACEpC,OAAA,CAACV,MAAM,CAACgF,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,KAAK,EAAEb,KAAK,GAAG;UAAK,CAAE;UACpCzC,SAAS,EAAC,sCAAsC;UAChDC,KAAK,EAAE;YACLC,eAAe,EAAEY,MAAM,KAAK,aAAa,GAAG,GAAGiC,SAAS,IAAI,GAAGpE,YAAY,CAACG;UAC9E,CAAE;UAAAqB,QAAA,gBAEFzB,OAAA;YAAKsB,SAAS,EAAC,yBAAyB;YAAAG,QAAA,gBACtCzB,OAAA;cAAKsB,SAAS,EAAC,2BAA2B;cAAAG,QAAA,eACxCzB,OAAA;gBAAKuB,KAAK,EAAE;kBAAEG,KAAK,EAAE2C;gBAAU,CAAE;gBAAA5C,QAAA,EAC9BY,WAAW,CAACD,MAAM;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN9B,OAAA;cAAKsB,SAAS,EAAC,QAAQ;cAAAG,QAAA,gBACrBzB,OAAA;gBAAGsB,SAAS,EAAC,SAAS;gBAACC,KAAK,EAAE;kBAC5BsD,UAAU,EAAEzC,MAAM,KAAK,aAAa,GAAG,KAAK,GAAG,QAAQ;kBACvDV,KAAK,EAAEU,MAAM,KAAK,aAAa,GAAGnC,YAAY,CAACI,IAAI,GAAG;gBACxD,CAAE;gBAAAoB,QAAA,EACCO,IAAI,CAAC8C;cAAW;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,EACHE,IAAI,CAACe,iBAAiB,iBACrB/C,OAAA;gBAAGsB,SAAS,EAAC,qBAAqB;gBAACC,KAAK,EAAE;kBAAEG,KAAK,EAAE;gBAAU,CAAE;gBAAAD,QAAA,GAAC,mBAC7C,EAACO,IAAI,CAACe,iBAAiB;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CACJ,eACD9B,OAAA;gBAAKsB,SAAS,EAAC,wBAAwB;gBAAAG,QAAA,eACrCzB,OAAA;kBAAMsB,SAAS,EAAC,gCAAgC;kBAACC,KAAK,EAAE;oBACtDC,eAAe,EAAE,GAAG6C,SAAS,IAAI;oBACjC3C,KAAK,EAAE2C;kBACT,CAAE;kBAAA5C,QAAA,EACCW,MAAM,KAAK,aAAa,GAAG,aAAa,GACxCA,MAAM,KAAK,SAAS,GAAG,gBAAgB,GACvCA,MAAM,KAAK,UAAU,GAAG,UAAU,GAAG;gBAAU;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLM,MAAM,KAAK,SAAS,IAAI1B,QAAQ,iBAC/BV,OAAA;YAAKsB,SAAS,EAAC,kCAAkC;YAAAG,QAAA,gBAC/CzB,OAAA;cACE+E,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAACkB,KAAK,EAAE,UAAU,CAAE;cACnDiB,QAAQ,EAAEnE,OAAO,CAACkD,KAAK,CAAE;cACzBzC,SAAS,EAAC,8EAA8E;cACxFC,KAAK,EAAE;gBACLC,eAAe,EAAEvB,YAAY,CAACK,MAAM;gBACpCoB,KAAK,EAAE;cACT,CAAE;cACFuD,KAAK,EAAC,cAAc;cAAAxD,QAAA,gBAEpBzB,OAAA,CAACL,OAAO;gBAAC2B,SAAS,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACnCjB,OAAO,CAACkD,KAAK,CAAC,GAAG,cAAc,GAAG,SAAS;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACT9B,OAAA;cACE+E,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAACG,KAAK,EAAE,UAAU,CAAE;cACnDiB,QAAQ,EAAEnE,OAAO,CAACkD,KAAK,CAAE;cACzBzC,SAAS,EAAC,8EAA8E;cACxFC,KAAK,EAAE;gBACLC,eAAe,EAAE,SAAS;gBAC1BE,KAAK,EAAE;cACT,CAAE;cACFuD,KAAK,EAAC,cAAc;cAAAxD,QAAA,gBAEpBzB,OAAA,CAACJ,OAAO;gBAAC0B,SAAS,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9B,OAAA;cACE+E,OAAO,EAAEA,CAAA,KAAMnB,gBAAgB,CAACG,KAAK,EAAE,UAAU,CAAE;cACnDiB,QAAQ,EAAEnE,OAAO,CAACkD,KAAK,CAAE;cACzBzC,SAAS,EAAC,8EAA8E;cACxFC,KAAK,EAAE;gBACLC,eAAe,EAAE,SAAS;gBAC1BE,KAAK,EAAE;cACT,CAAE;cACFuD,KAAK,EAAC,aAAa;cAAAxD,QAAA,eAEnBzB,OAAA,CAACH,SAAS;gBAACyB,SAAS,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA,GAlFIiC,KAAK;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmFA,CAAC;MAEjB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA1NIL,kBAAkB;AAAA2E,EAAA,GAAlB3E,kBAAkB;AA4NxB,eAAeA,kBAAkB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}