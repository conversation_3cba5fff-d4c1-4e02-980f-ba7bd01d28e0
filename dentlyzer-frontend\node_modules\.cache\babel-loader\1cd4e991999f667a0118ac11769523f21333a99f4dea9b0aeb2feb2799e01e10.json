{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\supervisor\\\\ReviewStepsDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaClock, FaCheck, FaTimes, FaComment } from 'react-icons/fa';\nimport axios from 'axios';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\nconst ReviewStepsDisplay = ({\n  reviewSteps,\n  procedureType,\n  reviewId,\n  onStepUpdate\n}) => {\n  _s();\n  const [loading, setLoading] = useState({});\n  const [showCommentModal, setShowCommentModal] = useState(false);\n  const [selectedStep, setSelectedStep] = useState(null);\n  const [comment, setComment] = useState('');\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 rounded-lg text-center\",\n      style: {\n        backgroundColor: '#f8f9fa'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: colorPalette.text\n        },\n        children: \"No review steps available for this procedure.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Count completed steps\n  const completedSteps = reviewSteps.filter(step => step.completed).length;\n  const totalSteps = reviewSteps.length;\n  const completionPercentage = Math.round(completedSteps / totalSteps * 100);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-lg overflow-hidden\",\n    style: {\n      backgroundColor: colorPalette.background,\n      border: `1px solid #e5e7eb`\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-b\",\n      style: {\n        backgroundColor: `${colorPalette.primary}10`,\n        borderColor: '#e5e7eb'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-md font-semibold\",\n          style: {\n            color: colorPalette.primary\n          },\n          children: [procedureType, \" Review Steps\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-32 rounded-full h-2.5 mr-2\",\n            style: {\n              backgroundColor: '#e5e7eb'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-2.5 rounded-full\",\n              style: {\n                width: `${completionPercentage}%`,\n                backgroundColor: colorPalette.accent\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            style: {\n              color: colorPalette.text\n            },\n            children: [completedSteps, \"/\", totalSteps]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divide-y\",\n      style: {\n        borderColor: '#e5e7eb'\n      },\n      children: reviewSteps.map((step, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 5\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.05\n        },\n        className: \"p-3 flex items-start\",\n        style: {\n          backgroundColor: step.completed ? `${colorPalette.accent}10` : colorPalette.background\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0 mt-0.5 mr-3\",\n          children: step.completed ? /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n            className: \"h-5 w-5\",\n            style: {\n              color: colorPalette.accent\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(FaCircle, {\n            className: \"h-5 w-5 text-gray-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm\",\n            style: {\n              fontWeight: step.completed ? '500' : 'normal',\n              color: step.completed ? colorPalette.text : '#666666'\n            },\n            children: step.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(ReviewStepsDisplay, \"1sUnRvsPkVUb4CGjB7+HZpIq2bw=\");\n_c = ReviewStepsDisplay;\nexport default ReviewStepsDisplay;\nvar _c;\n$RefreshReg$(_c, \"ReviewStepsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "motion", "FaCheckCircle", "FaTimesCircle", "FaCircle", "FaClock", "FaCheck", "FaTimes", "FaComment", "axios", "jsxDEV", "_jsxDEV", "colorPalette", "primary", "secondary", "background", "text", "accent", "ReviewStepsDisplay", "reviewSteps", "procedureType", "reviewId", "onStepUpdate", "_s", "loading", "setLoading", "showCommentModal", "setShowCommentModal", "selectedStep", "setSelectedStep", "comment", "setComment", "length", "className", "style", "backgroundColor", "children", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "completedSteps", "filter", "step", "completed", "totalSteps", "completionPercentage", "Math", "round", "border", "borderColor", "width", "map", "index", "div", "initial", "opacity", "y", "animate", "transition", "delay", "fontWeight", "description", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Co<PERSON>/dentlyzer-frontend/src/supervisor/ReviewStepsDisplay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaCheckCircle, FaTimesCircle, FaCircle, FaClock, FaCheck, FaTimes, FaComment } from 'react-icons/fa';\nimport axios from 'axios';\n\n// Website color palette\nconst colorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\nconst ReviewStepsDisplay = ({ reviewSteps, procedureType, reviewId, onStepUpdate }) => {\n  const [loading, setLoading] = useState({});\n  const [showCommentModal, setShowCommentModal] = useState(false);\n  const [selectedStep, setSelectedStep] = useState(null);\n  const [comment, setComment] = useState('');\n\n  if (!reviewSteps || reviewSteps.length === 0) {\n    return (\n      <div className=\"p-4 rounded-lg text-center\" style={{ backgroundColor: '#f8f9fa' }}>\n        <p style={{ color: colorPalette.text }}>No review steps available for this procedure.</p>\n      </div>\n    );\n  }\n\n  // Count completed steps\n  const completedSteps = reviewSteps.filter(step => step.completed).length;\n  const totalSteps = reviewSteps.length;\n  const completionPercentage = Math.round((completedSteps / totalSteps) * 100);\n\n  return (\n    <div className=\"rounded-lg overflow-hidden\" style={{ backgroundColor: colorPalette.background, border: `1px solid #e5e7eb` }}>\n      <div className=\"p-4 border-b\" style={{ backgroundColor: `${colorPalette.primary}10`, borderColor: '#e5e7eb' }}>\n        <div className=\"flex justify-between items-center\">\n          <h3 className=\"text-md font-semibold\" style={{ color: colorPalette.primary }}>\n            {procedureType} Review Steps\n          </h3>\n          <div className=\"flex items-center\">\n            <div className=\"w-32 rounded-full h-2.5 mr-2\" style={{ backgroundColor: '#e5e7eb' }}>\n              <div\n                className=\"h-2.5 rounded-full\"\n                style={{\n                  width: `${completionPercentage}%`,\n                  backgroundColor: colorPalette.accent\n                }}\n              ></div>\n            </div>\n            <span className=\"text-sm font-medium\" style={{ color: colorPalette.text }}>\n              {completedSteps}/{totalSteps}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"divide-y\" style={{ borderColor: '#e5e7eb' }}>\n        {reviewSteps.map((step, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 5 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.05 }}\n            className=\"p-3 flex items-start\"\n            style={{\n              backgroundColor: step.completed ? `${colorPalette.accent}10` : colorPalette.background\n            }}\n          >\n            <div className=\"flex-shrink-0 mt-0.5 mr-3\">\n              {step.completed ? (\n                <FaCheckCircle className=\"h-5 w-5\" style={{ color: colorPalette.accent }} />\n              ) : (\n                <FaCircle className=\"h-5 w-5 text-gray-300\" />\n              )}\n            </div>\n            <div className=\"flex-1\">\n              <p className=\"text-sm\" style={{\n                fontWeight: step.completed ? '500' : 'normal',\n                color: step.completed ? colorPalette.text : '#666666'\n              }}>\n                {step.description}\n              </p>\n            </div>\n          </motion.div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewStepsDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,QAAQ,gBAAgB;AAC7G,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,aAAa;EAAEC,QAAQ;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAE1C,IAAI,CAACmB,WAAW,IAAIA,WAAW,CAACa,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACErB,OAAA;MAAKsB,SAAS,EAAC,4BAA4B;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE;MAAU,CAAE;MAAAC,QAAA,eAChFzB,OAAA;QAAGuB,KAAK,EAAE;UAAEG,KAAK,EAAEzB,YAAY,CAACI;QAAK,CAAE;QAAAoB,QAAA,EAAC;MAA6C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;;EAEA;EACA,MAAMC,cAAc,GAAGvB,WAAW,CAACwB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACb,MAAM;EACxE,MAAMc,UAAU,GAAG3B,WAAW,CAACa,MAAM;EACrC,MAAMe,oBAAoB,GAAGC,IAAI,CAACC,KAAK,CAAEP,cAAc,GAAGI,UAAU,GAAI,GAAG,CAAC;EAE5E,oBACEnC,OAAA;IAAKsB,SAAS,EAAC,4BAA4B;IAACC,KAAK,EAAE;MAAEC,eAAe,EAAEvB,YAAY,CAACG,UAAU;MAAEmC,MAAM,EAAE;IAAoB,CAAE;IAAAd,QAAA,gBAC3HzB,OAAA;MAAKsB,SAAS,EAAC,cAAc;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE,GAAGvB,YAAY,CAACC,OAAO,IAAI;QAAEsC,WAAW,EAAE;MAAU,CAAE;MAAAf,QAAA,eAC5GzB,OAAA;QAAKsB,SAAS,EAAC,mCAAmC;QAAAG,QAAA,gBAChDzB,OAAA;UAAIsB,SAAS,EAAC,uBAAuB;UAACC,KAAK,EAAE;YAAEG,KAAK,EAAEzB,YAAY,CAACC;UAAQ,CAAE;UAAAuB,QAAA,GAC1EhB,aAAa,EAAC,eACjB;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9B,OAAA;UAAKsB,SAAS,EAAC,mBAAmB;UAAAG,QAAA,gBAChCzB,OAAA;YAAKsB,SAAS,EAAC,8BAA8B;YAACC,KAAK,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAC,QAAA,eAClFzB,OAAA;cACEsB,SAAS,EAAC,oBAAoB;cAC9BC,KAAK,EAAE;gBACLkB,KAAK,EAAE,GAAGL,oBAAoB,GAAG;gBACjCZ,eAAe,EAAEvB,YAAY,CAACK;cAChC;YAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN9B,OAAA;YAAMsB,SAAS,EAAC,qBAAqB;YAACC,KAAK,EAAE;cAAEG,KAAK,EAAEzB,YAAY,CAACI;YAAK,CAAE;YAAAoB,QAAA,GACvEM,cAAc,EAAC,GAAC,EAACI,UAAU;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9B,OAAA;MAAKsB,SAAS,EAAC,UAAU;MAACC,KAAK,EAAE;QAAEiB,WAAW,EAAE;MAAU,CAAE;MAAAf,QAAA,EACzDjB,WAAW,CAACkC,GAAG,CAAC,CAACT,IAAI,EAAEU,KAAK,kBAC3B3C,OAAA,CAACV,MAAM,CAACsD,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,KAAK,EAAEP,KAAK,GAAG;QAAK,CAAE;QACpCrB,SAAS,EAAC,sBAAsB;QAChCC,KAAK,EAAE;UACLC,eAAe,EAAES,IAAI,CAACC,SAAS,GAAG,GAAGjC,YAAY,CAACK,MAAM,IAAI,GAAGL,YAAY,CAACG;QAC9E,CAAE;QAAAqB,QAAA,gBAEFzB,OAAA;UAAKsB,SAAS,EAAC,2BAA2B;UAAAG,QAAA,EACvCQ,IAAI,CAACC,SAAS,gBACblC,OAAA,CAACT,aAAa;YAAC+B,SAAS,EAAC,SAAS;YAACC,KAAK,EAAE;cAAEG,KAAK,EAAEzB,YAAY,CAACK;YAAO;UAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5E9B,OAAA,CAACP,QAAQ;YAAC6B,SAAS,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC9C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN9B,OAAA;UAAKsB,SAAS,EAAC,QAAQ;UAAAG,QAAA,eACrBzB,OAAA;YAAGsB,SAAS,EAAC,SAAS;YAACC,KAAK,EAAE;cAC5B4B,UAAU,EAAElB,IAAI,CAACC,SAAS,GAAG,KAAK,GAAG,QAAQ;cAC7CR,KAAK,EAAEO,IAAI,CAACC,SAAS,GAAGjC,YAAY,CAACI,IAAI,GAAG;YAC9C,CAAE;YAAAoB,QAAA,EACCQ,IAAI,CAACmB;UAAW;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA,GAvBDa,KAAK;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBA,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA3EIL,kBAAkB;AAAA8C,EAAA,GAAlB9C,kBAAkB;AA6ExB,eAAeA,kBAAkB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}