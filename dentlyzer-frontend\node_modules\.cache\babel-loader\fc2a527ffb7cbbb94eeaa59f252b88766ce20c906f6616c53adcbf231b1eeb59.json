{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\dentlyzer-frontend\\\\src\\\\assistant\\\\Patients.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { useAuth } from '../context/AuthContext';\nimport Navbar from '../student/Navbar';\nimport AssistantSidebar from './AssistantSidebar';\nimport Loader from '../components/Loader';\nimport { motion } from 'framer-motion';\nimport { FaUsers, FaUserAlt, FaNotesMedical, FaCalendarAlt } from 'react-icons/fa';\n\n// Website color palette\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst websiteColorPalette = {\n  primary: '#0077B6',\n  secondary: '#20B2AA',\n  background: '#FFFFFF',\n  text: '#333333',\n  accent: '#28A745'\n};\n\n// Helper function to format appointment date and time in Cairo timezone\nconst formatAppointmentDateTime = appointment => {\n  try {\n    // Get the date from the appointment\n    const date = new Date(appointment.start || appointment.date || appointment.appointmentDate);\n\n    // Format the date in Cairo timezone (UTC+2)\n    const dateOptions = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      timeZone: 'Africa/Cairo'\n    };\n    const formattedDate = date.toLocaleDateString('en-US', dateOptions);\n\n    // Get the time from the appointment object or use the date's time\n    let timeString = appointment.time;\n\n    // If time is not available in the appointment object, format the time from the date\n    if (!timeString) {\n      const timeOptions = {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true,\n        timeZone: 'Africa/Cairo'\n      };\n      timeString = date.toLocaleTimeString('en-US', timeOptions);\n    }\n    return `${formattedDate} at ${timeString}`;\n  } catch (error) {\n    console.error('Error formatting appointment date:', error);\n    return 'Date not available';\n  }\n};\nconst Patients = () => {\n  _s();\n  var _students$find2, _patientDetails$medic, _patientDetails$medic2, _patientDetails$appoi;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [patients, setPatients] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [selectedPatient, setSelectedPatient] = useState(null);\n  const [patientDetails, setPatientDetails] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const {\n    user,\n    token\n  } = useAuth();\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !token) {\n        setError('Please log in to view data.');\n        setLoading(false);\n        return;\n      }\n      if (!user.university) {\n        setError('User profile incomplete. Missing university information.');\n        setLoading(false);\n        return;\n      }\n      try {\n        setLoading(true);\n        const config = {\n          headers: {\n            Authorization: `Bearer ${token}`\n          }\n        };\n        // Fetch patients for the assistant's university (with query param)\n        const patientsResponse = await axios.get(`http://localhost:5000/api/assistant/patients?university=${encodeURIComponent(user.university)}`, config);\n        setPatients(patientsResponse.data || []);\n        // Fetch students for mapping drId\n        const studentsResponse = await axios.get(`http://localhost:5000/api/students?university=${encodeURIComponent(user.university)}`, config);\n        setStudents(studentsResponse.data || []);\n        if ((patientsResponse.data || []).length === 0) setError('No patients found for your university.');\n      } catch (err) {\n        var _err$response;\n        setError('Failed to load patients');\n        if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 401) navigate('/login');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user, token, navigate]);\n  useEffect(() => {\n    const fetchPatientDetails = async () => {\n      if (selectedPatient && selectedPatient.nationalId) {\n        try {\n          const config = {\n            headers: {\n              Authorization: `Bearer ${token}`\n            }\n          };\n          const patientResponse = await axios.get(`http://localhost:5000/api/assistant/patients/${selectedPatient.nationalId}`, config);\n          setPatientDetails(patientResponse.data);\n        } catch (err) {\n          setError('Failed to load patient details.');\n        }\n      }\n    };\n    fetchPatientDetails();\n  }, [selectedPatient, token]);\n  const container = {\n    hidden: {\n      opacity: 0\n    },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const item = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    show: {\n      opacity: 1,\n      y: 0\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(AssistantSidebar, {\n          isOpen: sidebarOpen,\n          setIsOpen: setSidebarOpen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-8\",\n          children: /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex\",\n      children: [/*#__PURE__*/_jsxDEV(AssistantSidebar, {\n        isOpen: sidebarOpen,\n        setIsOpen: setSidebarOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-gray-900 mb-2\",\n              children: \"Patients\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Manage and view all patients in your university\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), error ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-6 py-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900\",\n                children: \"All Patients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this), patients.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overflow-x-auto\",\n              children: /*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"min-w-full divide-y divide-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  className: \"bg-gray-50\",\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"National ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"Assigned Student\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                      children: \"Actions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  className: \"bg-white divide-y divide-gray-200\",\n                  children: patients.map(patient => {\n                    var _students$find;\n                    return /*#__PURE__*/_jsxDEV(\"tr\", {\n                      className: \"hover:bg-gray-50 transition-colors duration-200\",\n                      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                        children: patient.fullName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: patient.nationalId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: ((_students$find = students.find(s => {\n                          var _patient$drId;\n                          return s._id === (((_patient$drId = patient.drId) === null || _patient$drId === void 0 ? void 0 : _patient$drId._id) || patient.drId);\n                        })) === null || _students$find === void 0 ? void 0 : _students$find.name) || 'None'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                        className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => setSelectedPatient(patient),\n                          className: \"text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200\",\n                          children: \"View Details\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 194,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 193,\n                        columnNumber: 29\n                      }, this)]\n                    }, patient._id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-12\",\n              children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-2 text-sm font-medium text-gray-900\",\n                children: \"No patients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: \"No patients found for your university.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), selectedPatient && patientDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: \"Patient Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSelectedPatient(null);\n                setPatientDetails(null);\n              },\n              className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-6 w-6\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-blue-600 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaUserAlt, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), \"Personal Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-4 rounded-lg shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Full Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1 font-medium\",\n                    children: patientDetails.fullName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"National ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: patientDetails.nationalId\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: patientDetails.phoneNumber || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Age\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1\",\n                    children: patientDetails.age || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:col-span-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Assigned Student\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900 mt-1 font-medium\",\n                    children: ((_students$find2 = students.find(s => {\n                      var _patientDetails$drId;\n                      return s._id === (((_patientDetails$drId = patientDetails.drId) === null || _patientDetails$drId === void 0 ? void 0 : _patientDetails$drId._id) || patientDetails.drId);\n                    })) === null || _students$find2 === void 0 ? void 0 : _students$find2.name) || 'None'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-blue-600 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaNotesMedical, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), \"Medical History\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-4 rounded-lg shadow-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Chief Complaint\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: ((_patientDetails$medic = patientDetails.medicalInfo) === null || _patientDetails$medic === void 0 ? void 0 : _patientDetails$medic.chiefComplaint) || 'None reported'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pt-2 border-t border-gray-100\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-500 mb-2\",\n                    children: \"Current Medications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-900\",\n                    children: ((_patientDetails$medic2 = patientDetails.medicalInfo) === null || _patientDetails$medic2 === void 0 ? void 0 : _patientDetails$medic2.currentMedications) || 'None reported'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-semibold text-blue-600 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"h-5 w-5 mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), \"Appointments\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white p-4 rounded-lg shadow-sm\",\n              children: ((_patientDetails$appoi = patientDetails.appointments) === null || _patientDetails$appoi === void 0 ? void 0 : _patientDetails$appoi.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"divide-y divide-gray-100\",\n                children: patientDetails.appointments.map((appt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"py-3 first:pt-0 last:pb-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-blue-100 rounded-full p-2 mr-3 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                        className: \"h-4 w-4 text-blue-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 303,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-between items-start\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-medium text-gray-900\",\n                          children: appt.title || appt.description || 'Appointment'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 307,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${appt.status === 'completed' ? 'bg-green-100 text-green-800' : appt.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-amber-100 text-amber-800'}`,\n                          children: appt.status ? appt.status.charAt(0).toUpperCase() + appt.status.slice(1) : 'Pending'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 308,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: [appt.date, \" at \", appt.time]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 315,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 27\n                  }, this)\n                }, appt._id || index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-6 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                  className: \"mx-auto h-10 w-10 text-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-2 text-sm text-gray-500 italic\",\n                  children: \"No appointments scheduled.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(Patients, \"zoWNeOUTT0KPX/wq3mcmTXKllMY=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Patients;\nexport default Patients;\nvar _c;\n$RefreshReg$(_c, \"Patients\");", "map": {"version": 3, "names": ["useState", "useEffect", "useNavigate", "axios", "useAuth", "<PERSON><PERSON><PERSON>", "Assistant<PERSON><PERSON><PERSON>", "Loader", "motion", "FaUsers", "FaUserAlt", "FaNotesMedical", "FaCalendarAlt", "jsxDEV", "_jsxDEV", "websiteColorPalette", "primary", "secondary", "background", "text", "accent", "formatAppointmentDateTime", "appointment", "date", "Date", "start", "appointmentDate", "dateOptions", "year", "month", "day", "timeZone", "formattedDate", "toLocaleDateString", "timeString", "time", "timeOptions", "hour", "minute", "hour12", "toLocaleTimeString", "error", "console", "Patients", "_s", "_students$find2", "_patientDetails$medic", "_patientDetails$medic2", "_patientDetails$appoi", "sidebarOpen", "setSidebarOpen", "patients", "setPatients", "students", "setStudents", "selectedPatient", "setSelectedPatient", "patientDetails", "setPatientDetails", "loading", "setLoading", "setError", "navigate", "user", "token", "fetchData", "university", "config", "headers", "Authorization", "patientsResponse", "get", "encodeURIComponent", "data", "studentsResponse", "length", "err", "_err$response", "response", "status", "fetchPatientDetails", "nationalId", "patientResponse", "container", "hidden", "opacity", "show", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "item", "y", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "setIsOpen", "map", "patient", "_students$find", "fullName", "find", "s", "_patient$drId", "_id", "drId", "name", "onClick", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "phoneNumber", "age", "_patientDetails$drId", "medicalInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentMedications", "appointments", "appt", "index", "title", "description", "char<PERSON>t", "toUpperCase", "slice", "_c", "$RefreshReg$"], "sources": ["D:/Dently<PERSON>_Final - Copy/dentlyzer-frontend/src/assistant/Patients.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport Navbar from '../student/Navbar';\r\nimport AssistantSidebar from './AssistantSidebar';\r\nimport Loader from '../components/Loader';\r\nimport { motion } from 'framer-motion';\r\nimport { FaUsers, FaUserAlt, FaNotesMedical, FaCalendarAlt } from 'react-icons/fa';\r\n\r\n// Website color palette\r\nconst websiteColorPalette = {\r\n  primary: '#0077B6',\r\n  secondary: '#20B2AA',\r\n  background: '#FFFFFF',\r\n  text: '#333333',\r\n  accent: '#28A745'\r\n};\r\n\r\n// Helper function to format appointment date and time in Cairo timezone\r\nconst formatAppointmentDateTime = (appointment) => {\r\n  try {\r\n    // Get the date from the appointment\r\n    const date = new Date(appointment.start || appointment.date || appointment.appointmentDate);\r\n\r\n    // Format the date in Cairo timezone (UTC+2)\r\n    const dateOptions = {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric',\r\n      timeZone: 'Africa/Cairo'\r\n    };\r\n    const formattedDate = date.toLocaleDateString('en-US', dateOptions);\r\n\r\n    // Get the time from the appointment object or use the date's time\r\n    let timeString = appointment.time;\r\n\r\n    // If time is not available in the appointment object, format the time from the date\r\n    if (!timeString) {\r\n      const timeOptions = {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: true,\r\n        timeZone: 'Africa/Cairo'\r\n      };\r\n      timeString = date.toLocaleTimeString('en-US', timeOptions);\r\n    }\r\n\r\n    return `${formattedDate} at ${timeString}`;\r\n  } catch (error) {\r\n    console.error('Error formatting appointment date:', error);\r\n    return 'Date not available';\r\n  }\r\n};\r\n\r\nconst Patients = () => {\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [patients, setPatients] = useState([]);\r\n  const [students, setStudents] = useState([]);\r\n  const [selectedPatient, setSelectedPatient] = useState(null);\r\n  const [patientDetails, setPatientDetails] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const navigate = useNavigate();\r\n  const { user, token } = useAuth();\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!user || !token) {\r\n        setError('Please log in to view data.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      if (!user.university) {\r\n        setError('User profile incomplete. Missing university information.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        setLoading(true);\r\n        const config = { headers: { Authorization: `Bearer ${token}` } };\r\n        // Fetch patients for the assistant's university (with query param)\r\n        const patientsResponse = await axios.get(\r\n          `http://localhost:5000/api/assistant/patients?university=${encodeURIComponent(user.university)}`,\r\n          config\r\n        );\r\n        setPatients(patientsResponse.data || []);\r\n        // Fetch students for mapping drId\r\n        const studentsResponse = await axios.get(\r\n          `http://localhost:5000/api/students?university=${encodeURIComponent(user.university)}`,\r\n          config\r\n        );\r\n        setStudents(studentsResponse.data || []);\r\n        if ((patientsResponse.data || []).length === 0) setError('No patients found for your university.');\r\n      } catch (err) {\r\n        setError('Failed to load patients');\r\n        if (err.response?.status === 401) navigate('/login');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, [user, token, navigate]);\r\n\r\n  useEffect(() => {\r\n    const fetchPatientDetails = async () => {\r\n      if (selectedPatient && selectedPatient.nationalId) {\r\n        try {\r\n          const config = { headers: { Authorization: `Bearer ${token}` } };\r\n          const patientResponse = await axios.get(\r\n            `http://localhost:5000/api/assistant/patients/${selectedPatient.nationalId}`,\r\n            config\r\n          );\r\n          setPatientDetails(patientResponse.data);\r\n        } catch (err) {\r\n          setError('Failed to load patient details.');\r\n        }\r\n      }\r\n    };\r\n    fetchPatientDetails();\r\n  }, [selectedPatient, token]);\r\n\r\n  const container = {\r\n    hidden: { opacity: 0 },\r\n    show: { opacity: 1, transition: { staggerChildren: 0.1 } },\r\n  };\r\n\r\n  const item = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    show: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <Navbar />\r\n        <div className=\"flex\">\r\n          <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n          <div className=\"flex-1 p-8\">\r\n            <Loader />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <Navbar />\r\n      <div className=\"flex\">\r\n        <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\r\n        \r\n        <div className=\"flex-1 p-8\">\r\n          <div className=\"max-w-7xl mx-auto\">\r\n            <div className=\"mb-8\">\r\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Patients</h1>\r\n              <p className=\"text-gray-600\">Manage and view all patients in your university</p>\r\n            </div>\r\n\r\n            {error ? (\r\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\r\n                <p className=\"text-red-800\">{error}</p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n                <div className=\"px-6 py-4 border-b border-gray-200\">\r\n                  <h3 className=\"text-lg font-medium text-gray-900\">All Patients</h3>\r\n                </div>\r\n                \r\n                {patients.length > 0 ? (\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200\">\r\n                      <thead className=\"bg-gray-50\">\r\n                        <tr>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">National ID</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Assigned Student</th>\r\n                          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                        {patients.map((patient) => (\r\n                          <tr key={patient._id} className=\"hover:bg-gray-50 transition-colors duration-200\">\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                              {patient.fullName}\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                              {patient.nationalId}\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                              {students.find((s) => s._id === (patient.drId?._id || patient.drId))?.name || 'None'}\r\n                            </td>\r\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\r\n                              <button\r\n                                onClick={() => setSelectedPatient(patient)}\r\n                                className=\"text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200\"\r\n                              >\r\n                                View Details\r\n                              </button>\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center py-12\">\r\n                    <FaUsers className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n                    <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No patients</h3>\r\n                    <p className=\"mt-1 text-sm text-gray-500\">No patients found for your university.</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Patient Details Modal */}\r\n      {selectedPatient && patientDetails && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\r\n          <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6 border-b border-gray-200\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h2 className=\"text-xl font-semibold text-gray-900\">Patient Details</h2>\r\n                <button\r\n                  onClick={() => {\r\n                    setSelectedPatient(null);\r\n                    setPatientDetails(null);\r\n                  }}\r\n                  className=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\r\n                >\r\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-6 space-y-6\">\r\n              <div className=\"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\">\r\n                <h4 className=\"text-lg font-semibold text-blue-600 mb-4 flex items-center\">\r\n                  <FaUserAlt className=\"h-5 w-5 mr-2 text-blue-600\" />\r\n                  Personal Information\r\n                </h4>\r\n                <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Full Name</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1 font-medium\">{patientDetails.fullName}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">National ID</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{patientDetails.nationalId}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Phone</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{patientDetails.phoneNumber || 'N/A'}</p>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Age</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1\">{patientDetails.age || 'N/A'}</p>\r\n                    </div>\r\n                    <div className=\"md:col-span-2\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500\">Assigned Student</h4>\r\n                      <p className=\"text-sm text-gray-900 mt-1 font-medium\">{students.find((s) => s._id === (patientDetails.drId?._id || patientDetails.drId))?.name || 'None'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\">\r\n                <h4 className=\"text-lg font-semibold text-blue-600 mb-4 flex items-center\">\r\n                  <FaNotesMedical className=\"h-5 w-5 mr-2 text-blue-600\" />\r\n                  Medical History\r\n                </h4>\r\n                <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Chief Complaint</h4>\r\n                      <p className=\"text-sm text-gray-900\">{patientDetails.medicalInfo?.chiefComplaint || 'None reported'}</p>\r\n                    </div>\r\n                    <div className=\"pt-2 border-t border-gray-100\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 mb-2\">Current Medications</h4>\r\n                      <p className=\"text-sm text-gray-900\">{patientDetails.medicalInfo?.currentMedications || 'None reported'}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-blue-50 p-5 rounded-xl shadow-sm border border-blue-100\">\r\n                <h4 className=\"text-lg font-semibold text-blue-600 mb-4 flex items-center\">\r\n                  <FaCalendarAlt className=\"h-5 w-5 mr-2 text-blue-600\" />\r\n                  Appointments\r\n                </h4>\r\n                <div className=\"bg-white p-4 rounded-lg shadow-sm\">\r\n                  {patientDetails.appointments?.length > 0 ? (\r\n                    <div className=\"divide-y divide-gray-100\">\r\n                      {patientDetails.appointments.map((appt, index) => (\r\n                        <div key={appt._id || index} className=\"py-3 first:pt-0 last:pb-0\">\r\n                          <div className=\"flex items-start\">\r\n                            <div className=\"bg-blue-100 rounded-full p-2 mr-3 mt-1\">\r\n                              <FaCalendarAlt className=\"h-4 w-4 text-blue-600\" />\r\n                            </div>\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"flex justify-between items-start\">\r\n                                <div className=\"font-medium text-gray-900\">{appt.title || appt.description || 'Appointment'}</div>\r\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\r\n                                  appt.status === 'completed' ? 'bg-green-100 text-green-800' :\r\n                                  appt.status === 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-amber-100 text-amber-800'\r\n                                }`}>\r\n                                  {appt.status ? appt.status.charAt(0).toUpperCase() + appt.status.slice(1) : 'Pending'}\r\n                                </span>\r\n                              </div>\r\n                              <div className=\"text-sm text-gray-600 mt-1\">\r\n                                {appt.date} at {appt.time}\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"py-6 text-center\">\r\n                      <FaCalendarAlt className=\"mx-auto h-10 w-10 text-gray-300\" />\r\n                      <p className=\"mt-2 text-sm text-gray-500 italic\">No appointments scheduled.</p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Patients; "], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAEC,aAAa,QAAQ,gBAAgB;;AAElF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,MAAMC,yBAAyB,GAAIC,WAAW,IAAK;EACjD,IAAI;IACF;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,WAAW,CAACG,KAAK,IAAIH,WAAW,CAACC,IAAI,IAAID,WAAW,CAACI,eAAe,CAAC;;IAE3F;IACA,MAAMC,WAAW,GAAG;MAClBC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAMC,aAAa,GAAGT,IAAI,CAACU,kBAAkB,CAAC,OAAO,EAAEN,WAAW,CAAC;;IAEnE;IACA,IAAIO,UAAU,GAAGZ,WAAW,CAACa,IAAI;;IAEjC;IACA,IAAI,CAACD,UAAU,EAAE;MACf,MAAME,WAAW,GAAG;QAClBC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZR,QAAQ,EAAE;MACZ,CAAC;MACDG,UAAU,GAAGX,IAAI,CAACiB,kBAAkB,CAAC,OAAO,EAAEJ,WAAW,CAAC;IAC5D;IAEA,OAAO,GAAGJ,aAAa,OAAOE,UAAU,EAAE;EAC5C,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,OAAO,oBAAoB;EAC7B;AACF,CAAC;AAED,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEoB,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM8D,QAAQ,GAAG5D,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6D,IAAI;IAAEC;EAAM,CAAC,GAAG5D,OAAO,CAAC,CAAC;EAEjCH,SAAS,CAAC,MAAM;IACd,MAAMgE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACF,IAAI,IAAI,CAACC,KAAK,EAAE;QACnBH,QAAQ,CAAC,6BAA6B,CAAC;QACvCD,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA,IAAI,CAACG,IAAI,CAACG,UAAU,EAAE;QACpBL,QAAQ,CAAC,0DAA0D,CAAC;QACpED,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA,IAAI;QACFA,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMO,MAAM,GAAG;UAAEC,OAAO,EAAE;YAAEC,aAAa,EAAE,UAAUL,KAAK;UAAG;QAAE,CAAC;QAChE;QACA,MAAMM,gBAAgB,GAAG,MAAMnE,KAAK,CAACoE,GAAG,CACtC,2DAA2DC,kBAAkB,CAACT,IAAI,CAACG,UAAU,CAAC,EAAE,EAChGC,MACF,CAAC;QACDf,WAAW,CAACkB,gBAAgB,CAACG,IAAI,IAAI,EAAE,CAAC;QACxC;QACA,MAAMC,gBAAgB,GAAG,MAAMvE,KAAK,CAACoE,GAAG,CACtC,iDAAiDC,kBAAkB,CAACT,IAAI,CAACG,UAAU,CAAC,EAAE,EACtFC,MACF,CAAC;QACDb,WAAW,CAACoB,gBAAgB,CAACD,IAAI,IAAI,EAAE,CAAC;QACxC,IAAI,CAACH,gBAAgB,CAACG,IAAI,IAAI,EAAE,EAAEE,MAAM,KAAK,CAAC,EAAEd,QAAQ,CAAC,wCAAwC,CAAC;MACpG,CAAC,CAAC,OAAOe,GAAG,EAAE;QAAA,IAAAC,aAAA;QACZhB,QAAQ,CAAC,yBAAyB,CAAC;QACnC,IAAI,EAAAgB,aAAA,GAAAD,GAAG,CAACE,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcE,MAAM,MAAK,GAAG,EAAEjB,QAAQ,CAAC,QAAQ,CAAC;MACtD,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IACDK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACF,IAAI,EAAEC,KAAK,EAAEF,QAAQ,CAAC,CAAC;EAE3B7D,SAAS,CAAC,MAAM;IACd,MAAM+E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAIzB,eAAe,IAAIA,eAAe,CAAC0B,UAAU,EAAE;QACjD,IAAI;UACF,MAAMd,MAAM,GAAG;YAAEC,OAAO,EAAE;cAAEC,aAAa,EAAE,UAAUL,KAAK;YAAG;UAAE,CAAC;UAChE,MAAMkB,eAAe,GAAG,MAAM/E,KAAK,CAACoE,GAAG,CACrC,gDAAgDhB,eAAe,CAAC0B,UAAU,EAAE,EAC5Ed,MACF,CAAC;UACDT,iBAAiB,CAACwB,eAAe,CAACT,IAAI,CAAC;QACzC,CAAC,CAAC,OAAOG,GAAG,EAAE;UACZf,QAAQ,CAAC,iCAAiC,CAAC;QAC7C;MACF;IACF,CAAC;IACDmB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACzB,eAAe,EAAES,KAAK,CAAC,CAAC;EAE5B,MAAMmB,SAAS,GAAG;IAChBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEE,UAAU,EAAE;QAAEC,eAAe,EAAE;MAAI;IAAE;EAC3D,CAAC;EAED,MAAMC,IAAI,GAAG;IACXL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,IAAI,EAAE;MAAED,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAE;EAC3B,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACE7C,OAAA;MAAK6E,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtC9E,OAAA,CAACT,MAAM;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVlF,OAAA;QAAK6E,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB9E,OAAA,CAACR,gBAAgB;UAAC2F,MAAM,EAAEhD,WAAY;UAACiD,SAAS,EAAEhD;QAAe;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpElF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzB9E,OAAA,CAACP,MAAM;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACElF,OAAA;IAAK6E,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC9E,OAAA,CAACT,MAAM;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVlF,OAAA;MAAK6E,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB9E,OAAA,CAACR,gBAAgB;QAAC2F,MAAM,EAAEhD,WAAY;QAACiD,SAAS,EAAEhD;MAAe;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpElF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAC,QAAA,eACzB9E,OAAA;UAAK6E,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9E,OAAA;YAAK6E,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9E,OAAA;cAAI6E,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnElF,OAAA;cAAG6E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,EAELvD,KAAK,gBACJ3B,OAAA;YAAK6E,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClE9E,OAAA;cAAG6E,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEnD;YAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAENlF,OAAA;YAAK6E,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnF9E,OAAA;cAAK6E,SAAS,EAAC,oCAAoC;cAAAC,QAAA,eACjD9E,OAAA;gBAAI6E,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EAEL7C,QAAQ,CAACwB,MAAM,GAAG,CAAC,gBAClB7D,OAAA;cAAK6E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B9E,OAAA;gBAAO6E,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBACpD9E,OAAA;kBAAO6E,SAAS,EAAC,YAAY;kBAAAC,QAAA,eAC3B9E,OAAA;oBAAA8E,QAAA,gBACE9E,OAAA;sBAAI6E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxGlF,OAAA;sBAAI6E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/GlF,OAAA;sBAAI6E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpHlF,OAAA;sBAAI6E,SAAS,EAAC,gFAAgF;sBAAAC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACRlF,OAAA;kBAAO6E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EACjDzC,QAAQ,CAACgD,GAAG,CAAEC,OAAO;oBAAA,IAAAC,cAAA;oBAAA,oBACpBvF,OAAA;sBAAsB6E,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,gBAC/E9E,OAAA;wBAAI6E,SAAS,EAAC,+DAA+D;wBAAAC,QAAA,EAC1EQ,OAAO,CAACE;sBAAQ;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CAAC,eACLlF,OAAA;wBAAI6E,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC9DQ,OAAO,CAACnB;sBAAU;wBAAAY,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CAAC,eACLlF,OAAA;wBAAI6E,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,EAC9D,EAAAS,cAAA,GAAAhD,QAAQ,CAACkD,IAAI,CAAEC,CAAC;0BAAA,IAAAC,aAAA;0BAAA,OAAKD,CAAC,CAACE,GAAG,MAAM,EAAAD,aAAA,GAAAL,OAAO,CAACO,IAAI,cAAAF,aAAA,uBAAZA,aAAA,CAAcC,GAAG,KAAIN,OAAO,CAACO,IAAI,CAAC;wBAAA,EAAC,cAAAN,cAAA,uBAAnEA,cAAA,CAAqEO,IAAI,KAAI;sBAAM;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClF,CAAC,eACLlF,OAAA;wBAAI6E,SAAS,EAAC,mDAAmD;wBAAAC,QAAA,eAC/D9E,OAAA;0BACE+F,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC4C,OAAO,CAAE;0BAC3CT,SAAS,EAAC,8EAA8E;0BAAAC,QAAA,EACzF;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC;oBAAA,GAjBEI,OAAO,CAACM,GAAG;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkBhB,CAAC;kBAAA,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAENlF,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA,CAACL,OAAO;gBAACkF,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDlF,OAAA;gBAAI6E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvElF,OAAA;gBAAG6E,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzC,eAAe,IAAIE,cAAc,iBAChC3C,OAAA;MAAK6E,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7F9E,OAAA;QAAK6E,SAAS,EAAC,mEAAmE;QAAAC,QAAA,gBAChF9E,OAAA;UAAK6E,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3C9E,OAAA;YAAK6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChD9E,OAAA;cAAI6E,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxElF,OAAA;cACE+F,OAAO,EAAEA,CAAA,KAAM;gBACbrD,kBAAkB,CAAC,IAAI,CAAC;gBACxBE,iBAAiB,CAAC,IAAI,CAAC;cACzB,CAAE;cACFiC,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAE5E9E,OAAA;gBAAK6E,SAAS,EAAC,SAAS;gBAACmB,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAAApB,QAAA,eAC5E9E,OAAA;kBAAMmG,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAsB;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK6E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9E,OAAA;YAAK6E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE9E,OAAA;cAAI6E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE9E,OAAA,CAACJ,SAAS;gBAACiF,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlF,OAAA;cAAK6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAChD9E,OAAA;gBAAK6E,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChElF,OAAA;oBAAG6E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAEnC,cAAc,CAAC6C;kBAAQ;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClElF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnC,cAAc,CAACwB;kBAAU;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnC,cAAc,CAAC4D,WAAW,IAAI;kBAAK;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACNlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1DlF,OAAA;oBAAG6E,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEnC,cAAc,CAAC6D,GAAG,IAAI;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACNlF,OAAA;kBAAK6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B9E,OAAA;oBAAI6E,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvElF,OAAA;oBAAG6E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAE,EAAA/C,eAAA,GAAAQ,QAAQ,CAACkD,IAAI,CAAEC,CAAC;sBAAA,IAAAe,oBAAA;sBAAA,OAAKf,CAAC,CAACE,GAAG,MAAM,EAAAa,oBAAA,GAAA9D,cAAc,CAACkD,IAAI,cAAAY,oBAAA,uBAAnBA,oBAAA,CAAqBb,GAAG,KAAIjD,cAAc,CAACkD,IAAI,CAAC;oBAAA,EAAC,cAAA9D,eAAA,uBAAjFA,eAAA,CAAmF+D,IAAI,KAAI;kBAAM;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1J,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE9E,OAAA;cAAI6E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE9E,OAAA,CAACH,cAAc;gBAACgF,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE3D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlF,OAAA;cAAK6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,eAChD9E,OAAA;gBAAK6E,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAI6E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3ElF,OAAA;oBAAG6E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE,EAAA9C,qBAAA,GAAAW,cAAc,CAAC+D,WAAW,cAAA1E,qBAAA,uBAA1BA,qBAAA,CAA4B2E,cAAc,KAAI;kBAAe;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,eACNlF,OAAA;kBAAK6E,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,gBAC5C9E,OAAA;oBAAI6E,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/ElF,OAAA;oBAAG6E,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE,EAAA7C,sBAAA,GAAAU,cAAc,CAAC+D,WAAW,cAAAzE,sBAAA,uBAA1BA,sBAAA,CAA4B2E,kBAAkB,KAAI;kBAAe;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE9E,OAAA;cAAI6E,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxE9E,OAAA,CAACF,aAAa;gBAAC+E,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlF,OAAA;cAAK6E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAC/C,EAAA5C,qBAAA,GAAAS,cAAc,CAACkE,YAAY,cAAA3E,qBAAA,uBAA3BA,qBAAA,CAA6B2B,MAAM,IAAG,CAAC,gBACtC7D,OAAA;gBAAK6E,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACtCnC,cAAc,CAACkE,YAAY,CAACxB,GAAG,CAAC,CAACyB,IAAI,EAAEC,KAAK,kBAC3C/G,OAAA;kBAA6B6E,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,eAChE9E,OAAA;oBAAK6E,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/B9E,OAAA;sBAAK6E,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,eACrD9E,OAAA,CAACF,aAAa;wBAAC+E,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACNlF,OAAA;sBAAK6E,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrB9E,OAAA;wBAAK6E,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/C9E,OAAA;0BAAK6E,SAAS,EAAC,2BAA2B;0BAAAC,QAAA,EAAEgC,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,WAAW,IAAI;wBAAa;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAClGlF,OAAA;0BAAM6E,SAAS,EAAE,2EACfiC,IAAI,CAAC7C,MAAM,KAAK,WAAW,GAAG,6BAA6B,GAC3D6C,IAAI,CAAC7C,MAAM,KAAK,WAAW,GAAG,yBAAyB,GAAG,6BAA6B,EACtF;0BAAAa,QAAA,EACAgC,IAAI,CAAC7C,MAAM,GAAG6C,IAAI,CAAC7C,MAAM,CAACiD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGL,IAAI,CAAC7C,MAAM,CAACmD,KAAK,CAAC,CAAC,CAAC,GAAG;wBAAS;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNlF,OAAA;wBAAK6E,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,GACxCgC,IAAI,CAACrG,IAAI,EAAC,MAAI,EAACqG,IAAI,CAACzF,IAAI;sBAAA;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAnBE4B,IAAI,CAAClB,GAAG,IAAImB,KAAK;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBtB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENlF,OAAA;gBAAK6E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B9E,OAAA,CAACF,aAAa;kBAAC+E,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7DlF,OAAA;kBAAG6E,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpD,EAAA,CAzRID,QAAQ;EAAA,QAQKzC,WAAW,EACJE,OAAO;AAAA;AAAA+H,EAAA,GAT3BxF,QAAQ;AA2Rd,eAAeA,QAAQ;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}