const Review = require('../models/Review');
const Student = require('../models/Student');

const submitReview = async (req, res) => {
  try {
    const { patientId, procedureType, chartId, comment, reviewSteps, note } = req.body;

    // Log the request body for debugging
    console.log('Review submission request body:', req.body);

    const student = await Student.findOne({ studentId: req.user.studentId });
    if (!student) return res.status(404).json({ message: 'Student not found' });

    // Get default review steps for the procedure type if not provided
    const steps = reviewSteps || Review.getReviewStepsByType(procedureType);

    // Create review object with all fields
    const reviewData = {
      patientId,
      studentId: student._id,
      studentName: student.name,
      procedureType,
      comment: comment || 'N/A',
      note: note || 'N/A',
      reviewSteps: steps,
    };

    // Only add chartId if it's provided
    if (chartId) {
      reviewData.chartId = chartId;
    }

    const review = new Review(reviewData);

    const savedReview = await review.save();
    res.status(201).json(savedReview);
  } catch (error) {
    console.error('Error submitting review:', error.message, error.stack);
    res.status(500).json({ message: 'Server error: ' + error.message });
  }
};

const getReviewById = async (req, res) => {
  try {
    const review = await Review.findById(req.params.id).populate('studentId', 'name').populate('supervisorId', 'name');
    if (!review) return res.status(404).json({ message: 'Review not found' });
    res.json(review);
  } catch (error) {
    console.error('Error fetching review:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getStudentReviews = async (req, res) => {
  try {
    console.log('getStudentReviews called with query:', req.query);
    console.log('User info:', req.user);

    // If studentId is provided in query params, fetch reviews for that student
    if (req.query.studentId) {
      console.log(`Looking for student with studentId: ${req.query.studentId}`);
      const student = await Student.findOne({ studentId: req.query.studentId });

      if (!student) {
        console.log(`Student not found with studentId: ${req.query.studentId}`);
        return res.status(404).json({ message: 'Student not found' });
      }

      console.log(`Found student:`, student);

      // Verify the requesting user is the same student or a supervisor/admin
      if (req.user.role === 'student' && req.user.studentId !== req.query.studentId) {
        console.log(`Access denied: User ${req.user.studentId} tried to access reviews for ${req.query.studentId}`);
        return res.status(403).json({ message: 'Access denied' });
      }

      console.log(`Looking for reviews with studentId: ${student._id}`);
      const reviews = await Review.find({ studentId: student._id })
        .populate('studentId', 'name studentId')
        .populate('supervisorId', 'name')
        .sort({ submittedDate: -1 });

      console.log(`Found ${reviews.length} reviews for student ${req.query.studentId}`);
      return res.json(reviews);
    }

    // Original functionality for supervisors
    console.log(`Looking for reviews with supervisorId: ${req.user._id}`);
    const reviews = await Review.find({ supervisorId: req.user._id }).populate('studentId', 'name');
    console.log(`Found ${reviews.length} reviews for supervisor ${req.user._id}`);
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching student reviews:', error.message);
    console.error('Error stack:', error.stack);
    console.error('Error details:', error);
    res.status(500).json({ message: 'Server error: ' + error.message });
  }
};

const getPendingReviews = async (req, res) => {
  try {
    // If supervisorId is not assigned yet, fetch all pending reviews for the university
    // This allows supervisors to see all pending reviews that need to be assigned
    const query = req.user.university ?
      {
        status: 'pending',
        $or: [
          { supervisorId: req.user._id },
          { supervisorId: null, 'studentId.university': req.user.university }
        ]
      } :
      { status: 'pending', supervisorId: req.user._id };

    const reviews = await Review.find(query)
      .populate('studentId', 'name studentId university')
      .sort({ submittedDate: -1 });

    res.json(reviews);
  } catch (error) {
    console.error('Error fetching pending reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const updateReview = async (req, res) => {
  try {
    const { procedureQuality, patientInteraction, status, note, supervisorSignature, stepStatuses } = req.body;
    const review = await Review.findById(req.params.reviewId);
    if (!review) return res.status(404).json({ message: 'Review not found' });

    // If the review already has a supervisor assigned, verify it's the current user
    if (review.supervisorId && review.supervisorId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied: This review is assigned to another supervisor' });
    }

    console.log('Updating review with supervisor info:', {
      supervisorId: req.user._id,
      supervisorName: req.user.name || 'Unknown Supervisor'
    });

    // Update review fields
    review.procedureQuality = procedureQuality || review.procedureQuality;
    review.patientInteraction = patientInteraction || review.patientInteraction;
    review.status = status || review.status;
    review.note = note || review.note;
    review.supervisorId = req.user._id;

    // Ensure supervisor name is set and not N/A
    review.supervisorName = req.user.name || 'Unknown Supervisor';

    review.reviewedDate = new Date();

    // Only update signature if provided
    if (supervisorSignature) {
      review.supervisorSignature = supervisorSignature;
    }

    // Update step statuses if provided
    if (stepStatuses && typeof stepStatuses === 'object') {
      review.stepStatuses = stepStatuses;
      
      // Update the reviewSteps with supervisor decisions
      if (review.reviewSteps && Array.isArray(review.reviewSteps)) {
        review.reviewSteps = review.reviewSteps.map((step, index) => ({
          ...step,
          supervisorStatus: stepStatuses[index] || 'pending'
        }));
      }
    }

    const updatedReview = await review.save();
    console.log('Review updated successfully with supervisor name:', updatedReview.supervisorName);
    res.json(updatedReview);
  } catch (error) {
    console.error('Error updating review:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getAllReviews = async (req, res) => {
  try {
    if (!['admin', 'superadmin'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { university } = req.query;
    if (!university && req.user.role !== 'superadmin') {
      return res.status(400).json({ message: 'University query parameter required' });
    }

    const query = university
      ? { 'studentId.university': university }
      : req.user.role === 'superadmin'
      ? {}
      : { 'studentId.university': req.user.university };

    const reviews = await Review.find(query)
      .populate('studentId', 'name university')
      .populate('supervisorId', 'name')
      .sort({ submittedDate: -1 });

    console.log(`Fetched ${reviews.length} reviews${university ? ` for university: ${university}` : ''}`);
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching all reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getReviewStepsByType = async (req, res) => {
  try {
    const { procedureType } = req.params;
    const { subType } = req.query;

    if (!procedureType) {
      return res.status(400).json({ message: 'Procedure type is required' });
    }

    // Pass the subType parameter to the model method
    const steps = Review.getReviewStepsByType(procedureType, subType);
    res.json(steps);
  } catch (error) {
    console.error('Error fetching review steps:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const updateReviewSteps = async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { reviewSteps, note } = req.body;

    const review = await Review.findById(reviewId);
    if (!review) return res.status(404).json({ message: 'Review not found' });

    // Verify the student owns this review
    if (review.studentId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    review.reviewSteps = reviewSteps;
    if (note) review.note = note;

    const updatedReview = await review.save();
    res.json(updatedReview);
  } catch (error) {
    console.error('Error updating review steps:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getDoneReviews = async (req, res) => {
  try {
    const query = req.user.university ?
      {
        status: { $ne: 'pending' },
        $or: [
          { supervisorId: req.user._id },
          { 'studentId.university': req.user.university }
        ]
      } :
      { status: { $ne: 'pending' }, supervisorId: req.user._id };

    const reviews = await Review.find(query)
      .populate('studentId', 'name studentId university')
      .sort({ reviewedDate: -1 });

    res.json(reviews);
  } catch (error) {
    console.error('Error fetching done reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getSupervisorReviews = async (req, res) => {
  try {
    // Extract filter parameters from query
    const {
      status,
      procedureType,
      studentId,
      startDate,
      endDate,
      search
    } = req.query;

    console.log('Supervisor reviews query params:', req.query);

    // Base query - get all reviews for this supervisor or for their university
    // Exclude signature storage reviews
    let query = req.user.university ?
      {
        $or: [
          { supervisorId: req.user._id },
          { 'studentId.university': req.user.university }
        ],
        procedureType: { $ne: 'Signature Storage' },
        'patientId.nationalId': { $ne: 'signature-storage' }
      } :
      {
        supervisorId: req.user._id,
        procedureType: { $ne: 'Signature Storage' },
        'patientId.nationalId': { $ne: 'signature-storage' }
      };

    // Add filters if provided
    if (status) {
      query.status = status;
    }

    if (procedureType) {
      query.procedureType = procedureType;
    }

    if (studentId) {
      // Find the student by studentId
      const student = await Student.findOne({ studentId });
      if (student) {
        query.studentId = student._id;
      }
    }

    // Date range filter
    if (startDate || endDate) {
      query.submittedDate = {};
      if (startDate) {
        query.submittedDate.$gte = new Date(startDate);
      }
      if (endDate) {
        query.submittedDate.$lte = new Date(endDate);
      }
    }

    // Search by patient name or student name
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { 'patientId.fullName': searchRegex },
        { studentName: searchRegex }
      ];
    }

    console.log('Final query:', JSON.stringify(query));

    const reviews = await Review.find(query)
      .populate('studentId', 'name studentId university')
      .sort({ submittedDate: -1 });

    console.log(`Found ${reviews.length} reviews for supervisor`);
    res.json(reviews);
  } catch (error) {
    console.error('Error fetching supervisor reviews:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getSupervisorSignature = async (req, res) => {
  try {
    // Find the most recent review with a signature by this supervisor
    const review = await Review.findOne(
      { supervisorId: req.user._id, supervisorSignature: { $ne: null } },
      { supervisorSignature: 1 }
    ).sort({ reviewedDate: -1 });

    if (review && review.supervisorSignature) {
      return res.json({ signature: review.supervisorSignature });
    }

    res.json({ signature: null });
  } catch (error) {
    console.error('Error fetching supervisor signature:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const saveSupervisorSignature = async (req, res) => {
  try {
    const { supervisorSignature } = req.body;

    if (!supervisorSignature) {
      return res.status(400).json({ message: 'Signature is required' });
    }

    console.log('Saving supervisor signature for user:', req.user);

    // First, try to find an existing review by this supervisor
    let existingReview = await Review.findOne({
      supervisorId: req.user._id,
      'patientId.nationalId': 'signature-storage'
    });

    if (existingReview) {
      // Update the existing review with the new signature
      console.log('Updating existing signature review');
      existingReview.supervisorSignature = supervisorSignature;
      existingReview.reviewedDate = new Date();
      await existingReview.save();
    } else {
      // Find any existing review to get a valid studentId
      console.log('Looking for any existing review to use for signature storage');
      const anyReview = await Review.findOne({}, { studentId: 1 });

      if (!anyReview) {
        console.error('No existing reviews found to use for signature storage');
        return res.status(500).json({ message: 'Cannot save signature: No reviews exist in the system' });
      }

      console.log('Creating new signature review with studentId from existing review');
      // Create a dummy review to store the signature using a valid studentId
      const dummyReview = new Review({
        patientId: { nationalId: 'signature-storage', fullName: 'Signature Storage' },
        studentId: anyReview.studentId, // Using an existing valid studentId
        studentName: 'Signature Storage',
        supervisorId: req.user._id,
        supervisorName: req.user.name,
        supervisorSignature: supervisorSignature,
        procedureType: 'Signature Storage',
        status: 'accepted',
        reviewSteps: [],
        reviewedDate: new Date()
      });

      await dummyReview.save();
    }

    res.json({ message: 'Signature saved successfully', signature: supervisorSignature });
  } catch (error) {
    console.error('Error saving supervisor signature:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

const getSupervisorAnalytics = async (req, res) => {
  try {
    // Extract time range from query params
    const { timeRange = 'all' } = req.query;
    console.log('Analytics request for supervisor:', req.user._id, 'timeRange:', timeRange);

    // Determine date range based on timeRange
    let startDate = new Date(0); // Default to beginning of time
    const endDate = new Date(); // Current date

    if (timeRange === 'week') {
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
    } else if (timeRange === 'month') {
      startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);
    } else if (timeRange === 'year') {
      startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1);
    }

    console.log('Date range:', { startDate, endDate });

    // Base query - get all reviews for this supervisor or for their university
    // Exclude signature storage reviews
    let query;
    if (req.user.university) {
      query = {
        submittedDate: { $gte: startDate, $lte: endDate },
        $or: [
          { supervisorId: req.user._id },
          { 'studentId.university': req.user.university }
        ],
        procedureType: { $ne: 'Signature Storage' },
        'patientId.nationalId': { $ne: 'signature-storage' }
      };
    } else {
      query = {
        submittedDate: { $gte: startDate, $lte: endDate },
        supervisorId: req.user._id,
        procedureType: { $ne: 'Signature Storage' },
        'patientId.nationalId': { $ne: 'signature-storage' }
      };
    }

    console.log('Query:', JSON.stringify(query, null, 2));

    // Fetch all reviews within the time range
    const reviews = await Review.find(query)
      .populate('studentId', 'name studentId university')
      .sort({ submittedDate: -1 });

    console.log('Found', reviews.length, 'reviews for analytics');

    // Calculate analytics
    const analytics = {
      totalReviews: reviews.length,
      statusDistribution: {
        pending: reviews.filter(r => r.status === 'pending').length,
        accepted: reviews.filter(r => r.status === 'accepted').length,
        denied: reviews.filter(r => r.status === 'denied').length
      },
      procedureTypeDistribution: {},
      studentPerformance: {},
      reviewTrends: [],
      qualityMetrics: {
        avgProcedureQuality: 0,
        avgPatientInteraction: 0
      }
    };

    console.log('Status distribution:', analytics.statusDistribution);

    // Calculate procedure type distribution
    reviews.forEach(review => {
      const type = review.procedureType || 'Unknown';
      if (!analytics.procedureTypeDistribution[type]) {
        analytics.procedureTypeDistribution[type] = {
          total: 0,
          accepted: 0,
          denied: 0,
          pending: 0
        };
      }
      analytics.procedureTypeDistribution[type].total++;
      if (review.status === 'accepted') analytics.procedureTypeDistribution[type].accepted++;
      else if (review.status === 'denied') analytics.procedureTypeDistribution[type].denied++;
      else analytics.procedureTypeDistribution[type].pending++;
    });

    console.log('Procedure types found:', Object.keys(analytics.procedureTypeDistribution));

    // Calculate student performance
    reviews.forEach(review => {
      const studentName = review.studentName || review.studentId?.name || 'Unknown';
      if (!analytics.studentPerformance[studentName]) {
        analytics.studentPerformance[studentName] = {
          total: 0,
          accepted: 0,
          denied: 0,
          pending: 0,
          avgProcedureQuality: 0,
          avgPatientInteraction: 0,
          qualityRatings: [],
          interactionRatings: []
        };
      }

      analytics.studentPerformance[studentName].total++;
      if (review.status === 'accepted') analytics.studentPerformance[studentName].accepted++;
      else if (review.status === 'denied') analytics.studentPerformance[studentName].denied++;
      else analytics.studentPerformance[studentName].pending++;

      if (review.procedureQuality) {
        analytics.studentPerformance[studentName].qualityRatings.push(review.procedureQuality);
      }

      if (review.patientInteraction) {
        analytics.studentPerformance[studentName].interactionRatings.push(review.patientInteraction);
      }
    });

    // Calculate averages for each student
    Object.keys(analytics.studentPerformance).forEach(student => {
      const { qualityRatings, interactionRatings } = analytics.studentPerformance[student];

      if (qualityRatings.length > 0) {
        analytics.studentPerformance[student].avgProcedureQuality =
          (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);
      }

      if (interactionRatings.length > 0) {
        analytics.studentPerformance[student].avgPatientInteraction =
          (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);
      }

      // Remove the arrays from the response
      delete analytics.studentPerformance[student].qualityRatings;
      delete analytics.studentPerformance[student].interactionRatings;
    });

    console.log('Students found:', Object.keys(analytics.studentPerformance));

    // Calculate review trends by month
    const reviewsByMonth = {};
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    reviews.forEach(review => {
      const date = new Date(review.submittedDate);
      if (date >= sixMonthsAgo) {
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;
        if (!reviewsByMonth[monthYear]) {
          reviewsByMonth[monthYear] = { total: 0, accepted: 0, denied: 0, pending: 0 };
        }
        reviewsByMonth[monthYear].total++;
        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;
        else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;
        else reviewsByMonth[monthYear].pending++;
      }
    });

    // Convert to array and sort by date
    analytics.reviewTrends = Object.keys(reviewsByMonth).map(month => ({
      month,
      ...reviewsByMonth[month]
    })).sort((a, b) => {
      const [aMonth, aYear] = a.month.split('/').map(Number);
      const [bMonth, bYear] = b.month.split('/').map(Number);
      return aYear === bYear ? aMonth - bMonth : aYear - bYear;
    });

    console.log('Trends calculated for', analytics.reviewTrends.length, 'months');

    // Calculate quality metrics
    const doneReviews = reviews.filter(r => r.status !== 'pending');
    if (doneReviews.length > 0) {
      analytics.qualityMetrics.avgProcedureQuality =
        (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1);
      analytics.qualityMetrics.avgPatientInteraction =
        (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1);
    }

    console.log('Quality metrics:', analytics.qualityMetrics);
    console.log('Sending analytics response');

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching supervisor analytics:', error.message, error.stack);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  submitReview,
  getReviewById,
  getStudentReviews,
  getPendingReviews,
  getDoneReviews,
  updateReview,
  getAllReviews,
  getReviewStepsByType,
  updateReviewSteps,
  getSupervisorReviews,
  getSupervisorSignature,
  saveSupervisorSignature,
  getSupervisorAnalytics,
};