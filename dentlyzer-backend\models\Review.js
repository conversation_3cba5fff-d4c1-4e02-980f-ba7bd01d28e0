const mongoose = require('mongoose');

// Define review step schema
const ReviewStepSchema = new mongoose.Schema({
  description: { type: String, required: true },
  completed: { type: Boolean, default: false }
}, { _id: false });

// Define periodontics review steps
const perioReviewSteps = [
  { description: 'Case assessment & charting', completed: false },
  { description: 'Oral hygiene instructions', completed: false },
  { description: 'Instrument selection & setup', completed: false },
  { description: 'Supragingival scaling', completed: false },
  { description: 'Subgingival scaling (if needed)', completed: false }
];

// Define endodontics review steps
const endodonticsReviewSteps = [
  { description: 'Diagnosis & case selection (pulpal & periapical)', completed: false },
  { description: 'Pre-op radiographs & photographs', completed: false },
  { description: 'Anesthesia & rubber dam isolation', completed: false },
  { description: 'Access cavity preparation', completed: false },
  { description: 'Canal location & patency check', completed: false },
  { description: 'Working length determination (radiographic/apex locator)', completed: false },
  { description: 'Canal preparation (cleaning & shaping)', completed: false },
  { description: 'Irrigation (type, sequence, activation if applicable)', completed: false },
  { description: 'Master cone fit & confirmation radiograph', completed: false },
  { description: 'Obturation technique (e.g., lateral/vertical compaction)', completed: false },
  { description: 'Post-obturation radiograph', completed: false },
  { description: 'Coronal seal (temporary/permanent restoration)', completed: false }
];

// Define removable prosthodontics review steps for Complete Denture
const removableCompleteReviewSteps = [
  { description: 'Diagnosis & case selection', completed: false },
  { description: 'Pre-op photographs & radiographs', completed: false },
  { description: 'Primary impressions', completed: false },
  { description: 'Custom tray fabrication', completed: false },
  { description: 'Final impressions', completed: false },
  { description: 'Jaw relation records', completed: false },
  { description: 'Facebow transfer (if needed)', completed: false },
  { description: 'Mounting on articulator', completed: false },
  { description: 'Teeth selection', completed: false },
  { description: 'Wax trial denture try-in', completed: false },
  { description: 'Esthetics, phonetics & occlusion check', completed: false },
  { description: 'Denture processing', completed: false },
  { description: 'Final denture insertion', completed: false },
  { description: 'Occlusal adjustments', completed: false },
  { description: 'Post-insertion follow-up & evaluation', completed: false }
];

// Define removable prosthodontics review steps for Partial Denture
const removablePartialReviewSteps = [
  { description: 'Diagnosis & case selection', completed: false },
  { description: 'Pre-op photographs & radiographs', completed: false },
  { description: 'Primary impressions', completed: false },
  { description: 'Surveying & design planning', completed: false },
  { description: 'Mouth preparation (rest seats, guide planes)', completed: false },
  { description: 'Final impressions', completed: false },
  { description: 'Master cast pouring & articulation', completed: false },
  { description: 'Framework try-in', completed: false },
  { description: 'Jaw relation records', completed: false },
  { description: 'Teeth selection & setup', completed: false },
  { description: 'Wax try-in', completed: false },
  { description: 'Processing of denture', completed: false },
  { description: 'Final insertion', completed: false },
  { description: 'Occlusal adjustments', completed: false },
  { description: 'Post-insertion follow-up & evaluation', completed: false }
];

// Default removable prosthodontics steps (for backward compatibility)
const removableReviewSteps = removableCompleteReviewSteps;

// Define operative amalgam restoration steps
const operativeAmalgamReviewSteps = [
  { description: 'Diagnosis & isolation', completed: false },
  { description: 'Cavity preparation', completed: false },
  { description: 'Matrix & wedge placement', completed: false },
  { description: 'Amalgam mixing & condensation', completed: false },
  { description: 'Carving & occlusal check', completed: false },
  { description: 'Finishing & polishing', completed: false }
];

// Define operative composite restoration steps
const operativeCompositeReviewSteps = [
  { description: 'Diagnosis & shade selection', completed: false },
  { description: 'Isolation', completed: false },
  { description: 'Cavity preparation', completed: false },
  { description: 'Etching, bonding', completed: false },
  { description: 'Composite placement & layering', completed: false },
  { description: 'Contouring, finishing & polishing', completed: false },
  { description: 'Final occlusal check', completed: false }
];

// Default operative steps (for backward compatibility)
const operativeReviewSteps = operativeAmalgamReviewSteps;

// Define fixed prosthodontics crown procedure steps
const fixedCrownReviewSteps = [
  { description: 'Diagnosis & case selection', completed: false },
  { description: 'Pre-op photographs', completed: false },
  { description: 'Primary impressions', completed: false },
  { description: 'Mounted diagnostic casts', completed: false },
  { description: 'Treatment plan & crown selection', completed: false },
  { description: 'Tooth preparation', completed: false },
  { description: 'Provisional crown', completed: false },
  { description: 'Final impression', completed: false },
  { description: 'Working cast & die trimming', completed: false },
  { description: 'Crown try-in (fit, contact, esthetics)', completed: false },
  { description: 'Cementation', completed: false },
  { description: 'Post-op photos & evaluation', completed: false }
];

// Define fixed prosthodontics bridge procedure steps
const fixedBridgeReviewSteps = [
  { description: 'Diagnosis & abutment evaluation', completed: false },
  { description: 'Pre-op photographs', completed: false },
  { description: 'Primary impressions', completed: false },
  { description: 'Mounted diagnostic casts & wax-up', completed: false },
  { description: 'Treatment planning (design, materials)', completed: false },
  { description: 'Abutment tooth preparation', completed: false },
  { description: 'Provisional bridge', completed: false },
  { description: 'Final impression', completed: false },
  { description: 'Working cast & articulation', completed: false },
  { description: 'Framework try-in', completed: false },
  { description: 'Esthetic try-in (if needed)', completed: false },
  { description: 'Cementation', completed: false },
  { description: 'Post-op evaluation', completed: false }
];

// Default fixed prosthodontics steps (for backward compatibility)
const fixedReviewSteps = fixedCrownReviewSteps;

// Oral Surgery has been removed

const ReviewSchema = new mongoose.Schema({
  patientId: {
    nationalId: { type: String, required: true },
    fullName: { type: String, required: true },
  },
  studentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Student', required: true },
  studentName: { type: String, required: true }, // Populated from student data
  supervisorId: { type: mongoose.Schema.Types.ObjectId, ref: 'Supervisor', default: null },
  supervisorName: { type: String, default: null }, // Populated when reviewed
  supervisorSignature: { type: String, default: null }, // Signature of the supervisor
  procedureType: { type: String, required: true },
  chartId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DentalChart',
    required: false,
  },
  procedureQuality: { type: Number, min: 0, max: 5, default: 0 }, // Set by supervisor
  patientInteraction: { type: Number, min: 0, max: 5, default: 0 }, // Set by supervisor
  status: { type: String, enum: ['pending', 'accepted', 'denied'], default: 'pending' },
  comment: { type: String, default: 'N/A' },
  note: { type: String, default: 'N/A' },
  reviewSteps: [ReviewStepSchema],
  stepStatuses: { type: Map, of: String, default: {} }, // Store supervisor decisions for each step
  submittedDate: { type: Date, default: Date.now },
  reviewedDate: { type: Date },
}, { timestamps: true });

// Static method to get review steps based on procedure type
ReviewSchema.statics.getReviewStepsByType = function(procedureType, subType) {
  switch(procedureType) {
    case 'Periodontics':
      return perioReviewSteps;
    case 'Operative':
      // Check if a subType is specified
      if (subType === 'Amalgam Restoration') {
        return operativeAmalgamReviewSteps;
      } else if (subType === 'Composite Restoration') {
        return operativeCompositeReviewSteps;
      }
      // Default to amalgam restoration if no subType is specified
      return operativeAmalgamReviewSteps;
    case 'Fixed Prosthodontics':
      // Check if a subType is specified
      if (subType === 'Crown Procedure') {
        return fixedCrownReviewSteps;
      } else if (subType === 'Bridge Procedure') {
        return fixedBridgeReviewSteps;
      }
      // Default to crown procedure if no subType is specified
      return fixedCrownReviewSteps;
    case 'Removable Prosthodontics':
      // Check if a subType is specified
      if (subType === 'Complete Denture') {
        return removableCompleteReviewSteps;
      } else if (subType === 'Partial Denture') {
        return removablePartialReviewSteps;
      }
      // Default to complete denture if no subType is specified
      return removableCompleteReviewSteps;
    case 'Endodontics':
      return endodonticsReviewSteps;
    default:
      return [];
  }
};

module.exports = mongoose.model('Review', ReviewSchema);